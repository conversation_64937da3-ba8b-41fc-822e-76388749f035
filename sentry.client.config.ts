// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';
import { replayIntegration } from '@sentry/nextjs';

// Only initialize Sentry in production and when not in debug mode
if (
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'production' &&
  process.env.NEXT_PUBLIC_APP_CHECK_DEBUG_MODE !== 'true'
) {
  Sentry.init({
    dsn: 'https://<EMAIL>/4508805606277120',
    integrations: [
      Sentry.replayIntegration(),
      replayIntegration({
        maskAllText: false, // UNMASK all text
        maskAllInputs: false, // UNMASK all inputs
      }),
    ],
    tracesSampleRate: 1,
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    debug: false,
  });
}
