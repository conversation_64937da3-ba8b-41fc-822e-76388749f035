# Settings for Backend (on Cloud Run).
# See https://firebase.google.com/docs/app-hosting/configure#cloud-run
runConfig:
  minInstances: 0
  maxInstances: 100
  concurrency: 80
  cpu: 1
  memoryMiB: 512

# Environment variables and secrets.
env:
  # <PERSON> access to secrets in Cloud Secret Manager.
  # See https://firebase.google.com/docs/app-hosting/configure#secret-parameters
  - variable: NEXT_PUBLIC_FIREBASE_API_KEY
    secret: NEXT_PUBLIC_FIREBASE_API_KEY
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
    secret: NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_PROJECT_ID
    secret: NEXT_PUBLIC_FIREBASE_PROJECT_ID
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
    secret: NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
    secret: NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_FIREBASE_APP_ID
    secret: NEXT_PUBLIC_FIREBASE_APP_ID
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_SQUARE_ACCESS_TOKEN
    secret: NEXT_SQUARE_ACCESS_TOKEN
    availability:
      - RUNTIME
  - variable: SENTRY_AUTH_TOKEN
    secret: SENTRY_AUTH_TOKEN
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_BASE_URL
    secret: NEXT_PUBLIC_BASE_URL
    availability:
      - RUNTIME
      - BUILD
  - variable: FIREBASE_CLIENT_EMAIL
    secret: FIREBASE_CLIENT_EMAIL
    availability:
      - BUILD
      - RUNTIME
  - variable: SENDGRID_MAGIC_LINK_TEMPLATE_ID
    secret: SENDGRID_MAGIC_LINK_TEMPLATE_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: SENDGRID_FROM_EMAIL
    secret: SENDGRID_FROM_EMAIL
    availability:
      - RUNTIME
      - BUILD
  - variable: SENDGRID_FROM_NAME
    secret: SENDGRID_FROM_NAME
    availability:
      - RUNTIME
      - BUILD
  - variable: SENDGRID_API_KEY
    secret: SENDGRID_API_KEY
    availability:
      - RUNTIME
      - BUILD
  - variable: FIREBASE_PRIVATE_KEY
    secret: FIREBASE_PRIVATE_KEY
    availability:
      - RUNTIME
      - BUILD
  - variable: FIREBASE_PRIVATE_BUCKET
    secret: FIREBASE_PRIVATE_BUCKET
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_CHECKOUT_STORE_URL
    secret: NEXT_PUBLIC_CHECKOUT_STORE_URL
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_ADULT_CATALOG_ID
    secret: NEXT_ADULT_CATALOG_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_CHILD_CATALOG_ID
    secret: NEXT_CHILD_CATALOG_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_SQUARE_LOCATION_ID
    secret: NEXT_SQUARE_LOCATION_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_SQUARE_ENVIRONMENT
    secret: NEXT_SQUARE_ENVIRONMENT
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_SQUARE_SUBSCRIPTION_PRODUCT_ID
    secret: NEXT_SQUARE_SUBSCRIPTION_PRODUCT_ID
    availability:
      - RUNTIME
  - variable: NEXT_SQUARE_SUBSCRIPTION_PRODUCT_VARIATION_ID
    secret: NEXT_SQUARE_SUBSCRIPTION_PRODUCT_VARIATION_ID
    availability:
      - RUNTIME
  - variable: NEXT_SQUARE_SUBSCRIPTION_PLAN_VARIATION_ID
    secret: NEXT_SQUARE_SUBSCRIPTION_PLAN_VARIATION_ID
    availability:
      - RUNTIME
  - variable: NEXT_SQUARE_SUBSCRIPTION_TWO_PHASE_PLAN_VARIATION_ID
    secret: NEXT_SQUARE_SUBSCRIPTION_TWO_PHASE_PLAN_VARIATION_ID
    availability:
      - RUNTIME
  - variable: NEXT_PUBLIC_SQUARE_LOCATION_ID
    secret: NEXT_PUBLIC_SQUARE_LOCATION_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_SQUARE_APP_ID
    secret: NEXT_PUBLIC_SQUARE_APP_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID
    secret: NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID
    availability:
      - RUNTIME
  - variable: TICKET_TAILOR_API_KEY
    secret: TICKET_TAILOR_API_KEY
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
    secret: NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_NEW_ZONE_REQUEST_URL
    secret: NEXT_PUBLIC_NEW_ZONE_REQUEST_URL
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_FARM_VISIT_INSTRUCTIONS_URL
    secret: NEXT_PUBLIC_FARM_VISIT_INSTRUCTIONS_URL
    availability:
      - RUNTIME
      - BUILD
  - variable: SENDGRID_WAIVER_NOTIFICATION_TEMPLATE_ID
    secret: SENDGRID_WAIVER_NOTIFICATION_TEMPLATE_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_MY_VERYMULBERRY_LINK
    secret: NEXT_PUBLIC_MY_VERYMULBERRY_LINK
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_MY_VERYMULBERRY_LINK_LABEL
    secret: NEXT_PUBLIC_MY_VERYMULBERRY_LINK_LABEL
    availability:
      - RUNTIME
      - BUILD
  - variable: SENDGRID_SUBSCRIPTION_CONFIRMATION_TEMPLATE_ID
    secret: SENDGRID_SUBSCRIPTION_CONFIRMATION_TEMPLATE_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_PRODUCT_ID
    secret: NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_PRODUCT_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_RECAPTCHA_ENTERPRISE_SITE_KEY
    secret: NEXT_PUBLIC_RECAPTCHA_ENTERPRISE_SITE_KEY
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_APP_CHECK_DEBUG_MODE
    value: NEXT_PUBLIC_APP_CHECK_DEBUG_MODE
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_TICKET_TAILOR_EVENT
    secret: NEXT_PUBLIC_TICKET_TAILOR_EVENT
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_TICKET_TYPES
    secret: NEXT_PUBLIC_TICKET_TYPES
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_WEBSITE_BASE_URL
    secret: NEXT_PUBLIC_WEBSITE_BASE_URL
    availability:
      - RUNTIME
      - BUILD
  - variable: SENDGRID_SUBSCRIPTION_RECIPIENT_TEMPLATE_ID
    secret: SENDGRID_SUBSCRIPTION_RECIPIENT_TEMPLATE_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: SENDGRID_SUBSCRIPTION_GIVER_TEMPLATE_ID
    secret: SENDGRID_SUBSCRIPTION_GIVER_TEMPLATE_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_JWT_SECRET_KEY
    secret: NEXT_JWT_SECRET_KEY
    availability:
      - RUNTIME
      - BUILD
  # Variables for Anonymous Self-Checkout
  - variable: SELF_CHECKOUT_ANONYMOUS_JWT_SECRET
    secret: SELF_CHECKOUT_ANONYMOUS_JWT_SECRET
    availability:
      - RUNTIME
  - variable: SELF_CHECKOUT_CLAMSHELL_VARIATION_ID
    secret: SELF_CHECKOUT_CLAMSHELL_VARIATION_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_CLAMSHELL_PURCHASE_LIMIT
    secret: SELF_CHECKOUT_CLAMSHELL_PURCHASE_LIMIT
    availability:
      - RUNTIME
      - BUILD
  # Aliases for existing Square variables to match self-checkout naming
  - variable: SELF_CHECKOUT_SQUARE_ACCESS_TOKEN
    secret: SELF_CHECKOUT_SQUARE_ACCESS_TOKEN
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_SQUARE_ENVIRONMENT
    secret: SELF_CHECKOUT_SQUARE_ENVIRONMENT
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_SQUARE_LOCATION_ID
    secret: SELF_CHECKOUT_SQUARE_LOCATION_ID
    availability:
      - RUNTIME
      - BUILD
  # Custom Attributes for Self-Checkout
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_CLAMSHELL_BALANCE
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_CLAMSHELL_BALANCE
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_FLOW
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_FLOW
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_ATTEMPT_ID
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_ATTEMPT_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_MINT_STATUS
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_MINT_STATUS
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_ORDER_MINTED_TOKENS
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_ORDER_MINTED_TOKENS
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_BURNED_TOKENS
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_BURNED_TOKENS
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: SELF_CHECKOUT_ATTR_DEF_KEY_BURN_DATE
    secret: SELF_CHECKOUT_ATTR_DEF_KEY_BURN_DATE
    availability:
      - RUNTIME
      - BUILD
  # Variables for Dynamic Catalog Self-Checkout
  - variable: NEXT_PUBLIC_SELF_CHECKOUT_CATEGORY_ID
    secret: NEXT_PUBLIC_SELF_CHECKOUT_CATEGORY_ID
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_PUBLIC_SQUARE_ITEM_VARIATION_TOKEN_TYPE_ATTR_KEY
    secret: NEXT_PUBLIC_SQUARE_ITEM_VARIATION_TOKEN_TYPE_ATTR_KEY
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_ALLOWED_ORIGINS
    secret: NEXT_ALLOWED_ORIGINS
    availability:
      - RUNTIME
      - BUILD
  - variable: NEXT_ACTIVE_EVENTS
    secret: NEXT_ACTIVE_EVENTS
    availability:
      - RUNTIME
      - BUILD
