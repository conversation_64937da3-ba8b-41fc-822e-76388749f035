import { DocumentReference, Timestamp } from 'firebase/firestore';

import { Events } from './Events';
import { EventSchedules } from './EventSchedules';
import { Users } from './Users';

/**
 * Orders resource
 * @description Represents a transaction record for purchases made within the system, including tickets and gifts.
 * @property id - The unique identifier for this order
 * @property order_by - Reference to the user who placed the order
 * @property square_order_id - External reference ID from Square payment system
 * @property item_type - The type of item being ordered
 *   | Value    | Description                          |
 *   |----------|--------------------------------------|
 *   | ticket   | Event ticket purchase                |
 *   | gift     | Gift item purchase                   |
 * @property item - Detailed information about the ordered item
 * @property quantity - Number of items ordered
 * @property total_amount - Total cost of the order in cents
 * @property order_date - Date when the order was placed
 * @property status - Current state of the order
 *   | Value     | Description                          |
 *   |-----------|--------------------------------------|
 *   | pending   | Order is awaiting processing         |
 *   | failed    | Order processing has failed          |
 *   | completed | Order has been successfully processed|
 * @property created_at - Timestamp when the order was created
 * @property updated_at - Timestamp when the order was last modified
 * @example
 * ```typescript
 * const order: Orders = {
 *   id: "ord_123",
 *   item_type: "ticket",
 *   quantity: 2,
 *   total_amount: 5000, // $50.00
 *   status: "completed",
 *   order_date: getPacificTimeNow()
 * };
 * ```
 * @see https://example-docs.com/api/types/orders
 */
export interface Orders {
  schedule?: EventSchedules;
  id?: string;
  order_by?: DocumentReference<Users>;
  square_order_id?: string;
  item_type?: 'ticket' | 'gift' | 'product';
  item?: {
    guests?: number;
    [key: string]: any; // Allow for other properties
  };
  quantity?: number;
  total_amount?: number;
  event_id?: DocumentReference<Events>;
  event_schedule_id?: DocumentReference<EventSchedules>;
  order_date?: Date;
  status?: 'pending' | 'failed' | 'completed';
  created_at?: Timestamp;
  updated_at?: Timestamp;
  event?: Events;
}
