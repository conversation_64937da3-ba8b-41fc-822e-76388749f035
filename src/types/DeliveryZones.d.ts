import { Timestamp } from 'firebase/firestore';

/**
 * DeliveryZones resource
 * @description Represents a geographic delivery zone with associated zip codes and delivery schedule.
 * Used to manage delivery areas and schedules for subscription services.
 *
 * @property id - Unique identifier for the delivery zone
 * @property zone_name - Descriptive name for the delivery zone (e.g., "Downtown", "North County")
 * @property delivery_days - Array of days when deliveries are made in this zone (e.g., ["Monday", "Thursday"])
 * @property delivery_window - Time window for deliveries in this zone (e.g., "3PM-9PM")
 * @property zip_codes - Array of postal codes included in this delivery zone
 * @property is_active - Boolean flag indicating if the delivery zone is currently active
 * @property start_date - Date string (YYYY-MM-DD) when this delivery zone becomes active
 * @property end_date - Date string (YYYY-MM-DD) when this delivery zone becomes inactive
 * @property deleted_at - Timestamp when the zone was soft-deleted (if applicable)
 * @property created_at - Timestamp when the delivery zone was created
 * @property updated_at - Timestamp when the delivery zone was last modified
 *
 * @example
 * ```typescript
 * const deliveryZone: DeliveryZones = {
 *   id: "zone_123",
 *   zone_name: "Downtown Area",
 *   delivery_days: ["Monday", "Thursday"],
 *   zip_codes: ["12345", "12346", "12347"],
 *   is_active: true,
 *   created_at: Timestamp.now(),
 *   updated_at: Timestamp.now()
 * };
 * ```
 *
 * @remarks
 * - All fields are optional but recommended for proper functionality
 * - Delivery zones can be deactivated (is_active=false) without deletion
 * - Soft deletion is supported via the deleted_at timestamp
 *
 * @see https://example-docs.com/api/types/delivery-zones
 */
export interface DeliveryZones {
  id?: string;
  zone_name?: string;
  delivery_days?: string[];
  delivery_window?: string;
  zip_codes?: string[];
  is_active?: boolean;
  start_date?: string;
  end_date?: string;
  deleted_at?: Timestamp;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}
