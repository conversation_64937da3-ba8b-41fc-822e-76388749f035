import { Timestamp } from 'firebase/firestore';

/**
 * Waivers resource
 * @description Represents a digital liability waiver signed by users, containing personal information
 * and acceptance details. Used to track legal agreements for event participation.
 *
 * @property id - Unique identifier for the waiver record
 * @property first_name - Legal first name of the person signing the waiver
 * @property last_name - Legal last name of the person signing the waiver
 * @property email - Email address of the person signing the waiver
 * @property zip_code - Postal code of the person signing the waiver
 * @property signed_at - Timestamp when the waiver was electronically signed
 * @property valid_until - Expiration date of the waiver
 * @property ip_address - IP address from which the waiver was signed
 * @property device_info - Information about the device used to sign the waiver
 *   Format: JSON string containing:
 *   - userAgent
 *   - platform
 *   - screenResolution
 *   - browser
 *
 * @example
 * ```typescript
 * const waiver: Waivers = {
 *   id: "waiver_123",
 *   first_name: "<PERSON>",
 *   last_name: "<PERSON><PERSON>",
 *   email: "<EMAIL>",
 *   zip_code: 12345,
 *   signed_at: Timestamp.now(),
 *   valid_until: new Date("2025-12-31"),
 *   ip_address: "***********",
 *   device_info: JSON.stringify({
 *     userAgent: "Mozilla/5.0",
 *     platform: "Windows",
 *     screenResolution: "1920x1080",
 *     browser: "Chrome"
 *   })
 * };
 * ```
 *
 * @remarks
 * - All fields are required as they contain essential legal information
 * - The waiver PDF is automatically generated upon signing
 * - IP address and device information are collected for legal verification
 *
 * @see https://example-docs.com/api/types/waivers
 */
export interface Waivers {
  first_name?: string;
  last_name?: string;
  email?: string;
  signed_at?: Timestamp;
  valid_until?: Date;
  ip_address?: string;
  device_info?: string;
  mailingList?: boolean;
  signature_path?: string;
  waiver_pdf_path?: string;
  waiver_pdf_generated_at?: Timestamp;
  waiver_version?: string;
  created_at?: Timestamp;
  deleted_at?: Timestamp | null;
  id?: string;
  age_group?: 'adult' | 'child' | 'senior';
  metadata?: object;
  booker_email?: string;
  waiver_accepted?: boolean;
}
