import { Timestamp } from 'firebase/firestore';

/**
 * MissingDeliveryAddresses resource
 * @description Represents a record of delivery addresses that are missing or incomplete.
 * Used to track and follow up with users who need to provide valid delivery information.
 *
 * @property id - Unique identifier for the missing address record
 * @property user_id - Reference to the user who needs to provide address information
 * @property email - Email address of the user for contact purposes
 * @property address - Partial or invalid address that needs correction
 * @property zip_code - Postal code associated with the address
 * @property created_at - Timestamp when the missing address record was created
 *
 * @example
 * ```typescript
 * const missingAddress: MissingDeliveryAddresses = {
 *   id: "missing_addr_123",
 *   user_id: doc(db, "users", "user_456"),
 *   email: "<EMAIL>",
 *   address: "123 Incomplete St",
 *   zip_code: "12345",
 *   created_at: Timestamp.now()
 * };
 * ```
 *
 * @remarks
 * - Used for tracking addresses that need verification or completion
 * - Helps maintain delivery quality by ensuring valid addresses
 * - Can be used to generate follow-up communications with users
 *
 * @see Related to DeliveryZones for address validation
 */
export interface MissingDeliveryAddresses {
  id?: string;
  email?: string;
  address?: string;
  zip_code?: string;
  status?: 'active' | 'inactive' | 'not_found';
  ip_address?: string;
  user_agent?: string;
  created_at?: Timestamp;
}
