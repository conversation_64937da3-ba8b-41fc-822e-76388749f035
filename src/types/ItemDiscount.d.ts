import { CatalogDiscount, CatalogObject } from 'square'; // Added CatalogObject

export type DiscountTargetType =
  (typeof DiscountTargetTypeValue)[keyof typeof DiscountTargetTypeValue];

export interface ItemDiscountValidationResult {
  isValid: boolean;
  message: string;
  discountId?: string;
  discountCodeName?: string; // The actual code/name from Square
  type?: CatalogDiscount['discountType'] | string | null;
  value?: string | bigint; // Percentage as string, Fixed Amount as BigInt (cents)
  targetType?: DiscountTargetType; // Which line item the discount applies to
  firstCycleTotalPreview?: number; // Overall first cycle total in cents (after capping)
  discountAmount?: number; // Actual discount amount applied in cents (after capping)
  originalProductPrice?: number; // Original product price in cents
  originalDeliveryFeePrice?: number; // Original delivery fee price in cents
}

// AppliedDiscount now inherits the structure but enforces validity and required fields
export interface AppliedDiscount {
  isValid: true; // Must be true
  message: string; // Keep message for consistency
  discountId: string;
  discountCodeName: string;
  type: CatalogDiscount['discountType'] | string | null;
  value: string | bigint; // Percentage as string, Fixed Amount as BigInt (cents)
  targetType: DiscountTargetType;
  firstCycleTotalPreview: number; // Cents
  discountAmount: number; // Cents
  originalProductPrice: number; // Cents
  originalDeliveryFeePrice: number; // Cents
}

export interface ItemDiscountDetailsResult {
  id: string;
  codeName: string;
  // Add other relevant fields from Square Discount object if needed later
  maximumAmountMoney?: { amount?: bigint | null; currency?: string | null } | null;
  fullObject?: CatalogObject; // Include the full object
}

export interface ValidateItemDiscountParams {
  discountCode: string;
  productVariationId: string;
}
