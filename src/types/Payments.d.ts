import { DocumentReference, Timestamp } from 'firebase/firestore';

import { Orders } from './Orders';

/**
 * Payments resource
 * @description Represents a payment transaction record in the system, tracking payment details, status, and processing information.
 * @property id - Unique identifier for the payment transaction
 * @property order_id - Reference to the associated order in the system
 * @property method - Payment method used for the transaction
 *   | Value       | Description                          |
 *   |-------------|--------------------------------------|
 *   | credit_card | Payment made via credit card         |
 *   | apple_pay   | Payment made via Apple Pay           |
 * @property gateway - Payment processing service used
 *   | Value  | Description                               |
 *   |--------|-------------------------------------------|
 *   | square | Payment processed through Square gateway   |
 * @property status - Current state of the payment
 *   | Value      | Description                           |
 *   |------------|---------------------------------------|
 *   | failed     | Payment processing failed             |
 *   | cancelled  | Payment was cancelled                 |
 *   | successful | Payment completed successfully        |
 * @property amount - Payment amount in cents (e.g., 2000 = $20.00)
 * @property payment_date - Date when the payment was processed
 * @property square_payment_id - External payment ID from Square
 * @property metadata - Additional payment-related data
 * @property created_at - Timestamp when the payment record was created
 * @property updated_at - Timestamp when the payment record was last modified
 * @example
 * ```typescript
 * const payment: Payments = {
 *   id: "pmt_123",
 *   method: "credit_card",
 *   gateway: "square",
 *   status: "successful",
 *   amount: 5000, // $50.00
 *   payment_date: getPacificTimeNow(),
 *   square_payment_id: "sqr_123456"
 * };
 * ```
 * @see https://example-docs.com/api/types/payments
 */
export interface Payments {
  id?: string;
  order_id?: DocumentReference<Orders>;
  method?: 'credit_card' | 'apple_pay' | 'CASH' | string;
  gateway?: 'square';
  status?: 'failed' | 'cancelled' | 'successful';
  amount?: number;
  payment_date?: Date;
  square_payment_id?: string;
  metadata?: object;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}
