import { Timestamp } from 'firebase/firestore';

import { EventSchedules } from './EventSchedules';

/**
 * Location information for an event.
 * Contains details about the physical or virtual location where an event takes place.
 * @property name - The name of the venue or location
 * @property street - Street address of the location
 * @property city - City where the event is located
 * @property state - State or province of the location
 * @property zip_code - Postal code of the location
 * @property country - Country where the event is located
 * @property coordinates - Geographic coordinates (latitude/longitude) of the location
 * @see https://example-docs.com/api/types/event-location
 */
export interface EventLocation {
  name?: string;
  street?: string;
  city?: string;
  state?: string;
  zip_code?: number;
  country?: string;
  latitude?: number;
  longitude?: number;
}

/**
 * Custom registration questions for event attendees.
 * Defines additional information to be collected during event registration.
 * @property question_text - The text of the question to be displayed
 * @property question_type - The type of input expected for the answer
 * @property is_required - Whether an answer is mandatory
 * @property show_for - Array of guest types this question applies to (e.g., ["adult", "child"])
 * @example
 * ```typescript
 * const question: EventQuestions = {
 *   question_text: "What is your email address?",
 *   question_type: "email",
 *   is_required: true,
 *   show_for: ["adult"]
 * };
 * ```
 * @see https://example-docs.com/api/types/event-questions
 */
export interface EventQuestions {
  question_text: string;
  question_type: 'email' | 'phone' | 'text';
  is_required: boolean;
  show_for: ('adult' | 'child' | 'senior')[];
}

/**
 * Event resource
 * Contains all information about an event, including its location, schedule, and registration details.
 * @property id - Unique identifier for the event
 * @property event_location - Location information for the event
 * @property title - Name or title of the event
 * @property description - Detailed description of the event
 * @property image - URL to the event's featured image
 * @property start_date - Starting date and time of the event
 * @property end_date - Ending date and time of the event
 * @property fees_on_customer - Indicates if processing fees are passed to customers
 * @property event_questions - List of custom registration questions
 * @property event_schedules - List of scheduled sessions within the event
 * @property deleted_at - Timestamp when the event was soft deleted
 * @property created_at - Timestamp when the event was created
 * @property updated_at - Timestamp when the event was last updated
 * @example
 * ```typescript
 * const event: Events = {
 *   id: "evt_123",
 *   title: "Summer Farm Festival",
 *   event_location: {
 *     name: "Green Acres Farm",
 *     city: "Springfield",
 *     state: "IL"
 *   },
 *   start_date: new Date("2024-06-01"),
 *   end_date: new Date("2024-06-02")
 * };
 * ```
 * @see https://example-docs.com/api/types/events
 */
export interface Events {
  guidelines: [string];
  what_to_expect: [string];
  id?: string;
  event_location?: EventLocation;
  title?: string;
  description?: string;
  image?: string;
  start_date?: Date;
  end_date?: Date;
  fees_on_customer?: boolean;
  event_questions?: EventQuestions[];
  event_schedules?: EventSchedules[];
  instructions?: string[];
  deleted_at?: Timestamp | null;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}
