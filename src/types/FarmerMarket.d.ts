import { Timestamp } from 'firebase/firestore';

interface Schedule {
  day: 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun';
  start_time: string;
  end_time: string;
  is_closed: boolean;
}

interface Location {
  street?: string;
  city?: string;
  state?: string;
  zip_code?: number;
  latitude?: number | string;
  longitude?: number | string;
}

export interface FarmerMarket {
  id: string;
  name: string;
  is_active: boolean;
  season_start_date: string;
  season_end_date: string;
  location: Location;
  schedules: Schedule[];
  created_at: Timestamp;
  updated_at: Timestamp;
  deleted_at: Timestamp | null;
}
