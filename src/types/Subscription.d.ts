export interface SubscriptionAction {
  id: string;
  type: 'PAUSE' | 'RESUME' | string;
  effective_date: string;
  info?: {
    detail?: string;
    code?: string;
  };
}

/**
 * Represents a user subscription from Square
 */
export interface Subscription {
  id: string;
  locationId: string;
  customerId: string;
  startDate: string;
  status?: 'ACTIVE' | 'PENDING' | 'CANCELED' | 'PAUSED' | 'DEACTIVATED';
  createdAt: string;
  planVariationId: string;
  canceledDate?: string;
  chargedThroughDate?: string;
  cardId?: string | null;
  cardDetails?: CardDetails | null;
  phases?: SubscriptionPhase[];
  orderDetails?: OrderDetails;
  invoice_ids?: string[];
  invoices?: Invoice[];
  catalogDetails?: CatalogDetails;
  square_subscription_response?: {
    subscription?: any;
    actions?: SubscriptionAction[];
  };
  square_order_template_response?: {
    order?: any;
  };
}

/**
 * Represents a phase in a subscription
 */
export interface SubscriptionPhase {
  uid: string;
  ordinal: string | number;
  orderTemplateId?: string | null;
  planPhaseUid?: string | null;
}

/**
 * Represents an invoice from Square
 */
export interface Invoice {
  id: string;
  createdAt: string;
  status: 'DRAFT' | 'UNPAID' | 'SCHEDULED' | 'PAID' | 'PARTIALLY_PAID' | 'CANCELED' | 'FAILED';
  amount?: number;
  dueDate?: string;
  paymentUrl?: string;
  invoiceNumber?: string;
}

/**
 * Represents payment card details for a subscription
 */
export interface CardDetails {
  id: string;
  cardBrand: 'VISA' | 'MASTERCARD' | 'AMEX' | 'DISCOVER' | string;
  last4: string;
  expMonth: string;
  expYear: string;
  billingAddress?: {
    postalCode?: string;
    addressLine1?: string;
    addressLine2?: string;
    locality?: string;
    administrativeDistrictLevel1?: string;
    country?: string;
  };
  fingerprint?: string;
  customerId?: string;
  merchantId?: string;
  enabled?: boolean;
  cardType?: 'CREDIT' | 'DEBIT' | string;
  prepaidType?: 'PREPAID' | 'NOT_PREPAID' | string;
  bin?: string;
  version?: string;
}

/**
 * Represents order details for a subscription from Square
 */
export interface OrderDetails {
  id: string;
  locationId: string;
  source?: Record<string, any>;
  customerId: string;
  lineItems: OrderLineItem[];
  fulfillments?: OrderFulfillment[];
  netAmounts: {
    totalMoney: Money;
    taxMoney: Money;
    discountMoney: Money;
    tipMoney: Money;
    serviceChargeMoney: Money;
  };
  createdAt: string;
  updatedAt: string;
  state: 'DRAFT' | 'OPEN' | 'COMPLETED' | 'CANCELED';
  version: number;
  totalMoney: Money;
  totalTaxMoney: Money;
  totalDiscountMoney: Money;
  totalTipMoney: Money;
  totalServiceChargeMoney: Money;
  pricingOptions?: {
    autoApplyTaxes: boolean;
  };
  netAmountDueMoney: Money;
}

/**
 * Represents a line item in an order
 */
export interface OrderLineItem {
  uid: string;
  name: string;
  quantity: string;
  note?: string;
  catalogObjectId?: string;
  catalogVersion?: string;
  variationName?: string;
  itemType: 'ITEM' | string;
  basePriceMoney: Money;
  variationTotalPriceMoney: Money;
  grossSalesMoney: Money;
  totalTaxMoney: Money;
  totalDiscountMoney: Money;
  totalMoney: Money;
  pricingBlocklists?: Record<string, any>;
  totalServiceChargeMoney: Money;
}

/**
 * Represents a fulfillment in an order
 */
export interface OrderFulfillment {
  uid: string;
  type: 'SHIPMENT' | 'PICKUP' | string;
  state: 'PROPOSED' | 'RESERVED' | 'PREPARED' | 'COMPLETED' | 'CANCELED' | string;
  shipmentDetails?: {
    recipient: {
      customerId?: string;
      displayName?: string;
      emailAddress?: string;
      phoneNumber?: string;
      address?: Address;
    };
  };
}

/**
 * Represents catalog details for a subscription plan from Square
 */
export interface CatalogDetails {
  type: 'SUBSCRIPTION_PLAN_VARIATION' | string;
  id: string;
  updatedAt: string;
  version: string;
  isDeleted: boolean;
  presentAtAllLocations: boolean;
  subscriptionPlanVariationData: {
    name: string;
    phases: {
      uid: string;
      cadence: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | string;
      ordinal: string;
      pricing: {
        type: 'RELATIVE' | 'FIXED' | string;
      };
    }[];
    subscriptionPlanId: string;
  };
}

/**
 * Represents a monetary amount
 */
export interface Money {
  amount: string;
  currency: string;
}

/**
 * Represents an address
 */
export interface Address {
  addressLine1?: string;
  addressLine2?: string;
  locality?: string;
  administrativeDistrictLevel1?: string;
  postalCode?: string;
  country?: string;
}
