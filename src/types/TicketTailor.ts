/**
 * Ticket Tailor API type definitions
 */

export interface TicketType {
  object: string;
  event_name?: string;
  event_date?: string;
  event_location?: string;
  id: string;
  access_code: string | null;
  barcode?: string;
  booking_fee: number;
  description: string | null;
  discounts?: [];
  group_id: string | null;
  has_overrides: string;
  hide_after: string | null;
  hide_until: string | null;
  hide_when_sold_out: string;
  max_per_order: number;
  min_per_order: number;
  name: string;
  override_id: string | null;
  price: number;
  quantity: number;
  quantity_held: number;
  quantity_in_baskets: number;
  quantity_issued: number;
  quantity_total: number;
  show_quantity_remaining: string;
  show_quantity_remaining_less_than: number | null;
  sort_order: number;
  status: string;
  type: string;
}

export interface DateTimeInfo {
  date: string;
  formatted: string;
  iso: string;
  time: string;
  timezone: string;
  unix: number;
}

export interface TicketTailorEvent {
  status: string;
  venue: any;
  checkout_url: string;
  call_to_action: string;
  name: string;
  data: any;
  object: string;
  id: string;
  bundles?: [];
  chk: string;
  currency: string;
  end: DateTimeInfo;
  event_series_id: string;
  hidden: string;
  max_tickets_sold_per_occurrence: number;
  override_id: string | null;
  revenue: number;
  start: DateTimeInfo;
  ticket_groups?: [];
  ticket_types: TicketType[];
  tickets_available: string;
  total_issued_tickets: number;
  unavailable: string;
  unavailable_status: string | null;
  url: string;
}

export interface TicketTailorResponse {
  data: TicketTailorEvent[] | null;
  next: string | null;
  previous: string | null;
}

export interface TicketTailorOrder {
  name?: string;
  start_date?: string;
  event_name: string;
  event_details?: {
    id: string;
    event_id: string;
    event_series_id: string;
    name: string;
    start_date: {
      date: string;
      formatted: string;
      iso: string;
      time: string;
      timezone: string;
      unix: number;
    };
    end_date: {
      date: string;
      formatted: string;
      iso: string;
      time: string;
      timezone: string;
      unix: number;
    };
    venue: {
      name: string;
      postal_code: string;
    };
  };
  barcode: string;
  ticket_type: string;
  created_at: number;
  event_date: string;
  timeslot: string;

  event_summary: {
    name?: string;
    event_series_id?: string;
    start_date?: DateTimeInfo;
    id?: string;
    end_date?: DateTimeInfo;
    venue?: {
      postal_code?: string;
      name?: string;
    };
    event_id?: string;
  };
  referral_tag?: string | null;
  tax?: number;
  id?: string;
  line_items?: {
    value?: number;
    id?: string;
    quantity?: number;
    booking_fee?: number;
    object?: string;
    total?: number;
    item_id?: string;
    type?: string;
    description?: string;
  }[];
  tax_treatment?: string;
  subtotal?: number;
  marketing_opt_in?: boolean | null;
  object?: string;
  credited_out_amount?: number;
  total?: number;
  refunded_voucher_id?: string | null;
  status_message?: string | null;
  issued_tickets?: {
    first_name?: string;
    id?: string;
    source?: string;
    barcode_url?: string;
    status?: string;
    updated_at?: number;
    email: string;
    created_at: number;
    ticket_type_id: string | null;
    last_name: string;
    reference: string | null;
    description: string;
    voided_at: string | null;
    event_id: string;
    qr_code_url: string;
    custom_questions: {
      question: string;
      answer: string;
    }[];
    barcode: string;
    event_series_id: string;
    add_on_id: string | null;
    object: string;
    reservation: string | null;
    order_id: string;
    group_ticket_barcode: string | null;
    full_name: string;
    checked_in: string;
  }[];
  buyer_details: {
    last_name: string;
    name: string;
    email: string;
    first_name: string;
    custom_questions: {
      answer: string;
      question: string;
    }[];
    phone: string | null;
    address: {
      address_1: string | null;
      address_3: string | null;
      postal_code: string | null;
      address_2: string | null;
    };
  };
  refund_amount: number;
  currency: {
    code: string;
    base_multiplier: number;
  };
  payment_method: {
    name: string | null;
    id: string | null;
    external_id: string | null;
    instructions: string | null;
    type: string;
  };
  txn_id: string;
  meta_data: any[];
  total_paid: number;
  status: string;
}
