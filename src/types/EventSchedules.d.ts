import { Timestamp } from 'firebase/firestore';

/**
 * Event Schedule resource
 * Represents a specific time slot or session within an event, including capacity and pricing details.
 * @property id - Unique identifier for the schedule
 * @property title - Name or description of this specific schedule/session
 * @property event_date - The date when this schedule occurs
 * @property price_per_adult - Cost per adult attendee
 * @property price_per_child - Cost per child attendee
 * @property max_no_of_guest - Maximum number of guests allowed per booking
 * @property tickets_sold - Current count of tickets sold for this schedule
 * @property max_no_of_tickets - Maximum total tickets available for this schedule
 * @property start_time - Starting time of the session (format: "HH:mm")
 * @property end_time - Ending time of the session (format: "HH:mm")
 * @property check_in_passcode - Security code required for check-in
 * @property check_out_passcode - Security code required for check-out
 * @property deleted_at - Timestamp when the schedule was soft deleted
 * @property created_at - Timestamp when the schedule was created
 * @property updated_at - Timestamp when the schedule was last modified
 * @example
 * ```typescript
 * const schedule: EventSchedules = {
 *   id: "sch_123",
 *   title: "Morning Session",
 *   event_date: new Date("2024-06-01"),
 *   price_per_adult: 25.00,
 *   price_per_child: 15.00,
 *   start_time: "09:00",
 *   end_time: "12:00",
 *   max_no_of_tickets: 100
 * };
 * ```
 * @see https://example-docs.com/api/types/event-schedules
 */
export interface EventSchedules {
  id?: string;
  title?: string;
  event_date?: Date;
  price_per_adult?: number;
  price_per_child?: number;
  max_no_of_guest?: number;
  tickets_sold?: number;
  max_no_of_tickets?: number;
  start_time?: string;
  end_time?: string;
  check_in_passcode?: string;
  check_out_passcode?: string;
  deleted_at?: Timestamp | null;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}
