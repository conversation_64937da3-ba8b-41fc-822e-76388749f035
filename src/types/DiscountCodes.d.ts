import { serverTimestamp } from 'firebase/firestore';

export interface DiscountRedemptions {
  id?: string;
  square_customer_id?: string;
  email?: string;
  redeemed_at?: Date;
  order_id?: string;
  subscription_id?: string;
  amount_saved?: number;
}

export interface DiscountCodes {
  id?: string;
  code?: string;
  square_discount_id?: string;
  type?: 'percentage' | 'amount';
  value?: number;
  active?: boolean;
  restrictions?: {
    min_order_amount?: {
      enabled?: boolean;
      amount?: number;
      error_message?: string;
    };
    item_restriction?: {
      enabled?: boolean;
      allowed_item_ids?: string[];
      error_message?: string;
    };
    one_time_per_user?: {
      enabled?: boolean;
      error_message?: string;
    };
  };
  discount_redemptions?: DiscountRedemptions[];
  created_at?: serverTimestamp;
  updated_at?: serverTimestamp;
}
