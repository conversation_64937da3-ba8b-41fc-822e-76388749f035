export const TIME_ZONE = 'America/Los_Angeles';
export const VM_EMAIL = 'mailto:<EMAIL>';
export const VM_FB = 'https://www.facebook.com/verymulberry.ca';
export const VM_INSTAGRAM = 'https://www.instagram.com/verymulberry';
export const VM_TIKTOK = 'https://www.tiktok.com/@verymulberry';

// Add these constants for subscription details
export const SUBSCRIPTION_DEFAULTS = {
  PRODUCT_FALLBACK_IMAGE_URL: '/Very Mulberry Clamshell (Weekly Delivery Subscription).png',
};

/**
 * Firebase collection names used throughout the application.
 * These constants ensure consistency when referencing Firestore collections.
 */
export const FIREBASE_COLLECTIONS = {
  EVENTS: 'events',
  EVENT_SCHEDULES: 'event_schedules',
  USER_REFERRALS: 'user_referrals',
};
/**
 * Firebase collection names used throughout the application.
 * These constants ensure consistency when referencing Firestore collections.
 */
export const COLLECTIONS = {
  EVENTS: 'events', // Collection for storing event details
  EVENT_SCHEDULES: 'event_schedules', // Subcollection for event schedules
  ORDERS: 'orders', // Collection for order information
  PAYMENTS: 'payments', // Collection for payment records
  TICKETS: 'tickets', // Collection for ticket details
  USERS: 'users', // Collection for user profiles
  WAIVERS: 'waivers', // Collection for digital waivers
  WAIVER_VERSIONS: 'waiver_versions', // Collection for waiver version content
  FARMER_MARKETS: 'farmer_markets',
  DELIVERY_ZONES: 'delivery_zones',
  MISSING_DELIVERY_ADDRESSES: 'missing_delivery_addresses',
  SUBSCRIPTIONS: 'subscriptions',
  CHECK_IN_PASSCODE: 'check-in-passcode',
  DISCOUNT_CODES: 'discount_codes',
  DISCOUNT_REDEMPTIONS: 'discount_redemptions',
  GIFT_ORDERS: 'gift_orders',
  REFERRAL_POLICIES: 'referral_policies',
} as const;

export const TICKET_TAILOR_ENPOINTS = {
  TICKET_TAILOR_URL: 'https://api.tickettailor.com/v1',
  TICKET_TAILOR_EVENT_URL: 'https://www.tickettailor.com/events/',
};

export const ENDPOINTS = {
  TICKET_TAILOR_URL: 'https://api.tickettailor.com/v1',
  SQUARE_URL: 'https://connect.squareup.com/v2',
  GOOGLE_MAPS_URL: 'https://www.google.com/maps/dir/?api=1&destination=',
  GOOGLE_MAPS_ADDRESS_URL: 'https://www.google.com/maps/search/?api=1&query=',
};

/**
 * API endpoint paths used throughout the application.
 * These constants ensure consistency when making API requests.
 *
 * @property EVENT_SCHEDULES - Endpoint for retrieving event schedules
 *   - GET: Returns all events with their associated schedules
 *   - Response: { message: string, data: Events[] }
 *
 * @example
 * fetch(API_ENDPOINTS.EVENT_SCHEDULES)
 */
export const API_ENDPOINTS = {
  EVENT_SCHEDULES: '/api/event-schedules',
  PROCESS_PAYMENT_LINK: '/api/square/process-payment',
  EVENT_SCHEDULE: '/api/event-schedule',
  UPDATE_ORDER_STATUS: '/api/update-order-status',
  GET_ORDER_DETAILS: '/api/get-order-details',
  GET_USER_TICKETS: '/api/get-user-tickets',
  GET_TICKET_BY_ID: '/api/get-ticket-by-id',
  CREATE_WAIVER: '/api/create-waiver',
  SEND_MAGIC_LINK: '/api/auth/send-magic-link',
  CHECK_WAIVER: '/api/check-waiver',
  VERIFY_CHECK_IN: '/api/verify-check-in',
  EVENT: (eventId: string) => `/api/get-event-by-id?eventId=${eventId}`,
  GET_TICKET_STOCK: '/api/ticket-stock',
  GET_WAIVER_CONTENT: '/api/waiver-content',
  FARMER_MARKETS: '/api/farmer-markets',
  UPDATE_PROFILE: '/api/users/update-profile',
  GET_TICKETS_BY_ORDER: '/api/get-tickets-by-order',
  VALIDATE_DELIVERY_ADDRESS: '/api/subscription/validate-address',
  LOG_MISSING_ADDRESS: '/api/subscription/missing-addresses',
  UPDATE_MISSING_ADDRESS_LOG: '/api/subscription/update-missing-address',
  GET_DELIVERY_ZONE: '/api/subscription/delivery-zone',
  GET_PRODUCT: '/api/subscription/get-product',
  GET_PRODUCT_IMAGE: '/api/square/get-image',
  SUBSCRIPTION_PAYMENT_LINK: '/api/subscription/payment-link',
  GET_USER_WAIVERS: '/api/get-user-waivers',
  GET_USER_ORDERS: '/api/get-user-orders',
  GET_USER_SUBSCRIPTION: '/api/subscription/get-user-subscription',
  UPDATE_SUBSCRIPTION: '/api/subscription/update-card',
  CANCEL_SUBSCRIPTION: '/api/subscription/cancel',
  CREATE_SUBSCRIPTION: '/api/subscription/create',
  CREATE_ONE_TIME_ORDER: '/api/subscription/create-one-time',
  VALIDATE_DISCOUNT: '/api/subscription/validate-discount',
  GET_OR_CREATE_CUSTOMER: '/api/square/get-or-create-customer',
  GET_SUBSCRIPTION_BY_ID: '/api/subscription/get_by_id',
  TICKETS: '/api/tickets',
  TICKET_TAILOR_EVENT_SCHEDULE: '/api/ticket-tailor/get-event-schedule',
  TT_GET_TICKET_BY_ID: '/api/ticket-tailor/get-ticket-by-id',
  TT_GET_TICKETS_BY_ORDER: '/api/ticket-tailor/get-tickets-by-order',
  TT_VERIFY_CHECK_IN: '/api/ticket-tailor/verify-check-in',
  REDEEM_CLAMSHELLS: '/api/checkout/redeem',
  PAUSE_SUBSCRIPTION: '/api/subscription/pause',
  SEND_WAIVER_NOTIFICATION: '/api/waiver-notification',
  IMPOSONATE_USER: '/api/auth/admin/impersonate',
  VERIFY_CHECK_IN_STATUS: '/api/ticket-tailor/verify-check-in-status',
  GET_DELIVERY_ZONES: '/api/get-delivery-zone',
  GET_WAIVER_PDF_URL: '/api/waivers/get-pdf-url',
  FIND_OR_CREATE_USER: '/api/auth/find-or-create-user',
  SYNC_USER_DATA: '/api/auth/sync-user-data',
  FIND_USER_BY_AUTH: '/api/auth/find-user-by-auth',
  VERIFY_ACCESS_TOKEN: '/api/verify-access-token',
  GET_ORDER_BY_ID: '/api/square/get-order-by-id',
  CREATE_GIFT_ORDER: '/api/gift/create-order',
  UPDATE_GIFT_ORDER: '/api/gift/update-order',
  GET_OR_CREATE_REFERRAL_CODE: '/api/referral/get-or-create-code',

  //self-checkout
  SELF_CHECKOUT_ANONYMOUS_INIT: '/api/self-checkout/anonymous/init',
  SELF_CHECKOUT_ANONYMOUS_PAYMENT_INTENT: '/api/self-checkout/anonymous/payment-intent',
  SELF_CHECKOUT_ANONYMOUS_CLAIM_DETAILS: '/api/self-checkout/anonymous/claim-details',
  SELF_CHECKOUT_ANONYMOUS_CLAIM: '/api/self-checkout/anonymous/claim',
  SELF_CHECKOUT_GET_IMAGE: '/api/self-checkout/get-image',
  SELF_CHECKOUT_ANONYMOUS_VERIFY_TAP: '/api/self-checkout/anonymous/verify-tap',
} as const;

/**
 * Application routes used for navigation
 * These constants ensure consistency in URL paths across the application
 */
export const ROUTES = {
  EVENT: (eventId: string) => `/event/${eventId}`,
  EVENT_GUESTS: (eventId: string, scheduleId: string) => `/event/${eventId}/schedule/${scheduleId}`,
  TICKET_SUMMARY: (eventId: string, scheduleId: string) =>
    `/event/${eventId}/schedule/${scheduleId}/ticket-summary`,
  LOGIN: '/auth/login',
  SIGN_WAIVER: '/sign-waiver',
  TICKETS: '/tickets',
  WAIVER_THANK_YOU: '/waiver-thank-you',
  SCHEDULE: '/schedule',
  ORDER_CONFIRMATION: (orderId: string) => `/order/${orderId}`,
  SUBSCRIPTION_DELIVERY_DAY: '/subscription/delivery-day',
  SUBSCRIPTION_PRODUCT: '/subscription/product',
  SUBSCRIPTION_PAYMENT: '/subscription/payment',
  SUBSCRIPTION_USER_INFO: '/subscription/user-info',
  SUBSCRIPTION_GIFT_DETAILS: '/subscription/gift-details',
  SUBSCRIPTION_CONFIRMATION: '/subscription/confirmation',
  RESERVATIONS: '/reservations',
  SUBSCRIPTIONS: '/subscriptions',
  SUBSCRIPTION: '/subscription',
  CHECKOUT: (orderId: string) => `reservations/checkout/${orderId}`,
  FARM_VISIT_INSTRUCTIONS: '/reservations/farm-visit-instructions',
  ADMIN_LOGIN: '/admin/login',
  SCHEDULE_TABS: '/schedule?tab=',
  PROFILE: '/profile',
};

/**
 * Success messages returned by API endpoints.
 * Used to maintain consistent response messaging across the application.
 */
export const SUCCESS_MESSAGES = {
  GET_EVENTS: 'Events retrieved successfully',
  GET_EVENT_SCHEDULES: 'Event schedules retrieved successfully',
  GET_EVENT_SCHEDULE: 'Event schedule retrieved successfully',
  WAIVER_SUBMITTED: 'Waiver submitted successfully!',
  CHECK_IN_SUCCESS: 'Check-in successful',
  GET_EVENT: 'Event retrieved successfully',
  GET_FARMER_MARKETS: 'Farmer markets retrieved successfully',
  SUBSCRIPTION_CANCELLED: 'Your subscription has been cancelled successfully.',
  CLAMSHELL_REDEEMED: 'Clamshell redeemed successfully',
  ALL_CLAMSHELLS_REDEEMED: 'All clamshells redeemed successfully',
  SUBSCRIPTION_PAUSED: 'Your subscription has been paused successfully.',
  PAYMENT_METHOD_UPDATED: 'Your payment method has been updated successfully',
  GET_DELIVERY_ZONES: 'Delivery zones retrieved successfully',
};

/**
 * Error messages returned by API endpoints.
 * Used to provide consistent error messaging across the application.
 */
export const ERROR_MESSAGES = {
  APP_CHECK_TOKEN_MISSING: 'Unauthorized: App Check token missing',
  APP_CHECK_TOKEN_INVALID: 'Unauthorized: Invalid App Check token',
  INVALID_REQUEST: 'Invalid Request',
  FAILED_TO_FETCH_EVENTS: 'Failed to fetch events',
  FAILED_TO_FETCH_WAIVER: 'Failed to load waiver content',
  FAILED_TO_FETCH_SCHEDULES: 'Failed to fetch schedules',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
  LOADING_SCHEDULES: 'Loading schedules...',
  FAILED_TO_UPDATE_ORDER_STATUS: 'Failed to update order status',
  FAILED_TO_FETCH_ORDER_DETAILS: 'Failed to fetch order details',
  MISSING_REQUIRED_FIELDS: 'Missing required fields',
  FAILED_TO_FETCH_TICKETS: 'Failed to fetch tickets',
  FAILED_TO_FETCH_TICKET: 'Failed to fetch ticket',
  FAILED_TO_SUBMIT_WAIVER: 'Failed to submit waiver',
  WAIVER_ALREADY_EXISTS: 'A valid waiver already exists for this email',
  FAILED_TO_CHECK_WAIVER: 'Failed to check waiver status',
  INVALID_CHECK_IN: 'Invalid check-in code. Please try again.',
  ALREADY_CHECKED_IN: 'This ticket has already been checked in',
  CHECK_IN_FAILED: 'Failed to process check-in. Please try again.',
  CHECK_IN_TOO_LATE: 'You are trying to Check-In too late.',
  CHECK_IN_TOO_EARLY: 'You are trying to Check-In too early.',
  CHECK_IN_TOO_EARLY_TIME: 'Check-in is only available 30 minutes before the event starts',
  FAILED_TO_FETCH_FARMER_MARKETS: 'Failed to fetch farmer markets',
  FAILED_TO_VALIDATE_ADDRESS: 'Failed to validate delivery address, Please try again.',
  FAILED_TO_LOG_MISSING_ADDRESS: 'Failed to log missing address',
  MISSING_REQUIRED_SUBSCRIPTION_ADDRESS_FIELDS: 'Email, address, and zip code are required',
  FAILED_TO_FETCH_PRODUCT: 'Failed to fetch product details',
  SELECT_START_DATE_FOR_YOUR_SUBSCRIPTION: 'Please select a start date for your subscription',
  FAILED_TO_CREATE_PAYMENT_LINK: 'Failed to create payment link. Please try again.',
  FAILED_TO_CANCEL_SUBSCRIPTION: 'Failed to cancel your subscription. Please try again.',
  FAILED_TO_CREATE_SUBSCRIPTION: 'Failed to create subscription. Please try again.',
  FAILED_TO_GET_CUSTOMER: 'Failed to get or create customer. Please try again.',
  FAILED_TO_FETCH_SUBSCRIPTION: 'Failed to fetch subscription details',
  FAILED_TO_CREATE_CUSTOMER: 'Failed to create customer. Please try again.',
  INTERNAL_SERVER_ERROR: 'An internal server error occurred. Please try again later.',
  FAILED_TO_FETCH_USER_ORDERS: 'Failed to fetch user orders. Please try again.',
  FAILED_TO_REDEEM_CLAMSHELL: 'Failed to redeem clamshell',
  FAILED_TO_REDEEM_ALL_CLAMSHELLS: 'Failed to redeem all clamshells',
  NO_CLAMSHELLS_SELECTED: 'No clamshells selected for redemption',
  FAILED_TO_PAUSE_SUBSCRIPTION: 'Failed to pause your subscription. Please try again.',
  FAILED_TO_UPDATE_PAYMENT_METHOD: 'Failed to update payment method. Please try again.',
  PAYMENT_PROCESSING_FAILED:
    'Payment processing failed. Please check your card details and try again.',
  SUBSCRIPTION_CANCELLATION_NOT_ALLOWED:
    'Subscription cancellation is not allowed on Sunday. Please try again tomorrow.',
  FAILED_TO_FETCH_DELIVERY_ZONES: 'Failed to fetch delivery zones',
  NO_WAIVER_CONTENT_FOUND: 'No waiver content found',
  FAILED_TO_SANITIZE_HTML: 'Failed to sanitize HTML content',
  FAILED_TO_UPDATE_ADDRESS_LOG: 'Failed to update address log',
  FAILED_TO_CREATE_ORDER: 'Failed to create order',
  FAILED_TO_FETCH_ORDER: 'Failed to fetch order',
  FAILED_TO_GET_CODE: 'Failed to get referral code',
  FAILED_TO_GET_POLICY: 'Failde to get policy',
};

/**
 * Button texts used throughout the application.
 * These constants ensure consistency in button labels.
 */
export const BUTTON_TEXTS = {
  CANCEL: 'Cancel',
  BOOK_NOW: 'Book Now',
  SIGN_WAIVER: 'Sign Waiver',
  CHECK_IN: 'Check In',
  CHECK_OUT: 'Check Out',
  PAY_AND_BOOK: 'Pay & Book',
  VIEW_RESERVATIONS: 'View My U-Pick',
  ACCEPT_WAIVER_AND_COMPLETE: 'Accept Waiver and Complete',
  CONTINUE: 'Continue',
  DOWNLOAD: 'Download Ticket',
  VIEW_SCHEDULE: 'View Schedule',
  LOADING: 'Loading...',
  FILL_WAIVER: 'Fill Waiver',
  SIGNATURE_REQUIRED: 'Signature Required',
  RESET_SIGNATURE: 'Reset Signature',
  SCAN_TO_CHECK_IN: 'Scan to Check In',
  SOLD_OUT: 'Sold Out',
  SUBMIT: 'Submit',
  SUBMITTING: 'Submitting...',
  APPLY_DISCOUNT: 'Apply',
  CHECK_YOUR_ELIGIBILITY: 'Check your eligibility',
  CHECKING: 'Checking...',
  PAY_AND_SUBSCRIBE: 'Pay & Subscribe',
  PROCESSING: 'Processing...',
  CLOSED: 'Closed',
  SHARE_VIA_EMAIL: 'Share via Email',
  SHARE_VIA_SMS: 'Share via SMS',
  CLICK_TO_SHARE: 'Click Here To Send Them a Message',
  COPY_LINK: 'Copy Link',
};

/**
 * Form labels used throughout the application.
 * These constants ensure consistency in form field labels.
 */
export const FORM_LABELS = {
  FIRST_NAME: 'First Name',
  LAST_NAME: 'Last Name',
  EMAIL: 'Email',
  PHONE: 'Phone',
} as const;

/**
 * Validation error messages used in forms throughout the application.
 * These constants ensure consistency in form validation messages.
 */
export const VALIDATION_ERROR_MESSAGES = {
  FIRST_NAME_MIN: 'First Name must be at least 2 characters.',
  LAST_NAME_MIN: 'Last Name must be at least 2 characters.',
  EMAIL_INVALID: 'Invalid email address.',
  PHONE_MIN: 'Phone number must be at least 10 digits.',
  ZIP_CODE_INVALID: 'Please enter a valid ZIP code (e.g., 12345 or 12345-6789).',
  SIGNATURE_REQUIRED: 'Signature is required to complete the waiver.',
  EMAIL_REQUIRED_FOR_ADULTS: 'Email is required for adults',
  PHONE_REQUIRED_FOR_ADULTS: 'Phone is required for adults',
  INVALID_PHONE_FORMAT: 'Invalid phone number format',
  INVALID_PHONE_NUMBER: 'Invalid phone number',
  ADDRESS_MIN: 'Address must be at least 5 characters',
  CITY_REQUIRED: 'City is required',
  STATE_REQUIRED: 'State is required',
  ZIP_CODE_REQUIRED: 'Zip code is required',
  ADDRESS_IS_REQUIRED: 'Address is required',
} as const;

/**
 * Age groups used for ticketing and guest management.
 * These constants ensure consistency in age group representation.
 */
export const AGE_GROUPS = {
  ADULT: 'adult',
  CHILD: 'child',
  SENIOR: 'senior',
};

/**
 * User roles used for access control and user management.
 * These constants ensure consistency in role representation.
 */
export const USER_ROLES = {
  CUSTOMER: 'customer',
  DRIVER: 'driver',
  FARM: 'farm',
  FARM_MANAGER: 'farm_manager',
  FARMER: 'farmer',
  MARKET: 'market',
  ADMIN: 'admin',
};

/**
 * Question types used in event registration forms.
 * These constants ensure consistency in question type representation.
 */
export const QUESTION_TYPES = {
  EMAIL: 'email',
  PHONE: 'phone',
  TEXT: 'text',
} as const;

/**
 * Ticket status constants used throughout the application.
 * These constants ensure consistency when handling ticket states.
 */
export const TICKET_STATUS = {
  DRAFT: 'draft',
  RESERVED: 'reserved',
  CANCELLED: 'cancelled',
  CHECKED_IN: 'checked_in',
  CHECKED_OUT: 'checked_out',
  VALID: 'valid',
} as const;

/**
 * Display text for ticket statuses.
 * These constants ensure consistency in ticket status representation.
 */
export const TICKET_STATUS_DISPLAY = {
  [TICKET_STATUS.DRAFT]: 'Draft',
  [TICKET_STATUS.RESERVED]: 'Reserved',
  [TICKET_STATUS.CANCELLED]: 'Cancelled',
  [TICKET_STATUS.CHECKED_IN]: 'Checked In',
  [TICKET_STATUS.CHECKED_OUT]: 'Checked Out',
  [TICKET_STATUS.VALID]: 'Valid',
};

/**
 * Navigation items for the header.
 */
export const navigation = [
  {
    name: 'Very Mulberries',
    href: `${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}`,
  },
  { name: 'U-Pick', href: `${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}/u-pick` },
  {
    name: "Farmers' Markets",
    href: `${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}/farmers-markets`,
  },
  {
    name: 'Home Delivery',
    href: `${process.env.NEXT_PUBLIC_BASE_URL}${ROUTES.SUBSCRIPTION}`,
  },
  { name: 'FAQs', href: `${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}/faqs` },
  { name: 'Contact Us', href: `${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}/contact-us` },
];

/**
 * Routes that should display the sidebar.
 */
export const SIDEBAR_ROUTES = [
  '/',
  '/profile',
  '/reservations',
  '/purchases',
  '/waivers',
  '/subscriptions',
  '/preferences',
  '/or_', // Add this prefix to match dynamic order routes
  '/it_',
  '/referrals',
];

/**
 * Sidebar items for the navigation menu.
 */
export const sidebarItems = [
  { label: 'Home', path: '/' },
  { label: 'My Profile', path: '/profile' },
  { label: 'My U-Pick', path: '/reservations' },
  // { label: 'My Purchases', path: '/purchases' },
  // { label: 'My Waiver(s)', path: '/waivers' },
  { label: 'My Subscriptions', path: '/subscriptions' },
  { label: 'Referrals', path: '/referrals' },
];

export const TICKET_TYPES = {
  ADULT: 'adult',
  CHILD: 'child',
  SENIOR: 'senior',
  CLAM_SHELL: 'clamshell',
  PARKING: 'parking',
};

export const validTabs = ['all', 'u-pick', 'farmers-market', 'home-delivery'];

export const SCHEDULE_TABS_NAME = {
  ALL: 'all',
  UPICK: 'U-Pick',
  FARMERMARKET: 'Farmers&apos; Markets',
  HOMEDELIVERY: 'Home Delivery',
};

export const EVENT_STATUS = {
  PUBLISHED: 'published',
  DRAFT: 'draft',
  DELETED: 'deleted',
};

export const CLOSE_UPICK_CARD_TEXT = {
  HEADING: 'FARM CLOSED',
  NAME: 'Very Mulberry U-Pick',
  ADDRESS: 'Habitera Farms, 94513',
};

export const SOLD_OUT_CARD_TEXT = {
  HEADING: 'SOLD OUT',
  NAME: 'Very Mulberry U-Pick',
  ADDRESS: 'Habitera Farms, 94513',
};

export const SHARE_MESSAGES = {
  EMAIL_SUBJECT: 'Fresh Very Mulberries Delivered to Your Door',
  EMAIL_BODY: `Hey! I just signed up for Very Mulberry's home delivery service. They bring fresh, delicious Himalayan mulberries straight to your doorstep each week. You've got to check them out: ${process.env.NEXT_PUBLIC_BASE_URL}${ROUTES.SUBSCRIPTION}`,
  SMS_BODY: `Hey! I just signed up for Very Mulberry's home delivery service. They bring fresh, delicious Himalayan mulberries straight to your doorstep each week. You've got to check them out: ${process.env.NEXT_PUBLIC_BASE_URL}${ROUTES.SUBSCRIPTION}`,
};

export const FARM_ADDRESS = '501 Hoffman Ln, Brentwood, CA 94513';

export const FARM_ADDRESS_LINK = 'https://maps.app.goo.gl/8GHLsFSk7mpNzjVm8';

export const SCHEDULE_TITLE = {
  ALL: 'All',
  U_PICK: 'U-Pick',
  FARMERS_MARKET: 'FarmersMarket',
  HOME_DELIVERY: 'delivery',
  TICKET_TAILOR: 'ticketTailor',
};

/**
 * Subscription order types
 */
export const SUBSCRIPTION_ORDER_TYPES = {
  SELF: 'Self',
  ONE_TIME: 'OneTime',
  SINGLE_GIFT: 'SingleGift',
  MULTIPLE_GIFT: 'MultipleGift',
  ONE_TIME_GIFT: 'OneTimeGift',
} as const;

// Define target type using an enum-like const object for runtime access in Zod
export const DiscountTargetTypeValue = {
  PRODUCT: 'PRODUCT',
  DELIVERY_FEE: 'DELIVERY_FEE',
} as const;

export const APP_CHECK_RETRY_CONFIG = {
  MAX_RETRIES: 5,
  INITIAL_BACKOFF_MS: 500,
  BACKOFF_FACTOR: 2,
  MAX_BACKOFF_MS: 4000, // Cap delay at 4 seconds
} as const;
