import { toZonedTime } from 'date-fns-tz';

import { TIME_ZONE } from './constants';

/**
 * Gets the current date and time in Pacific Time zone
 * @returns Date object representing current time in PT
 */
export function getPacificTimeNow(): Date {
  return toZonedTime(new Date(), TIME_ZONE);
}

/**
 * Converts a date to Pacific Time zone
 * @param date - Date object or date string to convert
 * @returns Date object in PT timezone
 */
export function convertToPacificTime(date: Date | string): Date {
  return toZonedTime(new Date(date), TIME_ZONE);
}
