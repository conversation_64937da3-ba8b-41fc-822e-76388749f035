import { clsx, type ClassValue } from 'clsx';
import { parse, isValid, format, startOfToday } from 'date-fns'; // Added startOfToday
// import createDOMPurify from 'dompurify';
import { getAuth } from 'firebase/auth';
import { DocumentReference, Timestamp } from 'firebase/firestore';
import { Order as SquareOrder } from 'square';
import { twMerge } from 'tailwind-merge';

// Removed unused import: import { getAppCheckInstance } from './firebaseConfig';
import { EventLocation } from '../types/Events';
import { Users } from '../types/Users';

import { SUBSCRIPTION_ORDER_TYPES } from './constants';
import { getPacificTimeNow, convertToPacificTime } from './timezone'; // Added convertToPacificTime

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Formats a time string from 24-hour format (HH:mm) to 12-hour format with AM/PM
 *
 * @param time - Time string in HH:mm format (e.g., "14:30") or undefined
 * @returns
 * - Formatted time string in "h:mm a" format (e.g., "2:30 PM")
 * - Original string if invalid format
 * - undefined if input is undefined
 *
 * @example
 * formatTime("14:30") // returns "2:30 PM"
 * formatTime("09:00") // returns "9:00 AM"
 * formatTime("invalid") // returns "invalid"
 * formatTime(undefined) // returns undefined
 */
export const formatTime = (time: string | undefined): string => {
  // Return type explicitly string
  if (!time) return ''; // Return empty string if undefined or null

  // Validate time format (HH:mm) using regex
  // Matches 00:00 to 23:59 format only
  const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
  if (!timeRegex.test(time)) {
    return time; // If invalid format, return original string
  }

  // Parse time string to Date object using date-fns
  // We use a dummy date (2000-01-01) since we only care about the time
  const parsedTime = parse(time, 'HH:mm', new Date(2000, 0, 1));

  // Format the time if valid, otherwise return original string
  return isValid(parsedTime) ? format(parsedTime, 'h:mm a') : time;
};

/**
 * Formats a time range from start and end times (24-hour format) to 12-hour format with AM/PM
 * @param startTime - Start time in 24-hour format (e.g., "14:00")
 * @param endTime - End time in 24-hour format (e.g., "16:00")
 * @returns Formatted time range string (e.g., "2:00 PM - 4:00 PM")
 */
export const formatTimeRange = (startTime: string, endTime: string): string => {
  const formattedStartTime = formatTime(startTime);
  const formattedEndTime = formatTime(endTime);
  // Handle cases where one or both times might be empty strings after formatting
  if (!formattedStartTime || !formattedEndTime) {
    return formattedStartTime || formattedEndTime || ''; // Return whichever is valid, or empty
  }
  return `${formattedStartTime} - ${formattedEndTime}`;
};

/**
 * Formats a date string or Date object into a human-readable format
 *
 * @param date - Date string in various formats or Date object
 * @returns
 * - Formatted date string in "MMMM d, yyyy" format (e.g., "March 15, 2024")
 * - Empty string if input is undefined/null
 * - Original string if parsing fails
 *
 * @example
 * formatDate("2024-03-15") // returns "March 15, 2024"
 * formatDate(getPacificTimeNow()) // returns current date in "Month D, YYYY" format
 * formatDate("03/15/2024") // returns "March 15, 2024"
 * formatDate(undefined) // returns ""
 */
export const formatDate = (date: string | Date | Timestamp | undefined | null): string => {
  if (!date) return ''; // Return an empty string if undefined/null

  let dateObject: Date | null = null;

  // Handle Firestore Timestamp
  if (date && typeof (date as Timestamp).toDate === 'function') {
    dateObject = (date as Timestamp).toDate();
  }
  // Handle Date object
  else if (date instanceof Date) {
    dateObject = date;
  }
  // Handle string date
  else if (typeof date === 'string') {
    // Try parsing with different formats
    const formats = [
      'yyyy-MM-dd',
      'yyyy/MM/dd',
      'MM-dd-yyyy',
      'MM/dd/yyyy',
      "yyyy-MM-dd'T'HH:mm:ss.SSSX",
      "yyyy-MM-dd'T'HH:mm:ssX",
    ]; // Added ISO formats
    for (const formatStr of formats) {
      const attemptParse = parse(date, formatStr, getPacificTimeNow());
      if (isValid(attemptParse)) {
        dateObject = attemptParse;
        break;
      }
    }
    // If still not parsed, try direct Date constructor (less reliable)
    if (!dateObject) {
      const attemptDirect = new Date(date);
      if (isValid(attemptDirect)) {
        dateObject = attemptDirect;
      }
    }
  }

  // Return formatted date if valid, otherwise return string representation of input
  return dateObject && isValid(dateObject) ? format(dateObject, 'MMMM d, yyyy') : String(date);
};

// Convert the raw timestamp object to a Date
export const formatTimestamp = (timestamp: Timestamp | null | undefined | Date | any): string => {
  if (!timestamp) return 'No date';

  try {
    // Handle different timestamp formats
    let date;
    if (timestamp instanceof Date) {
      date = timestamp;
    } else if (timestamp.seconds) {
      // Standard Firestore Timestamp
      date = new Date(timestamp.seconds * 1000);
    } else if (timestamp._seconds) {
      // Handle serialized Firestore Timestamp format
      date = new Date(timestamp._seconds * 1000);
    } else {
      return 'Invalid date';
    }

    // Validate the date is within reasonable range
    if (isNaN(date.getTime()) || date.getFullYear() > 2100) {
      return 'Invalid date';
    }

    return format(date, 'MMMM do, yyyy h:mm aaa');
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return 'Invalid date format';
  }
};

/**
 * Formats an event location into a readable address string.
 *
 * @param {EventLocation} eventLocation - The event location object.
 * @returns {string} - A formatted string like "Mulberry Farm, Nashville, Tennessee, USA".
 */
export const formatEventLocation = (eventLocation: EventLocation | undefined): string => {
  if (!eventLocation) return 'Location not available';

  const parts = [
    eventLocation.name,
    eventLocation.street,
    eventLocation.city,
    eventLocation.state,
    eventLocation.zip_code, // Include zip code
    eventLocation.country,
  ].filter(Boolean); // Filter out empty/null/undefined parts

  return parts.join(', ');
};

/**
 * Formats a phone number string into US format: (XXX) XXX-XXXX
 *
 * @param phone - Phone number string to format
 * @returns Formatted phone number string or original string if invalid
 *
 * @example
 * formatPhoneNumber("1234567890") // returns "(*************"
 * formatPhoneNumber("************") // returns "(*************"
 * formatPhoneNumber("invalid") // returns "invalid"
 */
export const formatPhoneNumber = (phone: string | null | undefined): string => {
  if (!phone) return '';

  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');

  // Handle numbers with or without country code '1'
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    const match = cleaned.match(/^1(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return `(${match[1]}) ${match[2]}-${match[3]}`;
    }
  } else if (cleaned.length === 10) {
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return `(${match[1]}) ${match[2]}-${match[3]}`;
    }
  }

  // If formatting fails, return original (or cleaned if preferred)
  return phone;
};

export function getDocumentId(refPath: DocumentReference<Users>): string {
  return refPath.id;
}

/**
 * Generates a waiver token for a user.
 * @param userData - The user's data.
 * @returns The encoded waiver token.
 */
export function generateWaiverToken(userData: Users) {
  // Create a simple payload with user data
  const payload = {
    fn: userData.first_name,
    ln: userData.last_name,
    em: userData.email,
    ag: userData.age_group,
  };

  // Convert to base64url
  const base64 = Buffer.from(JSON.stringify(payload)).toString('base64');
  // Convert to base64url format by replacing characters
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Decodes a waiver token and returns the user data.
 * @param token - The encoded waiver token.
 * @returns The decoded user data.
 * @throws Will throw an error if the token is invalid.
 */
export function decodeWaiverToken(token: string): {
  age_group: Users['age_group'];
  first_name: string;
  last_name: string;
  email: string | undefined;
} {
  try {
    // Replace URL-safe characters back to base64 standard
    let base64 = token.replace(/-/g, '+').replace(/_/g, '/');
    // Pad with '=' if necessary
    while (base64.length % 4) {
      base64 += '=';
    }
    const data = JSON.parse(Buffer.from(base64, 'base64').toString());

    // Basic validation of expected fields
    if (!data.fn || !data.ln || !data.ag) {
      throw new Error('Decoded token missing required fields');
    }

    return {
      age_group: data.ag,
      first_name: data.fn,
      last_name: data.ln,
      email: data.em, // Changed from data.email to data.em to match encoding
    };
  } catch (error: any) {
    console.error('Error decoding waiver token:', error);
    throw new Error('Invalid token');
  }
}

/**
 * Formats an array of delivery days into a human-readable string
 *
 * @param days - Array of delivery day strings (e.g., ["Monday", "Wednesday", "Friday"])
 * @returns Formatted string representation of delivery days
 *
 * @example
 * formatDeliveryDays(["Monday"]) // returns "Monday"
 * formatDeliveryDays(["Monday", "Wednesday"]) // returns "Monday and Wednesday"
 * formatDeliveryDays(["Monday", "Wednesday", "Friday"]) // returns "Monday, Wednesday, and Friday"
 * formatDeliveryDays([]) // returns "scheduled days"
 */
export const formatDeliveryDays = (days: string[] | undefined): string => {
  // Added undefined check
  // Return default text if no days are provided or array is empty
  if (!days || days.length === 0) return 'scheduled days';

  // Capitalize each day
  const capitalizedDays = days.map(day => day.charAt(0).toUpperCase() + day.slice(1));

  // For a single day, just return that day
  if (capitalizedDays.length === 1) return capitalizedDays[0];

  // For two days, join with "and"
  if (capitalizedDays.length === 2) return `${capitalizedDays[0]} and ${capitalizedDays[1]}`;

  // For three or more days, use Oxford comma format
  // Create a copy of the array to avoid modifying the original
  const daysCopy = [...capitalizedDays];
  const lastDay = daysCopy.pop();
  return `${daysCopy.join(', ')}, and ${lastDay}`;
};

/**
 * Calculates the subscription start date based on the selected delivery date.
 * The start date is the Saturday immediately preceding the delivery date.
 * @param {string} selectedDeliveryDate - The date selected by customer in 'YYYY-MM-DD' format
 * @returns {string} The calculated start date in 'YYYY-MM-DD' format.
 * @throws Error if the input date string is invalid.
 */
export function calculateSubscriptionStartDate(selectedDeliveryDate: string): string {
  // Parse the customer-selected date carefully
  const deliveryDate = parse(selectedDeliveryDate, 'yyyy-MM-dd', getPacificTimeNow()); // Use PT time

  if (!isValid(deliveryDate)) {
    throw new Error('Invalid delivery date format provided. Expected YYYY-MM-DD.');
  }

  // Get the day of the week (0 = Sunday, 6 = Saturday)
  const dayOfWeek = deliveryDate.getDay();

  // Calculate days to subtract to get to the previous Saturday
  // If delivery day is Sunday (0), subtract 1 day.
  // If delivery day is Monday (1), subtract 2 days.
  // ...
  // If delivery day is Saturday (6), subtract 7 days (or 0 if we want the same day?)
  // The formula (dayOfWeek + 1) % 7 works:
  // Sun(0) -> (0+1)%7 = 1 day back to Sat
  // Mon(1) -> (1+1)%7 = 2 days back to Sat
  // ...
  // Sat(6) -> (6+1)%7 = 0 days back (meaning the start date IS Saturday if delivery is Saturday) - Let's adjust this to be 7 days back
  let daysToSubtract = (dayOfWeek + 1) % 7;
  if (daysToSubtract === 0) {
    // If delivery day is Saturday, go back a full week
    daysToSubtract = 7;
  }

  // Clone the delivery date and subtract the calculated days
  const startDate = new Date(deliveryDate);
  startDate.setDate(deliveryDate.getDate() - daysToSubtract);

  // Format the start date as 'YYYY-MM-DD'
  const year = startDate.getFullYear();
  const month = String(startDate.getMonth() + 1).padStart(2, '0');
  const day = String(startDate.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Converts a date in 'YYYY-MM-DD' format to ISO format with time set to 00:00:00.000Z
 * @param {string} dateString - The date in 'YYYY-MM-DD' format
 * @returns {string} The date in ISO format with time set to zeros
 * @throws Error if the input date string is invalid.
 */
export function convertToISOWithZeroTime(dateString: string): string {
  // Parse the date string first
  const date = parse(dateString, 'yyyy-MM-dd', getPacificTimeNow()); // Use PT time

  if (!isValid(date)) {
    throw new Error('Invalid date string format. Expected YYYY-MM-DD.');
  }

  // Set the time to 00:00:00.000 (UTC)
  date.setUTCHours(0, 0, 0, 0);

  // Return the ISO string
  return date.toISOString();
}

// Format date from various timestamp formats
export const formatWaiverDate = (timestamp: any) => {
  if (!timestamp) return 'N/A';

  let date: Date | null = null;

  // Handle Firestore Timestamp
  if (timestamp?.toDate) {
    date = timestamp.toDate();
  }
  // Handle seconds timestamp
  else if (timestamp?.seconds) {
    date = new Date(timestamp.seconds * 1000);
  }
  // Handle Date object
  else if (timestamp instanceof Date) {
    date = timestamp;
  }
  // Handle string or number (attempt parsing)
  else {
    try {
      date = new Date(timestamp);
    } catch (_error) {
      return 'N/A'; // Return N/A if parsing fails
    }
  }

  // Check if the resulting date is valid
  if (!date || !isValid(date)) {
    return 'N/A';
  }

  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Format timestamp to show only time (h:mm a)
export const formatTimeOnly = (timestamp: any): string => {
  if (!timestamp) return 'N/A';

  let date: Date | null = null;

  // Handle Firestore Timestamp
  if (timestamp?.toDate) {
    date = timestamp.toDate();
  }
  // Handle seconds timestamp
  else if (timestamp?.seconds) {
    date = new Date(timestamp.seconds * 1000);
  }
  // Handle Date object
  else if (timestamp instanceof Date) {
    date = timestamp;
  }
  // Try to parse as string or number
  else {
    try {
      date = new Date(timestamp);
    } catch (_error) {
      return 'N/A'; // Return N/A if parsing fails
    }
  }

  // Check if the resulting date is valid
  if (!date || !isValid(date)) {
    return 'N/A';
  }

  return format(date, 'h:mm a');
};

/**
 * Gets the current user's Firebase ID token
 * @returns Promise that resolves to the ID token string or null if not authenticated
 * @throws Error if there's a problem getting the token
 */
export const getFirebaseToken = async (): Promise<string | null> => {
  const auth = getAuth();
  const currentUser = auth.currentUser;

  if (!currentUser) {
    return null; // Return null instead of throwing error here, let caller handle auth state
  }

  try {
    // Use forceRefresh=true only if you suspect the token might be stale
    // Usually, the SDK handles refresh automatically. Setting to false is generally safer.
    return await currentUser.getIdToken(false);
  } catch (error) {
    console.error('Error getting Firebase token:', error);
    // Throw a more specific error or return null based on how you want to handle this
    // Throwing makes it explicit that token retrieval failed.
    throw new Error('Failed to get authentication token');
  }
};

/**
 * Extracts custom attributes from Square API response
 *
 * @param customAttributeValues - The custom attribute values from Square API
 * @returns Record of attribute names and their typed values
 */
export const extractCustomAttributes = (
  customAttributeValues: Record<string, any> | undefined
): Record<string, string | number | boolean | string[] | undefined> => {
  const customAttributes: Record<string, string | number | boolean | string[] | undefined> = {};

  if (!customAttributeValues) return customAttributes;

  Object.entries(customAttributeValues).forEach(([_key, value]) => {
    // Skip if no name property
    if (!value?.name) return;

    // Handle different value types
    if (value.stringValue !== null && value.stringValue !== undefined) {
      customAttributes[value.name] = value.stringValue;
    } else if (value.numberValue !== null && value.numberValue !== undefined) {
      customAttributes[value.name] = Number(value.numberValue);
    } else if (value.booleanValue !== null && value.booleanValue !== undefined) {
      customAttributes[value.name] = value.booleanValue;
    } else if (value.selectionUidValues !== null && value.selectionUidValues !== undefined) {
      customAttributes[value.name] = value.selectionUidValues;
    }
  });

  return customAttributes;
};

/**
 * Returns the date of the next Sunday from today in "YYYY-MM-DD" format.
 * If today is Sunday, it returns the following Sunday.
 *
 * @returns string representing the next Sunday in "YYYY-MM-DD" format
 */
export function getNextSundayDate(): string {
  // Get today's date
  const today = convertToPacificTime(startOfToday()); // Use start of today in PT

  // Get the current day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  const currentDay = today.getDay();

  // Calculate days until next Sunday
  // If today is Sunday (0), we want to add 7 days to get next Sunday
  // Otherwise, we calculate days remaining until Sunday
  const daysUntilSunday = currentDay === 0 ? 7 : 7 - currentDay;

  // Add the calculated days to the current date
  const nextSunday = new Date(today);
  nextSunday.setDate(today.getDate() + daysUntilSunday);

  // Format the date as YYYY-MM-DD
  const year = nextSunday.getFullYear();
  // Add 1 to month since getMonth() returns 0-11
  const month = String(nextSunday.getMonth() + 1).padStart(2, '0');
  const day = String(nextSunday.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

/**
 * Helper function to extract order details from a Square order object
 * @param order - The Square order object
 * @returns An object containing product name, price, quantity, delivery fee and total
 */
export function getSubscriptionDetailsFromOrder(order: SquareOrder) {
  // Initialize variables to store the extracted information
  let productName: string = '';
  let productPrice: number = 0;
  let productQuantity: number = 0;
  let deliveryFee: number = 0;
  let originalDeliveryFee: number = 0;
  let total: number = 0;
  let totalDiscount: number = 0;

  // Process line items
  if (order.lineItems && Array.isArray(order.lineItems)) {
    order.lineItems.forEach(item => {
      // Check if the item is a delivery fee or a product
      if (item?.name?.toLowerCase().includes('delivery fee')) {
        // Extract delivery fee amount and convert from cents to dollars
        deliveryFee = item.totalMoney?.amount ? parseInt(String(item.totalMoney.amount)) / 100 : 0;
        originalDeliveryFee = item.basePriceMoney?.amount
          ? parseInt(String(item.basePriceMoney.amount)) / 100
          : 0;
      } else {
        // Extract product details
        productName = item.name ?? '';
        // Convert price from cents to dollars
        productPrice =
          item.basePriceMoney?.amount !== undefined && item.basePriceMoney?.amount !== null
            ? parseInt(String(item.basePriceMoney.amount)) / 100
            : 0;
        // Convert quantity from string to number
        productQuantity = parseInt(item.quantity);
      }
    });
  }

  // Get the total from the order and convert from cents to dollars
  if (order.totalMoney && order.totalMoney.amount) {
    total = parseInt(order.totalMoney.amount.toString()) / 100;
  }

  if (order.totalDiscountMoney && order.totalDiscountMoney.amount) {
    totalDiscount = parseInt(order.totalDiscountMoney.amount.toString()) / 100;
  }

  const originalTotalAmount = total + totalDiscount;

  // Return all extracted details
  return {
    productName,
    productPrice,
    productQuantity,
    deliveryFee,
    originalDeliveryFee,
    total,
    totalDiscount,
    originalTotalAmount,
  };
}

/**
 * Checks if subscription cancellation is allowed based on the current day
 * Cancellation is not allowed on Sundays (after Saturday midnight)
 *
 * @returns {boolean} True if cancellation is allowed, false otherwise
 * @example
 * if (!isSubscriptionCancellationAllowed()) {
 *   toast.error("Cancellation not allowed on Sunday");
 *   return;
 * }
 */
export function isSubscriptionCancellationAllowed(): boolean {
  const now = getPacificTimeNow();
  const day = now.getDay(); // 0 = Sunday, 6 = Saturday
  return day !== 0; // Not allowed on Sunday
}

/**
 * Determines if an order is a gift order based on the orderFor value
 * @param orderFor - The type of order
 * @returns boolean indicating if it's a gift order
 */
export const isGiftHomeDelivery = (orderFor: string | undefined): boolean => {
  return (
    orderFor !== undefined &&
    (orderFor === SUBSCRIPTION_ORDER_TYPES.SINGLE_GIFT ||
      orderFor === SUBSCRIPTION_ORDER_TYPES.MULTIPLE_GIFT ||
      orderFor === SUBSCRIPTION_ORDER_TYPES.ONE_TIME_GIFT)
  );
};

/**
 * Determines if an order is a one-time order based on the orderFor value
 * @param orderFor - The type of order
 * @returns boolean indicating if it's a one-time order
 */
export const isOneTimeHomeDelivery = (orderFor: string | undefined): boolean => {
  return (
    orderFor !== undefined &&
    (orderFor === SUBSCRIPTION_ORDER_TYPES.ONE_TIME ||
      orderFor === SUBSCRIPTION_ORDER_TYPES.ONE_TIME_GIFT)
  );
};

/**
 * Determines the standardized ticket type based on the ticket label
 *
 * @param ticketLabel - The original ticket label from the system
 * @returns The standardized ticket type or the original label if no match is found
 * @example
 * // Returns "adult" if the environment variable contains matching labels
 * const ticketType = getTicketType("Adult General Admission");
 */
export const getTicketType = (ticketLabel: string) => {
  if (!process.env.NEXT_PUBLIC_TICKET_TYPES) {
    return ticketLabel;
  }
  const ticketTypesMap = JSON.parse(process.env.NEXT_PUBLIC_TICKET_TYPES);

  const normalizedLabel = ticketLabel.trim().toLowerCase();

  // Check each type's array for matches
  for (const [type, labels] of Object.entries(ticketTypesMap) as [string, string[]][]) {
    if (
      labels.some(
        (label: string) =>
          normalizedLabel.includes(label.toLowerCase()) ||
          label.toLowerCase().includes(normalizedLabel)
      )
    ) {
      return type;
    }
  }

  // Default to adult if no match found
  return ticketLabel;
};

/**
 * Converts Firestore Timestamp fields in an object to JavaScript Date objects
 *
 * @param data - Object containing potential Timestamp fields
 * @returns Object with Timestamp fields converted to Date objects
 */
export const convertTimestampFields = (data: any): any => {
  if (!data) return data;

  const result = { ...data };

  // Process each field in the object
  Object.keys(result).forEach(key => {
    const value = result[key];

    // Check if the value is a Firestore Timestamp
    if (
      value &&
      typeof value === 'object' &&
      'toDate' in value &&
      typeof value.toDate === 'function'
    ) {
      // Convert Timestamp to JavaScript Date
      result[key] = value.toDate();
    }
    // Recursively process nested objects
    else if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = convertTimestampFields(value);
    }
    // Recursively process arrays
    else if (Array.isArray(value)) {
      result[key] = value.map(item =>
        item && typeof item === 'object' ? convertTimestampFields(item) : item
      );
    }
  });

  return result;
};

/**
 * Encodes a JSON object to base64 string (URL-safe)
 *
 * @param data - Any JSON-serializable object
 * @returns URL-safe base64 encoded string
 *
 * @example
 * const encoded = base64EncodeJson({ name: "John", age: 30 });
 * // Returns URL-safe base64 string
 */
export function base64EncodeJson(data: any): string {
  // Convert to base64
  const base64 = Buffer.from(JSON.stringify(data)).toString('base64');
  // Convert to base64url format (URL-safe)
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Decodes a base64 string back to a JSON object
 *
 * @param base64String - URL-safe base64 encoded string
 * @returns Decoded JSON object
 * @throws Will throw an error if the string is invalid or not proper JSON
 *
 * @example
 * const decoded = base64DecodeJson("eyJuYW1lIjoiSm9obiIsImFnZSI6MzB9");
 * // Returns { name: "John", age: 30 }
 */
export function base64DecodeJson<T = any>(base64String: string): T {
  try {
    // Replace URL-safe characters back to base64 standard
    let base64 = base64String.replace(/-/g, '+').replace(/_/g, '/');

    // Pad with '=' if necessary
    while (base64.length % 4) {
      base64 += '=';
    }

    // Decode and parse
    return JSON.parse(Buffer.from(base64, 'base64').toString());
  } catch (error: any) {
    console.error('Error decoding base64 JSON:', error);
    throw new Error('Invalid base64 string or JSON format');
  }
}
