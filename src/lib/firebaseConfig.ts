/**
 * Firebase Configuration Module
 *
 * This module initializes and exports Firebase services for use throughout the application.
 * It handles the Firebase app initialization and provides access to Authentication,
 * Firestore database, and Storage services. Includes App Check initialization.
 *
 * @module firebaseConfig
 */
import { initializeApp, getApps, getApp, FirebaseApp } from 'firebase/app';
import {
  initializeAppCheck,
  ReCaptchaEnterpriseProvider,
  AppCheck,
  getLimitedUseToken as getFirebaseLimitedUseToken, // Rename to avoid conflict
  getToken as getFirebaseAppCheckToken,
} from 'firebase/app-check';
import { getAuth } from 'firebase/auth';

/**
 * Firebase configuration object containing credentials and settings
 * All values are loaded from environment variables for security
 *
 * @remarks
 * - All environment variables must be prefixed with NEXT_PUBLIC_ to be accessible client-side
 * - These values should be set in .env.local for development and in deployment environment
 */
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// --- Firebase App Initialization ---
const app: FirebaseApp = !getApps().length ? initializeApp(firebaseConfig) : getApp();
console.log('[Firebase] App initialized.');

// --- App Check Initialization ---
let appCheckInstance: AppCheck | null = null;

/**
 * Initializes Firebase App Check based on environment variables.
 * Should be called once, client-side (e.g., in Providers.tsx).
 */
export const initializeFirebaseAppCheck = () => {
  if (typeof window === 'undefined') {
    console.log('[App Check] Skipping initialization (not in browser).');
    return;
  }
  // Avoid re-initialization
  if (appCheckInstance) {
    // console.log('[App Check] Already initialized. Skipping.'); // Less noise
    return;
  }

  console.log('[App Check] Attempting initialization...');

  // Use the explicit environment variable for debug mode detection
  const isDebugEnv = process.env.NEXT_PUBLIC_APP_CHECK_DEBUG_MODE === 'true';

  const recaptchaKey = process.env.NEXT_PUBLIC_RECAPTCHA_ENTERPRISE_SITE_KEY;

  console.log(
    `[App Check] Mode: ${isDebugEnv ? 'DEBUG (based on NEXT_PUBLIC_APP_CHECK_DEBUG_MODE)' : 'PRODUCTION'}`
  );
  console.log(`[App Check] ReCaptcha Key Env Var: ${recaptchaKey ? 'SET' : 'NOT SET'}`);

  try {
    // --- Debug Mode: Use the `true` flag ---
    // This tells the SDK to generate and log a token in the console.
    // The developer MUST copy this token and register it in Firebase Console.
    if (isDebugEnv) {
      console.log(
        '[App Check] Setting self.FIREBASE_APPCHECK_DEBUG_TOKEN = true based on environment variable.'
      );
      // @ts-expect-error - Set the global variable for the SDK
      self.FIREBASE_APPCHECK_DEBUG_TOKEN = true;
      // We still initialize AppCheck, but the SDK uses the debug flow due to the flag.
    }

    // --- Initialize with Provider (Required for both modes) ---
    // If debug flag is true, SDK uses it. If false, it uses ReCaptcha.
    if (recaptchaKey) {
      console.log('[App Check] Initializing with ReCaptchaEnterpriseProvider.');
      const provider = new ReCaptchaEnterpriseProvider(recaptchaKey);
      appCheckInstance = initializeAppCheck(app, {
        provider: provider,
        isTokenAutoRefreshEnabled: true,
      });
      console.log('[App Check] Initialized with Provider. Instance is set.');
    } else {
      console.error(
        '[App Check Error] NEXT_PUBLIC_RECAPTCHA_ENTERPRISE_SITE_KEY is missing. Cannot initialize App Check Provider.'
      );
      appCheckInstance = null;
    }
  } catch (error) {
    console.error('[App Check Error] Unexpected error during initialization:', error);
    appCheckInstance = null;
  }

  // Log final state
  if (appCheckInstance) {
    console.log(
      `[App Check] Setup complete. App Check instance is ${isDebugEnv ? 'in debug mode (using console token)' : 'in production mode'}.`
    );
  } else {
    console.error('[App Check] Setup failed or skipped. App Check will not function.');
  }
};

// --- Export Firebase Services ---
export const auth = getAuth(app);

/**
 * Gets a limited-use App Check token (for replay protection).
 * The SDK handles whether it's a debug or production token based on initialization.
 * @returns The limited-use App Check token string or null if unavailable/error.
 */
export const getLimitedUseAppCheckToken = async (): Promise<string | null> => {
  if (!appCheckInstance) {
    console.warn(
      '[App Check] getLimitedUseAppCheckToken called, but App Check is not initialized.'
    );
    return null;
  }
  try {
    const tokenResponse = await getFirebaseLimitedUseToken(appCheckInstance);
    // console.log('[App Check] Limited-use token retrieved.'); // Reduce logging noise
    return tokenResponse.token;
  } catch (error) {
    console.error('[App Check Error] Error getting limited-use token:', error);
    return null;
  }
};

/**
 * Gets the current App Check token (standard use).
 * The SDK handles whether it's a debug or production token based on initialization.
 * @param forceRefresh - Whether to force a token refresh.
 * @returns The App Check token string or null if unavailable/error.
 */
export const getAppCheckToken = async (forceRefresh = false): Promise<string | null> => {
  if (!appCheckInstance) {
    console.warn('[App Check] getAppCheckToken called, but App Check is not initialized.');
    return null;
  }
  try {
    const tokenResponse = await getFirebaseAppCheckToken(appCheckInstance, forceRefresh);
    // console.log('[App Check] Standard token retrieved.'); // Reduce logging noise
    return tokenResponse.token;
  } catch (error) {
    console.error('[App Check Error] Error getting standard token:', error);
    return null;
  }
};
