/* eslint-disable no-console */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

const CURRENT_LOG_LEVEL = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG;

// ANSI escape codes for server-side colors (same as Option 1)
const SERVER_RESET = '\x1b[0m';
const SERVER_RED = '\x1b[31m';
const SERVER_GREEN = '\x1b[32m';
const SERVER_YELLOW = '\x1b[33m';
const SERVER_BLUE = '\x1b[34m';
const SERVER_MAGENTA = '\x1b[35m';
const SERVER_CYAN = '\x1b[36m';
const SERVER_BOLD = '\x1b[1m';

function getServerFormattedMessage(
  level: LogLevel,
  context: string,
  message: string,
  data?: Record<string, any>
): string {
  const timestamp = new Date().toISOString();
  let levelString = LogLevel[level];
  let colorStart = '';

  switch (level) {
    case LogLevel.DEBUG:
      colorStart = SERVER_CYAN;
      levelString = `${SERVER_CYAN}${levelString}${SERVER_RESET}`;
      break;
    case LogLevel.INFO:
      colorStart = SERVER_GREEN;
      levelString = `${SERVER_GREEN}${levelString}${SERVER_RESET}`;
      break;
    case LogLevel.WARN:
      colorStart = SERVER_YELLOW;
      levelString = `${SERVER_BOLD}${SERVER_YELLOW}${levelString}${SERVER_RESET}`;
      break;
    case LogLevel.ERROR:
      colorStart = SERVER_RED;
      levelString = `${SERVER_BOLD}${SERVER_RED}${levelString}${SERVER_RESET}`;
      break;
  }

  let logEntry = `${SERVER_MAGENTA}${timestamp}${SERVER_RESET} [${levelString}] [${SERVER_BLUE}${context}${SERVER_RESET}] ${colorStart}${message}${SERVER_RESET}`;

  if (data) {
    try {
      const dataString = JSON.stringify(
        data,
        (_key, value) => (typeof value === 'bigint' ? value.toString() : value),
        2
      );
      logEntry += `\nData: ${colorStart}${dataString}${SERVER_RESET}`;
    } catch (e) {
      logEntry += `\nData: [Could not serialize data: ${(e as Error).message}]`;
    }
  }
  return logEntry;
}

export function logServer(
  level: LogLevel,
  context: string,
  message: string,
  data?: Record<string, any>
): void {
  if (level >= CURRENT_LOG_LEVEL) {
    const formattedMessage = getServerFormattedMessage(level, context, message, data);
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage);
        break;
    }
  }
}

export function logClient(
  level: LogLevel,
  context: string,
  message: string,
  data?: Record<string, any>
): void {
  if (typeof window === 'undefined' || level < CURRENT_LOG_LEVEL) {
    return;
  }

  const timestamp = new Date().toISOString();
  const levelString = LogLevel[level];

  const style = 'font-weight: bold; padding: 2px 4px; border-radius: 3px;';
  let levelStyle = style;
  const contextStyle = 'color: navy; font-weight: normal;';
  let messageStyle = '';

  switch (level) {
    case LogLevel.DEBUG:
      levelStyle += 'background: #e0e0e0; color: #333;'; // Light gray background, dark text
      messageStyle = 'color: #555;'; // Dimmer text
      break;
    case LogLevel.INFO:
      levelStyle += 'background: #d1e7dd; color: #0f5132;'; // Light green background, dark green text
      messageStyle = 'color: #0f5132;';
      break;
    case LogLevel.WARN:
      // Browser default often good enough, but can customize
      levelStyle += 'background: #fff3cd; color: #664d03; font-weight: bold;';
      messageStyle = 'color: #664d03;';
      // For warn and error, we can let the browser do its thing for the main message,
      // or pass the message and data separately.
      if (data)
        console.warn(
          `%c${timestamp}%c [%c${levelString}%c] [%c${context}%c] ${message}`,
          'color: magenta;',
          '',
          levelStyle,
          '',
          contextStyle,
          '',
          data
        );
      else
        console.warn(
          `%c${timestamp}%c [%c${levelString}%c] [%c${context}%c] ${message}`,
          'color: magenta;',
          '',
          levelStyle,
          '',
          contextStyle,
          ''
        );
      return; // Return early for warn/error if using default browser styling primarily
    case LogLevel.ERROR:
      levelStyle += 'background: #f8d7da; color: #58151c; font-weight: bold;';
      messageStyle = 'color: #58151c; font-weight: bold;';
      if (data)
        console.error(
          `%c${timestamp}%c [%c${levelString}%c] [%c${context}%c] ${message}`,
          'color: magenta;',
          '',
          levelStyle,
          '',
          contextStyle,
          '',
          data
        );
      else
        console.error(
          `%c${timestamp}%c [%c${levelString}%c] [%c${context}%c] ${message}`,
          'color: magenta;',
          '',
          levelStyle,
          '',
          contextStyle,
          ''
        );
      return;
  }

  // For DEBUG and INFO, use console.log with %c for custom styling
  if (data) {
    console.log(
      `%c${timestamp}%c [%c${levelString}%c] [%c${context}%c] %c${message}`,
      'color: magenta;', // Timestamp
      '', // Space
      levelStyle, // Level
      '', // Space
      contextStyle, // Context
      '', // Space
      messageStyle, // Message
      data // Data object
    );
  } else {
    console.log(
      `%c${timestamp}%c [%c${levelString}%c] [%c${context}%c] %c${message}`,
      'color: magenta;',
      '',
      levelStyle,
      '',
      contextStyle,
      '',
      messageStyle
    );
  }
}
/* eslint-enable no-console */
