import { ERROR_MESSAGES, APP_CHECK_RETRY_CONFIG } from './constants';
import { getAppCheckToken, getLimitedUseAppCheckToken } from './firebaseConfig';

interface FetchOptions extends RequestInit {
  useLimitedUseToken?: boolean;
}

/**
 * Wraps the native fetch function to automatically include Firebase App Check tokens.
 * Retries with exponential backoff if token acquisition fails.
 * Relies on the Firebase JS SDK to provide the correct token (production or debug).
 *
 * @param url - The URL to fetch.
 * @param options - Optional fetch options (RequestInit). Can include `useLimitedUseToken: true`.
 * @returns The fetch Response promise.
 * @throws Throws an error if App Check token cannot be obtained after retries.
 */
export async function fetchWithAppCheck(
  url: string,
  options: FetchOptions = {}
): Promise<Response> {
  // Destructure headers out from the rest of the options
  const { headers: originalHeaders, ...restOptions } = options;
  const headers = new Headers(originalHeaders as HeadersInit); // Create Headers object safely

  const getTokenFunc = options.useLimitedUseToken ? getLimitedUseAppCheckToken : getAppCheckToken;
  let appCheckToken: string | null = null;
  let lastError: any = null;

  for (let attempt = 1; attempt <= APP_CHECK_RETRY_CONFIG.MAX_RETRIES; attempt++) {
    // USE IMPORTED CONSTANT
    try {
      appCheckToken = await getTokenFunc();
      if (appCheckToken) {
        // console.log(`[fetchWithAppCheck] App Check token obtained on attempt ${attempt} for ${url}.`); // Optional debug log
        lastError = null; // Reset last error on success
        break; // Token obtained, exit retry loop
      }
      lastError = new Error('App Check token was null (getTokenFunc returned null).');
      console.warn(
        `[fetchWithAppCheck] App Check token not available for ${url} (attempt ${attempt}/${APP_CHECK_RETRY_CONFIG.MAX_RETRIES}). Null returned by getTokenFunc.` // USE IMPORTED CONSTANT
      );
    } catch (error) {
      lastError = error;
      console.warn(
        `[fetchWithAppCheck] Error during App Check token acquisition for ${url} (attempt ${attempt}/${APP_CHECK_RETRY_CONFIG.MAX_RETRIES}):`, // USE IMPORTED CONSTANT
        error
      );
    }

    if (!appCheckToken && attempt < APP_CHECK_RETRY_CONFIG.MAX_RETRIES) {
      // USE IMPORTED CONSTANT
      const delay = Math.min(
        APP_CHECK_RETRY_CONFIG.INITIAL_BACKOFF_MS *
          Math.pow(APP_CHECK_RETRY_CONFIG.BACKOFF_FACTOR, attempt - 1), // USE IMPORTED CONSTANTS
        APP_CHECK_RETRY_CONFIG.MAX_BACKOFF_MS // USE IMPORTED CONSTANT
      );
      console.warn(
        `[fetchWithAppCheck] Retrying App Check token acquisition for ${url} in ${delay}ms...`
      );
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  if (!appCheckToken) {
    console.error(
      `[fetchWithAppCheck] Failed to obtain App Check token for ${url} after ${APP_CHECK_RETRY_CONFIG.MAX_RETRIES} attempts. Last error:`, // USE IMPORTED CONSTANT
      lastError
    );
    throw new Error(ERROR_MESSAGES.APP_CHECK_TOKEN_MISSING);
  }

  headers.set('X-Firebase-AppCheck', appCheckToken);

  return fetch(url, {
    ...restOptions,
    headers,
  });
}

// ... (rest of the apiPost, apiGet, apiPutFormData, apiDelete functions remain unchanged)
// They don't directly use these constants, only fetchWithAppCheck does.

/**
 * Helper for making POST requests with App Check.
 * @param url - The URL to POST to.
 * @param body - The request body (will be JSON.stringify'd).
 * @param options - Optional fetch options.
 * @returns The fetch Response promise.
 */
export const apiPost = async (url: string, body: any, options: FetchOptions = {}) => {
  // console.info('[apiPost] Received options:', options); // Keep info log if needed

  // Separate headers from the rest of the incoming options to avoid overwriting Content-Type
  const { headers: incomingHeaders, ...restIncomingOptions } = options;

  // Construct the final headers, ensuring 'content-type' is present and merging incoming headers
  const finalHeaders = {
    'content-type': 'application/json', // Set default Content-Type first
    ...(incomingHeaders || {}), // Merge incoming headers (like Authorization)
  };

  // Construct the final options object explicitly
  const finalOptions: FetchOptions = {
    method: 'POST',
    headers: finalHeaders, // Use the explicitly constructed headers object
    body: JSON.stringify(body),
    ...restIncomingOptions, // Spread the *rest* of the incoming options (e.g., useLimitedUseToken)
  };

  // console.info('[apiPost] Final options before fetchWithAppCheck:', finalOptions); // Keep info log if needed

  return fetchWithAppCheck(url, finalOptions);
};

/**
 * Helper for making GET requests with App Check.
 * @param url - The URL to GET from.
 * @param options - Optional fetch options.
 * @returns The fetch Response promise.
 */
export const apiGet = async (url: string, options: FetchOptions = {}) => {
  // Separate headers for clarity, though less critical for GET
  const { headers: incomingHeaders, ...restIncomingOptions } = options;
  const finalHeaders = {
    ...(incomingHeaders || {}),
  };

  const finalOptions: FetchOptions = {
    method: 'GET',
    headers: finalHeaders,
    ...restIncomingOptions,
  };
  // console.info('[apiGet] Final options before fetchWithAppCheck:', finalOptions); // Keep info log if needed

  return fetchWithAppCheck(url, finalOptions);
};

/**
 * Helper for making PUT requests with App Check (specifically for FormData).
 * @param url - The URL to PUT to.
 * @param formData - The FormData object.
 * @param authToken - The Firebase Auth token (required for PUT).
 * @param options - Optional fetch options.
 * @returns The fetch Response promise.
 */
export const apiPutFormData = async (
  url: string,
  formData: FormData,
  authToken: string,
  options: FetchOptions = {}
) => {
  // Separate headers from the rest of the incoming options
  const { headers: incomingHeaders, ...restIncomingOptions } = options;

  // Create Headers object from incoming headers
  const finalHeaders = new Headers(incomingHeaders as HeadersInit);

  // Add Authorization header
  finalHeaders.set('Authorization', `Bearer ${authToken}`);

  // !! IMPORTANT: DO NOT set Content-Type for FormData.
  // The browser MUST set it automatically along with the boundary.
  if (finalHeaders.has('Content-Type')) {
    console.warn('[apiPutFormData] Removing explicit Content-Type header for FormData.');
    finalHeaders.delete('Content-Type');
  }

  // Construct final options
  const finalOptions: FetchOptions = {
    method: 'PUT',
    headers: finalHeaders, // Pass the modified Headers object
    body: formData,
    ...restIncomingOptions,
  };
  // console.info('[apiPutFormData] Final options before fetchWithAppCheck:', { // Keep info log if needed
  //   ...finalOptions,
  //   body: '[FormData object]', // Don't log the entire FormData body
  // });

  return fetchWithAppCheck(url, finalOptions);
};

// Add apiDelete etc. as needed
export const apiDelete = async (url: string, options: FetchOptions = {}) => {
  const { headers: incomingHeaders, ...restIncomingOptions } = options;
  const finalHeaders = {
    ...(incomingHeaders || {}),
  };
  const finalOptions: FetchOptions = {
    method: 'DELETE',
    headers: finalHeaders,
    ...restIncomingOptions,
  };
  // console.info('[apiDelete] Final options before fetchWithAppCheck:', finalOptions); // Keep info log if needed
  return fetchWithAppCheck(url, finalOptions);
};
