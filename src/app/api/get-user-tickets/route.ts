import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

/**
 * Handles GET requests to retrieve all tickets associated with a specific user
 *
 * @param {Request} req - The incoming HTTP request with userId as query parameter
 * @returns {Promise<Response>} JSON response with tickets including user, event and schedule details
 *
 * Success Response (200):
 * - tickets: Array of ticket objects with user, event and schedule details
 *
 * Error Responses:
 * - 400: Missing user ID
 * - 404: User not found
 * - 500: Server error during processing
 */
export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract userId from URL query parameters
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    // Validate that userId is provided
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Create reference to user document and verify it exists
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
    const userDoc = await userRef.get();

    // Return 404 if user doesn't exist
    if (!userDoc.exists) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Transform user data to include document ID
    const userData = {
      id: userDoc.id,
      ...userDoc.data(),
    } as Users;

    // Check waiver status for the user
    const currentDate = getPacificTimeNow();
    currentDate.setHours(0, 0, 0, 0);
    const waiversRef = adminDb.collection(COLLECTIONS.WAIVERS);

    let userSignedWaiver = false;
    if (userData.email) {
      const waiverQuery = waiversRef
        .where('email', '==', userData.email)
        .where('valid_until', '>', currentDate);
      const waiverSnapshot = await waiverQuery.get();
      userSignedWaiver = !waiverSnapshot.empty;
    }

    // Query tickets collection for all tickets associated with this user
    const ticketsRef = adminDb.collection(COLLECTIONS.TICKETS);
    const ticketsQuery = ticketsRef
      .where('user_id', '==', userRef)
      .where('status', 'not-in', ['cancelled', 'draft']);
    const ticketsSnapshot = await ticketsQuery.get();

    // Return empty tickets array if no tickets found
    if (ticketsSnapshot.empty) {
      return NextResponse.json(
        {
          tickets: [],
          message: 'No tickets found for user',
        },
        { status: 200 }
      );
    }

    // Process each ticket to include its associated event, schedule, and user details
    const tickets = await Promise.all(
      ticketsSnapshot.docs.map(async ticketDoc => {
        const ticketData = ticketDoc.data();

        // Fetch and transform event data for each ticket
        let eventRef;
        if (typeof ticketData.event_id === 'string') {
          eventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(ticketData.event_id);
        } else {
          eventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(ticketData.event_id.id);
        }
        const eventDoc = await eventRef.get();
        const eventData = eventDoc.exists ? { id: eventDoc.id, ...eventDoc.data() } : null;

        // Fetch and transform schedule data for each ticket
        let scheduleData = null;
        if (eventDoc.exists && ticketData.event_schedule_id) {
          const scheduleId =
            typeof ticketData.event_schedule_id === 'string'
              ? ticketData.event_schedule_id
              : ticketData.event_schedule_id.id;

          const scheduleRef = adminDb
            .collection(COLLECTIONS.EVENTS)
            .doc(eventDoc.id)
            .collection(COLLECTIONS.EVENT_SCHEDULES)
            .doc(scheduleId);
          const scheduleDoc = await scheduleRef.get();
          scheduleData = scheduleDoc.exists
            ? { id: scheduleDoc.id, ...(scheduleDoc.data() as EventSchedules) }
            : null;
        }

        // Include user data with signed_waiver status
        const userWithWaiver = {
          ...userData,
          signed_waiver: userSignedWaiver,
        };

        // Get order information to find other tickets in the same order
        const orderRef =
          typeof ticketData.order_id === 'string'
            ? adminDb.collection(COLLECTIONS.ORDERS).doc(ticketData.order_id)
            : adminDb.collection(COLLECTIONS.ORDERS).doc(ticketData.order_id.id);
        const orderDoc = await orderRef.get();
        const orderData = orderDoc.exists ? orderDoc.data() : null;

        // Get all tickets for the same order (excluding current ticket)
        const guests: (Users & {
          signed_waiver: boolean;
          ticket_id: string;
          ticket_number: string;
          status: string;
        })[] = [];
        let unsignedWaivers = 0; // Initialize counter for unsigned waivers

        if (orderRef) {
          const ticketsForOrderQuery = ticketsRef.where('order_id', '==', orderRef);
          const ticketsForOrderSnapshot = await ticketsForOrderQuery.get();

          // Process each ticket in the order to get guest information
          for (const doc of ticketsForOrderSnapshot.docs) {
            // Skip the current ticket
            if (doc.id === ticketDoc.id) continue;

            const guestTicketData = doc.data();
            const guestUserRef =
              typeof guestTicketData.user_id === 'string'
                ? adminDb.collection(COLLECTIONS.USERS).doc(guestTicketData.user_id)
                : adminDb.collection(COLLECTIONS.USERS).doc(guestTicketData.user_id.id);
            const guestUserDoc = await guestUserRef.get();

            if (guestUserDoc.exists) {
              const guestUserData = guestUserDoc.data() as Users;
              let guestSignedWaiver = false;

              // Check waiver status for guest
              if (guestUserData.age_group === 'child') {
                // For children, get the booker's email from the order
                const orderByRef =
                  typeof orderData?.order_by === 'string'
                    ? adminDb.collection(COLLECTIONS.USERS).doc(orderData.order_by)
                    : adminDb.collection(COLLECTIONS.USERS).doc(orderData?.order_by?.id);
                const orderByDoc = await orderByRef.get();
                const bookerEmail = orderByDoc?.data()?.email;

                if (bookerEmail) {
                  // Check child waiver using booker's email and child's name
                  const guestWaiverQuery = waiversRef
                    .where('email', '==', bookerEmail)
                    .where('first_name', '==', guestUserData.first_name)
                    .where('last_name', '==', guestUserData.last_name)
                    .where('valid_until', '>', currentDate);
                  const guestWaiverSnapshot = await guestWaiverQuery.get();
                  guestSignedWaiver = !guestWaiverSnapshot.empty;
                }
              } else if (guestUserData.email) {
                // For adults, check waiver using their own email
                const guestWaiverQuery = waiversRef
                  .where('email', '==', guestUserData.email)
                  .where('valid_until', '>', currentDate);
                const guestWaiverSnapshot = await guestWaiverQuery.get();
                guestSignedWaiver = !guestWaiverSnapshot.empty;
              }

              // Increment unsigned waiver count if needed
              if (!guestSignedWaiver) {
                unsignedWaivers++;
              }

              guests.push({
                ...guestUserData,
                signed_waiver: guestSignedWaiver,
                ticket_id: doc.id,
                ticket_number: guestTicketData.ticket_id || '',
                status: guestTicketData.status || '',
              });
            }
          }
        }

        // After processing all guests, check if the booker hasn't signed a waiver
        // and include that in the unsigned_waivers count
        if (!userSignedWaiver) {
          unsignedWaivers++;
        }

        // Return complete ticket object with all associated data
        return {
          id: ticketDoc.id,
          ...ticketData,
          user: userWithWaiver,
          event: eventData,
          schedule: scheduleData,
          guests: guests,
          unsigned_waivers: unsignedWaivers, // This now includes the booker if they haven't signed
          order: orderData,
        } as Tickets & {
          user: Users & { signed_waiver: boolean };
          event: Events | null;
          schedule: EventSchedules | null;
          guests: (Users & {
            signed_waiver: boolean;
            ticket_id: string;
            ticket_number: string;
            status: string;
          })[];
          unsigned_waivers: number;
        };
      })
    );

    // Return successful response with tickets
    return NextResponse.json(
      {
        tickets,
        message: 'Tickets retrieved successfully',
      },
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    // Log error and return 500 response with error details
    console.error('Error fetching user tickets:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch tickets',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
