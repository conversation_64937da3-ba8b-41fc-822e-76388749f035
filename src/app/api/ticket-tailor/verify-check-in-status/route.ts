import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { searchParams } = new URL(req.url);
    const ticketId = searchParams.get('ticketId');

    if (!ticketId) {
      return NextResponse.json({ error: 'Please provide a ticket ID' }, { status: 400 });
    }

    // Get main ticket details
    const ticketRef = adminDb.collection(COLLECTIONS.TICKETS).doc(ticketId);
    const ticketDoc = await ticketRef.get();

    if (!ticketDoc.exists) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Update the ticket to set verify_check_in to true
    await ticketRef.update({
      verify_check_in: true,
    });

    return NextResponse.json({ status: 'Status updated successfully' });
  } catch (error) {
    console.error('Check-in status error:', error);
    return NextResponse.json({ error: 'Failed to verify check-in status' }, { status: 500 });
  }
}
