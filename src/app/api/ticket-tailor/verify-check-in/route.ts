import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { checkInTicketTailorTicket } from '@/src/app/api/ticket-tailor/_ticketTailerService';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const { ticketId, passcode, selectedGuestIds } = await req.json();

    if (!ticketId || !passcode) {
      return NextResponse.json(
        { error: 'Please provide both ticket ID and check-in code' },
        { status: 400 }
      );
    }

    // Get main ticket details
    const ticketRef = adminDb.collection(COLLECTIONS.TICKETS).doc(ticketId);
    const ticketDoc = await ticketRef.get();

    if (!ticketDoc.exists) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    const ticketData = ticketDoc.data();

    // Check event date
    // const currentDate = getPacificTimeNow();
    // const eventDate = new Date(ticketData?.event_details.start_date.date);

    // // Set both dates to start of day for comparison
    // currentDate.setHours(0, 0, 0, 0);
    // eventDate.setHours(0, 0, 0, 0);

    // if (currentDate < eventDate) {
    //   return NextResponse.json({ error: ERROR_MESSAGES.CHECK_IN_TOO_EARLY }, { status: 400 });
    // }

    // if (currentDate > eventDate) {
    //   return NextResponse.json({ error: 'You are trying to Check-In too late.' }, { status: 400 });
    // }

    // // Check if user is trying to check in too early (more than 30 min before start time)
    // if (ticketData?.event_details?.start_date?.time) {
    //   const currentTime = getPacificTimeNow();
    //   const [hours, minutes] = ticketData.event_details.start_date.time.split(':').map(Number);
    //   const eventStartTime = new Date(eventDate);
    //   eventStartTime.setHours(hours, minutes, 0, 0);

    //   // Calculate time difference in minutes
    //   const timeDiffInMinutes = (eventStartTime.getTime() - currentTime.getTime()) / (1000 * 60);

    //   // If trying to check in more than 30 minutes before start time
    //   if (timeDiffInMinutes > 30) {
    //     return NextResponse.json(
    //       { error: 'Check-in is only available 30 minutes before the event starts' },
    //       { status: 400 }
    //     );
    //   }
    // }

    // Get event schedule to verify passcode
    const eventRef = adminDb.collection(COLLECTIONS.CHECK_IN_PASSCODE).get();
    const scheduleDoc = await (await eventRef).docs[0].data();

    // Verify passcode
    if (scheduleDoc.passcode !== passcode) {
      return NextResponse.json(
        { error: 'Invalid check-in code. Please try again.' },
        { status: 400 }
      );
    }

    // Prepare batch write
    const batch = adminDb.batch();
    const mainTicketId = ticketData?.ticket_id;

    // Update main ticket
    batch.update(ticketRef, {
      check_in: [{ status: true, timestamp: getPacificTimeNow() }],
      checked_in_at: Timestamp.fromDate(getPacificTimeNow()),
    });

    // Process guest tickets if needed
    const guestPromises: Promise<unknown>[] = [];

    if (selectedGuestIds?.length > 0) {
      // Get all guest tickets for the order
      const orderRef = ticketData?.order_id;
      const ticketsQuery = adminDb
        .collection(COLLECTIONS.TICKETS)
        .where('order_id', '==', orderRef);
      const ticketsSnapshot = await ticketsQuery.get();

      // Process selected guest tickets
      ticketsSnapshot.forEach(doc => {
        if (selectedGuestIds.includes(doc.id)) {
          const guestTicketData = doc.data();
          const guestTicketId = guestTicketData.ticket_id;

          // Add to Firestore batch update
          batch.update(doc.ref, {
            check_in: [{ status: true, timestamp: getPacificTimeNow() }],
            checked_in_at: Timestamp.fromDate(getPacificTimeNow()),
          });

          // Add API call to promises if ticket ID exists
          if (guestTicketId) {
            guestPromises.push(
              checkInTicketTailorTicket(guestTicketId).catch(error => {
                console.error(`Failed to check in guest ticket ${guestTicketId}:`, error);
                return null; // Prevent Promise.all from failing
              })
            );
          }
        }
      });
    }

    // Commit all Firestore updates
    await batch.commit();

    // Check in main ticket with Ticket Tailor API
    guestPromises.push(
      checkInTicketTailorTicket(mainTicketId).catch(error => {
        console.error(`Failed to check in main ticket ${mainTicketId}:`, error);
        return null; // Prevent Promise.all from failing
      })
    );

    // Execute all API calls in parallel (don't block response)
    Promise.all(guestPromises).catch(error => {
      console.error('Error in batch check-in operations:', error);
    });

    return NextResponse.json({
      success: true,
      message: 'Check-in successful',
    });
  } catch (error) {
    console.error('Check-in error:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.INVALID_CHECK_IN }, { status: 500 });
  }
}
