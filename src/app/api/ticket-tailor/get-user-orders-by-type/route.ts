import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';
import { Orders } from '@/src/types/Orders';

/**
 * Handles GET requests to retrieve user orders filtered by item_type
 */
export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract parameters from URL query
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const itemType = searchParams.get('itemType');

    // Validate required parameters
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Create reference to user document and verify it exists
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
    const userDoc = await userRef.get();

    // Return 404 if user doesn't exist
    if (!userDoc.exists) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Query orders collection for orders with matching user
    const ordersRef = adminDb.collection(COLLECTIONS.ORDERS);

    // Create query based on whether itemType is provided
    const ordersQuery = itemType
      ? ordersRef.where('order_by', '==', userRef).where('item_type', '==', itemType)
      : ordersRef.where('order_by', '==', userRef);

    const ordersSnapshot = await ordersQuery.get();

    // Return empty array if no orders found
    if (ordersSnapshot.empty) {
      const message = itemType
        ? `No ${itemType} orders found for user`
        : 'No orders found for user';

      return NextResponse.json({
        orders: [],
        message,
        status: 'success',
      });
    }

    // Transform orders data to include document ID
    const orders = ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Orders[];

    // Return successful response
    return NextResponse.json({
      orders,
      message: 'Orders retrieved successfully',
      status: 'success',
    });
  } catch (_error) {
    return NextResponse.json(
      { error: ERROR_MESSAGES.FAILED_TO_FETCH_USER_ORDERS },
      { status: 500 }
    );
  }
}
