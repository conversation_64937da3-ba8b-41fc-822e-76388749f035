/**
 * Updates ticket check-in status in Ticket Tailor API
 */
export async function checkInTicketTailorTicket(ticketId: string): Promise<unknown> {
  try {
    const apiKey = process.env.TICKET_TAILOR_API_KEY;
    const encodedAuth = btoa(apiKey as string);

    const response = await fetch('https://api.tickettailor.com/v1/check_ins', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        Authorization: `Basic ${encodedAuth}`,
        contentType: 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        quantity: '1',
        issued_ticket_id: ticketId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error(`Failed to check in ticket ${ticketId}:`, errorData);
      throw new Error(`Ticket Tailor API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    console.error(`Error checking in ticket ${ticketId}:`, error);
    throw error;
  }
}
