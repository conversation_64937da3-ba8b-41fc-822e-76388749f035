import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES, TICKET_TYPES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { getTicketType } from '@/src/lib/utils';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

/**
 * Handles GET requests to retrieve all tickets and associated user information for a specific order
 *
 * @param {Request} req - The incoming HTTP request with orderId as query parameter
 * @returns {Promise<Response>} JSON response with tickets including user, event and schedule details
 *
 * Success Response (200):
 * - tickets: Array of ticket objects with user, event and schedule details
 * - order: Complete order object with order_by user details
 *
 * Error Responses:
 * - 400: Missing order ID
 * - 404: Order not found or no tickets found
 * - 500: Server error during processing
 */
export const GET = async (req: NextRequest) => {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract orderId from URL query parameters
    const { searchParams } = new URL(req.url);
    const orderId = searchParams.get('orderId');

    // Validate that orderId is provided
    if (!orderId) {
      return NextResponse.json(
        {
          error: 'Order ID is required',
          status: 'error',
        },
        { status: 400 }
      );
    }

    // Create reference to order document and verify it exists
    const orderRef = adminDb.collection(COLLECTIONS.ORDERS).doc(orderId);
    const orderDoc = await orderRef.get();

    // Return 404 if order doesn't exist
    if (!orderDoc.exists) {
      return NextResponse.json(
        {
          error: 'Order not found',
          status: 'error',
        },
        { status: 404 }
      );
    }

    const orderData = orderDoc.data();

    if (!orderData) {
      return NextResponse.json(
        {
          error: 'Order data is empty',
          status: 'error',
        },
        { status: 404 }
      );
    }

    // Fetch the order_by user details
    const orderByUserRef = adminDb.collection(COLLECTIONS.USERS).doc(orderData.order_by.id);
    const orderByUserDoc = await orderByUserRef.get();
    const orderByUserData = orderByUserDoc.exists ? orderByUserDoc.data() : null;

    // Query tickets collection for all tickets associated with this order
    const ticketsQuery = adminDb.collection(COLLECTIONS.TICKETS).where('order_id', '==', orderRef);
    const ticketsSnapshot = await ticketsQuery.get();

    // Return empty array if no tickets found
    if (ticketsSnapshot.empty) {
      return NextResponse.json(
        {
          tickets: [],
          order: {
            ...orderData,
            order_by: orderByUserData,
          },
          message: 'No tickets found for order',
          status: 'success',
        },
        { status: 200 }
      );
    }

    // Check waiver status for the booker
    const currentDate = getPacificTimeNow();
    currentDate.setHours(0, 0, 0, 0);
    const waiversRef = adminDb.collection(COLLECTIONS.WAIVERS);

    // Get all tickets and filter for children without waivers
    const tickets = await Promise.all(
      ticketsSnapshot.docs.map(async ticketDoc => {
        const ticketData = ticketDoc.data();

        if (!ticketData) return null;

        // Check if this is a child ticket that belongs to the booker
        // We want to skip adult tickets and only process child tickets without waivers
        const ticketType = getTicketType(ticketData.ticket_type);
        const isAdultTicket = ticketType === TICKET_TYPES.ADULT;
        const isChildTicket = ticketType === TICKET_TYPES.CHILD;
        const isBookerTicket =
          ticketData.user_details?.email?.toLowerCase() === orderByUserData?.email?.toLowerCase() &&
          ticketData.user_details.first_name?.toLowerCase() ===
            orderByUserData?.first_name?.toLowerCase() &&
          ticketData.user_details.last_name?.toLowerCase() ===
            orderByUserData?.last_name?.toLowerCase();

        if (!isChildTicket && !(isAdultTicket && isBookerTicket)) {
          return null;
        }

        // Check if child has a valid waiver
        const childWaiverQuery = waiversRef
          .where('email', '==', ticketData.user_details?.email ?? orderByUserData?.email)
          .where('first_name', '==', ticketData.user_details?.first_name)
          .where('last_name', '==', ticketData.user_details?.last_name)
          .where('valid_until', '>', currentDate);
        const childWaiverSnapshot = await childWaiverQuery.get();

        // If child has a waiver, skip
        if (!childWaiverSnapshot.empty) {
          return null;
        }

        // Return ticket data only for children without waivers
        return {
          id: ticketDoc.id,
          ...ticketData,
        } as Tickets & {
          user: Users | null;
        };
      })
    );

    // Filter out null values
    const filteredTickets = tickets.filter(
      (ticket): ticket is Tickets & { user: Users | null } => ticket !== null
    );

    // Return successful response with tickets and order (including order_by user)
    return NextResponse.json(
      {
        tickets: filteredTickets,
        order: {
          ...orderData,
          organizer: orderByUserData,
        },
        message: 'Tickets retrieved successfully',
        status: 'success',
      },
      { status: 200 }
    );
  } catch (error) {
    // Log error and return 500 response
    console.error('Error fetching tickets by order:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch tickets',
        details: error instanceof Error ? error.message : 'Unknown error',
        status: 'error',
      },
      { status: 500 }
    );
  }
};
