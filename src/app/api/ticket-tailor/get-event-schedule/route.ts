import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES, SUCCESS_MESSAGES, TICKET_TAILOR_ENPOINTS } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

/**
 * Handles GET requests to retrieve events from Ticket Tailor API
 *
 * @param {Request} req - The incoming HTTP request
 * @returns {Promise<Response>} JSON response with events
 *
 * Success Response (200):
 * - data: Events from Ticket Tailor
 *
 * Error Responses:
 * - 500: Server error during processing
 */
export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract limit from query parameters if provided
    const { searchParams } = new URL(req.url);
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : undefined;

    // Fetch events from Ticket Tailor API with optional limit
    const eventsData = await fetchEventsFromTicketTailor(limit);

    return NextResponse.json({
      message: SUCCESS_MESSAGES.GET_EVENT_SCHEDULE,
      data: eventsData,
    });
  } catch (error) {
    console.error('Error fetching from Ticket Tailor:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_FETCH_SCHEDULES }, { status: 500 });
  }
}

/**
 * Interface for Ticket Tailor API response
 */
interface TicketTailorResponse {
  data: any[];
  links?: {
    next?: string;
    previous?: string;
  };
}

/**
 * Fetches all events from Ticket Tailor API with pagination
 *
 * @param {number} limit - Maximum number of events to fetch (optional)
 * @returns {Promise<any>} - Promise resolving to the API response data with events
 */
async function fetchEventsFromTicketTailor(limit?: number): Promise<any> {
  const apiKey = process.env.TICKET_TAILOR_API_KEY;
  // Use Buffer for Node.js environment instead of btoa
  const encodedAuth = Buffer.from(apiKey as string).toString('base64');
  const today = getPacificTimeNow();
  today.setHours(0, 0, 0, 0);
  const currentDate = Math.floor(today.getTime() / 1000);

  let allEvents: any[] = [];
  let nextUrl = `${TICKET_TAILOR_ENPOINTS.TICKET_TAILOR_URL}/events?status=published&start_at.gte=${currentDate}`;

  while (nextUrl && (!limit || allEvents.length < limit)) {
    const response = await fetch(nextUrl, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        Authorization: `Basic ${encodedAuth}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Ticket Tailor API error: ${response.status} ${response.statusText}`);
    }

    const responseData: TicketTailorResponse = await response.json();

    // Add events from this page to our collection
    if (responseData.data && responseData.data.length > 0) {
      // If we have a limit, only add up to the limit
      if (limit) {
        const remainingSpace = limit - allEvents.length;
        const eventsToAdd = responseData.data.slice(0, remainingSpace);
        allEvents = [...allEvents, ...eventsToAdd];
      } else {
        allEvents = [...allEvents, ...responseData.data];
      }
    }

    // Stop pagination if we've reached the limit
    if (limit && allEvents.length >= limit) {
      break;
    }

    // Check if there's a next page
    if (responseData.links?.next) {
      // Convert relative URL to absolute URL
      nextUrl = `${TICKET_TAILOR_ENPOINTS.TICKET_TAILOR_URL}${responseData.links.next}`;
    } else {
      // No more pages
      nextUrl = '';
    }
  }

  allEvents = allEvents.filter(event => {
    return (process.env.NEXT_ACTIVE_EVENTS || '').includes(event.event_series_id);
  });

  // Return in the same format as the original API response
  return {
    data: allEvents,
    // We're not including links in the final response since we've fetched all pages
  };
}
