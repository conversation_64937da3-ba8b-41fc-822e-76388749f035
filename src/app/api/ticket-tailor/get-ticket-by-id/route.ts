import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES, TICKET_TYPES } from '@/src/lib/constants';
import { getTicketType } from '@/src/lib/utils';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Orders } from '@/src/types/Orders';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

/**
 * Handles GET requests to retrieve detailed ticket information
 *
 * @param {Request} req - The incoming HTTP request with ticketId as query parameter
 * @returns {Promise<Response>} JSON response with ticket details including user, event and schedule information
 */
export const GET = async (req: NextRequest) => {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract ticketId from URL query parameters
    const { searchParams } = new URL(req.url);
    const ticketId = searchParams.get('ticketId');

    // Validate that ticketId is provided
    if (!ticketId) {
      return NextResponse.json(
        {
          error: 'Ticket ID is required',
          status: 'error',
        },
        { status: 400 }
      );
    }

    // Get ticket document using Admin SDK
    const ticketRef = adminDb.collection(COLLECTIONS.TICKETS).doc(ticketId);
    const ticketDoc = await ticketRef.get();

    // Check if ticket exists
    if (!ticketDoc.exists) {
      return NextResponse.json(
        {
          error: 'Ticket not found',
          status: 'error',
        },
        { status: 404 }
      );
    }

    const ticketData = ticketDoc.data();

    // Check if ticketData exists
    if (!ticketData) {
      return NextResponse.json(
        {
          error: 'Ticket data is empty',
          status: 'error',
        },
        { status: 404 }
      );
    }

    // Fetch user data
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(ticketData.user_id.id);
    const userDoc = await userRef.get();
    const userData = userDoc.exists ? userDoc.data() : null;

    // Fetch order data
    const orderRef = adminDb.collection(COLLECTIONS.ORDERS).doc(ticketData.order_id.id);
    const orderDoc = await orderRef.get();
    const orderData = orderDoc.exists ? orderDoc.data() : null;

    // Fetch booker data
    const bookerRef = adminDb.collection(COLLECTIONS.USERS).doc(orderData?.order_by?.id);
    const bookerDoc = await bookerRef.get();
    const bookerData = bookerDoc.exists
      ? ({ ...bookerDoc.data(), id: bookerDoc.id } as Users)
      : null;

    // Initialize array to store guest data
    const guests: Users[] = [];

    // Only get guests if the current ticket user is the order booker
    if (orderData?.order_by?.id === userRef.id) {
      // Query all tickets for the same order
      const ticketsQuery = adminDb
        .collection(COLLECTIONS.TICKETS)
        .where('order_id', '==', adminDb.doc(orderRef.path));
      const ticketsSnapshot = await ticketsQuery.get();

      // Process each ticket in the order to get guest information
      for (const doc of ticketsSnapshot.docs) {
        // Skip the current ticket as we're looking for other guests
        if (doc.id === ticketId) continue;

        const ticketData = doc.data();
        const ticketType = getTicketType(ticketData.ticket_type);

        if (ticketType === TICKET_TYPES.CLAM_SHELL || ticketType === TICKET_TYPES.PARKING) continue;

        // Add guest to array with ticket info
        guests.push({
          ...ticketData.user_details,
          ticket_id: doc.id,
          ticket_number: ticketData.ticket_id,
          ticket_type: ticketData.ticket_type,
          barcode: ticketData.barcode,
          status: ticketData.status,
          check_in: ticketData.check_in,
        });
      }
    }

    // Construct complete ticket object with all associated data
    const ticket = {
      id: ticketDoc.id,
      ...ticketData,
      user: userData,
      order: orderData,
      guests: guests,
      booker: bookerData,
    } as Tickets & {
      user: Users | null;
      event: Events | null;
      schedule: EventSchedules | null;
      order: Orders;
      guests: Users[];
      booker: Users | null;
    };

    // Return successful response with ticket data
    return NextResponse.json({
      ticket,
      message: 'Ticket details retrieved successfully',
      status: 'success',
    });
  } catch (error) {
    // Log and return error response if anything fails
    console.error('Error fetching ticket details:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch ticket details',
        details: error instanceof Error ? error.message : 'Unknown error',
        status: 'error',
      },
      { status: 500 }
    );
  }
};
