import { Timestamp as ClientTimestamp } from 'firebase/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Orders } from '@/src/types/Orders';
import { Users } from '@/src/types/Users';

/**
 * Helper function to convert Admin Firestore Timestamp to client-compatible Timestamp
 * @param adminTimestamp The Admin Firestore timestamp to convert
 * @returns A client-compatible Timestamp object
 */
function convertTimestamp(adminTimestamp: any): ClientTimestamp | null {
  if (!adminTimestamp) return null;
  if (adminTimestamp.seconds !== undefined && adminTimestamp.nanoseconds !== undefined) {
    return new ClientTimestamp(adminTimestamp.seconds, adminTimestamp.nanoseconds);
  }
  return null;
}

/**
 * Converts all Timestamp fields in an object to client-compatible Timestamps
 * @param data The object containing potential Timestamp fields
 * @returns A new object with converted Timestamp fields
 */
function convertTimestampFields(data: any): any {
  if (!data) return data;

  const result = { ...data };
  for (const key in result) {
    if (result[key] && typeof result[key] === 'object') {
      if (result[key].seconds !== undefined && result[key].nanoseconds !== undefined) {
        result[key] = convertTimestamp(result[key]);
      } else if (Array.isArray(result[key])) {
        result[key] = result[key].map((item: any) =>
          typeof item === 'object' ? convertTimestampFields(item) : item
        );
      } else {
        result[key] = convertTimestampFields(result[key]);
      }
    }
  }
  return result;
}

/**
 * Handles POST requests to retrieve detailed order information including associated tickets
 *
 * @param {Request} req - The incoming HTTP request object containing orderId in the body
 * @returns {Promise<Response>} JSON response with order details and associated tickets
 *                             or appropriate error message with status code
 *
 * Success Response (200):
 * - tickets: Array of ticket objects with event and schedule details
 * - order: Complete order object with event and schedule information
 *
 * Error Responses:
 * - 400: Missing order ID
 * - 404: Order not found or no tickets found
 * - 500: Server error during processing
 */
export const POST = async (req: NextRequest) => {
  try {
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    //  App Check
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Extract orderId from request body
    const { orderId } = await req.json();

    // Validate that orderId is provided
    if (!orderId) {
      return new Response(JSON.stringify({ error: 'Order ID is required' }), {
        status: 400,
      });
    }

    // Create reference to order document and fetch it using Admin SDK
    const orderRef = adminDb.collection(COLLECTIONS.ORDERS).doc(orderId);
    const orderDoc = await orderRef.get();

    // Return 404 if order doesn't exist
    if (!orderDoc.exists) {
      return new Response(JSON.stringify({ error: 'Order not found' }), {
        status: 404,
      });
    }

    // Extract order data from document
    const orderData = convertTimestampFields(orderDoc.data());

    // Fetch the event associated with the order
    const orderEventRefPath = orderData?.event_id?.path;
    if (!orderEventRefPath) {
      return new Response(JSON.stringify({ error: 'Event reference not found in order' }), {
        status: 404,
      });
    }

    // Extract event ID from reference path
    const eventId = orderEventRefPath.split('/').pop();
    const orderEventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(eventId);
    const orderEventDoc = await orderEventRef.get();

    // Transform event data, including document ID if event exists
    const orderEventData = orderEventDoc.exists
      ? { id: orderEventDoc.id, ...convertTimestampFields(orderEventDoc.data()) }
      : null;

    // Fetch the specific schedule from event's subcollection
    const scheduleId = orderData?.event_schedule_id?.path?.split('/').pop();
    if (!scheduleId) {
      return new Response(JSON.stringify({ error: 'Schedule reference not found in order' }), {
        status: 404,
      });
    }

    const orderScheduleRef = adminDb
      .collection(COLLECTIONS.EVENTS)
      .doc(eventId)
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(scheduleId);

    const orderScheduleDoc = await orderScheduleRef.get();

    // Transform schedule data, including document ID if schedule exists
    const orderScheduleData = orderScheduleDoc.exists
      ? { id: orderScheduleDoc.id, ...convertTimestampFields(orderScheduleDoc.data()) }
      : null;

    // Combine order data with its associated event and schedule
    const orderWithDetails = {
      id: orderDoc.id,
      ...orderData,
      event: orderEventData,
      schedule: orderScheduleData,
      waiver_count: 0,
      booker: undefined,
      pending_child_waivers: undefined,
    } as Orders & {
      event: Events | null;
      schedule: EventSchedules | null;
      waiver_count: number;
      booker: Users | undefined;
      pending_child_waivers?: { first_name: string; last_name: string }[];
    };

    // Query tickets collection for all tickets associated with this order using Admin SDK
    const ticketsQuery = adminDb
      .collection(COLLECTIONS.TICKETS)
      .where('order_id', '==', adminDb.doc(orderRef.path));

    const ticketsSnapshot = await ticketsQuery.get();

    // Get user who placed the order
    const userRefPath = orderData.order_by?.path;
    if (userRefPath) {
      const userId = userRefPath.split('/').pop();
      const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
      const userDoc = await userRef.get();

      if (userDoc.exists && userDoc.data()?.email) {
        orderWithDetails.booker = convertTimestampFields(userDoc.data());
        const waiverResult = await getWaiverCount(userDoc.data()?.email, orderRef.path);

        if (typeof waiverResult === 'number') {
          orderWithDetails.waiver_count = waiverResult;
        } else {
          orderWithDetails.waiver_count = waiverResult.waiver_count;
          orderWithDetails.pending_child_waivers = waiverResult.pendingChildWaivers;
        }
      }
    }

    // Return 404 if no tickets are found for the order
    if (ticketsSnapshot.empty) {
      return new Response(
        JSON.stringify({
          error: 'No tickets found for order',
          order: orderWithDetails,
        }),
        { status: 404 }
      );
    }

    // Process each ticket to include its associated event and schedule details
    const tickets = await Promise.all(
      ticketsSnapshot.docs.map(async ticketDoc => {
        const ticketData = convertTimestampFields(ticketDoc.data());

        // Fetch and transform event data for each ticket
        const eventRefPath = ticketData.event_id?.path;
        const eventId = eventRefPath?.split('/').pop();
        let eventData = null;

        if (eventId) {
          const eventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(eventId);
          const eventDoc = await eventRef.get();
          eventData = eventDoc.exists
            ? { id: eventDoc.id, ...convertTimestampFields(eventDoc.data()) }
            : null;
        }

        // Fetch and transform schedule data for each ticket
        const scheduleRefPath = ticketData.event_schedule_id?.path;
        const scheduleId = scheduleRefPath?.split('/').pop();
        let scheduleData = null;

        if (eventId && scheduleId) {
          const scheduleRef = adminDb
            .collection(COLLECTIONS.EVENTS)
            .doc(eventId)
            .collection(COLLECTIONS.EVENT_SCHEDULES)
            .doc(scheduleId);

          const scheduleDoc = await scheduleRef.get();
          scheduleData = scheduleDoc.exists
            ? { id: scheduleDoc.id, ...convertTimestampFields(scheduleDoc.data()) }
            : null;
        }

        // Fetch and transform user data for each ticket
        const userRefPath = ticketData.user_id?.path;
        const userId = userRefPath?.split('/').pop();
        let userData = null;

        if (userId) {
          const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
          const userDoc = await userRef.get();
          userData = userDoc.exists ? convertTimestampFields(userDoc.data()) : null;
        }

        // Return complete ticket object with all associated data
        return {
          id: ticketDoc.id,
          ...ticketData,
          order_id: { path: orderRef.path } as any, // Convert to client-compatible reference
          event: eventData,
          schedule: scheduleData,
          user: userData,
        };
      })
    );

    // Return successful response with tickets and order details
    return new Response(
      JSON.stringify({
        tickets,
        order: orderWithDetails,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    // Log error and return 500 response with error details
    console.error('Error fetching tickets:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch tickets',
        details: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

async function getWaiverCount(email: string | undefined, orderRefPath: string) {
  if (!email) return 0;

  const currentDate = getPacificTimeNow();
  currentDate.setHours(0, 0, 0, 0);

  // First check for adult waiver using Admin SDK
  const waiversQuery = adminDb
    .collection(COLLECTIONS.WAIVERS)
    .where('email', '==', email)
    .where('valid_until', '>', currentDate);

  const waiverSnapshot = await waiversQuery.get();

  // Check for any pending child waivers using Admin SDK
  const ticketsQuery = adminDb
    .collection(COLLECTIONS.TICKETS)
    .where('order_id', '==', adminDb.doc(orderRefPath));

  const ticketsSnapshot = await ticketsQuery.get();

  const pendingChildWaivers: { first_name: string; last_name: string }[] = [];

  for (const ticketDoc of ticketsSnapshot.docs) {
    const ticketData = ticketDoc.data();
    const ticketUserRefPath = ticketData.user_id?.path;

    if (ticketUserRefPath) {
      const userId = ticketUserRefPath.split('/').pop();
      const ticketUserRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
      const ticketUserDoc = await ticketUserRef.get();
      const ticketUserData = ticketUserDoc.data();

      if (ticketUserData?.age_group === 'child') {
        // Check if child waiver exists using Admin SDK
        const childWaiverQuery = adminDb
          .collection(COLLECTIONS.WAIVERS)
          .where('email', '==', email)
          .where('first_name', '==', ticketUserData.first_name)
          .where('last_name', '==', ticketUserData.last_name)
          .where('valid_until', '>', currentDate);

        const childWaiverSnapshot = await childWaiverQuery.get();

        if (childWaiverSnapshot.empty) {
          pendingChildWaivers.push({
            first_name: ticketUserData.first_name,
            last_name: ticketUserData.last_name,
          });
        }
      }
    }
  }

  if (pendingChildWaivers.length > 0) {
    return {
      waiver_count: waiverSnapshot.size,
      pendingChildWaivers,
    };
  }

  return waiverSnapshot.size;
}
