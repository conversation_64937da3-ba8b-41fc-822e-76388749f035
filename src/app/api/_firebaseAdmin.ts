import { initializeApp, getApps, cert, App } from 'firebase-admin/app';
import { AppCheck, getAppCheck as getAdminAppCheckInstance } from 'firebase-admin/app-check'; // Renamed import for clarity
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore } from 'firebase-admin/firestore';

let app: App;

if (!getApps().length) {
  // console.log('[Firebase Admin] Initializing new app...'); // For debugging
  if (!process.env.FIREBASE_PRIVATE_KEY) {
    throw new Error('[Firebase Admin] FIREBASE_PRIVATE_KEY is not defined.');
  }
  if (!process.env.FIREBASE_CLIENT_EMAIL) {
    throw new Error('[Firebase Admin] FIREBASE_CLIENT_EMAIL is not defined.');
  }
  if (!process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID) {
    throw new Error('[Firebase Admin] NEXT_PUBLIC_FIREBASE_PROJECT_ID is not defined.');
  }

  app = initializeApp({
    credential: cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
  });
  // console.log('[Firebase Admin] New app initialized.'); // For debugging
} else {
  app = getApps()[0];
  // console.log('[Firebase Admin] Using existing app.'); // For debugging
}

export const adminAuth: Auth = getAuth(app);
export const adminDb: Firestore = getFirestore(app);
export const adminAppCheck: AppCheck = getAdminAppCheckInstance(app);
