import crypto from 'crypto';

import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { customerSchema } from '@/src/schema/profile.zod';

import { client } from '../_squareConfig';

export async function POST(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const requestData = await req.json();

    // Validate request data
    const validationResult = customerSchema.safeParse(requestData);

    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors[0]?.message || 'Invalid customer data';
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    // Use validated data
    const { email, firstName, lastName, phone, addressLine1, city, state, zipCode } =
      validationResult.data;

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // First, try to find existing customer by email
    const searchResponse = await client.customersApi.searchCustomers({
      query: {
        filter: {
          emailAddress: {
            exact: email,
          },
        },
      },
    });

    // If customer exists, return their ID
    if (searchResponse.result.customers && searchResponse.result.customers.length > 0) {
      return NextResponse.json({
        customerId: searchResponse.result.customers[0].id,
      });
    }

    // Prepare address object if address fields are provided
    const address =
      addressLine1 && city && state && zipCode
        ? {
            addressLine1,
            locality: city,
            administrativeDistrictLevel1: state,
            postalCode: zipCode,
            country: 'US',
          }
        : undefined;

    // If no customer found, create a new one
    const createResponse = await client.customersApi.createCustomer({
      idempotencyKey: crypto.randomUUID(),
      emailAddress: email,
      givenName: firstName,
      familyName: lastName,
      phoneNumber: phone,
      referenceId: email, // Optional: store email as reference
      address: address,
    });

    if (!createResponse.result.customer?.id) {
      throw new Error('Failed to create customer');
    }

    return NextResponse.json({
      customerId: createResponse.result.customer.id,
    });
  } catch (error: any) {
    console.error('Error in get-or-create-customer:', error);
    if (error.body) {
      try {
        const errorBody = typeof error.body === 'string' ? JSON.parse(error.body) : error.body;
        if (errorBody.errors && errorBody.errors.length > 0) {
          return NextResponse.json(
            { error: errorBody.errors[0].detail || 'Failed to process customer' },
            { status: 400 }
          );
        }
      } catch (parseError) {
        return NextResponse.json(
          { error: parseError || 'Failed to process customer' },
          { status: 500 }
        );
      }
    }
    return NextResponse.json(
      { error: error.message || 'Failed to process customer' },
      { status: 500 }
    );
  }
}
