import { NextRequest } from 'next/server';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

import { client, JSONBigInt } from '../_squareConfig';

// Define allowed origins
const allowedOrigins =
  process.env.NEXT_ALLOWED_ORIGINS && JSON.parse(process.env.NEXT_ALLOWED_ORIGINS);

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin') || '';

  // Only allow listed origins
  if (!allowedOrigins.includes(origin)) {
    return new Response(null, { status: 403 });
  }

  return new Response(null, {
    headers: {
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Firebase-AppCheck',
    },
  });
}

export async function POST(req: NextRequest) {
  // Handle CORS
  const origin = req.headers.get('origin') || '';

  // Only proceed if origin is allowed
  if (!allowedOrigins.includes(origin)) {
    return new Response(JSON.stringify({ error: 'Origin not allowed' }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const corsHeaders = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': origin,
  };

  try {
    // Verify App Check token
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return new Response(JSON.stringify({ error: ERROR_MESSAGES.INVALID_REQUEST }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    const { contactInfoType, contactInfo } = await req.json();

    if (!contactInfoType || !contactInfo) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Validate contact info format
    if (contactInfoType === 'email' && !/\S+@\S+\.\S+/.test(contactInfo)) {
      return new Response(JSON.stringify({ error: 'Invalid email format' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    if (contactInfoType === 'phone' && !/^\+?[1-9]\d{1,14}$/.test(contactInfo)) {
      return new Response(JSON.stringify({ error: 'Invalid phone format' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Search for customer in Square
    const searchQuery: Record<string, { exact: string }> = {};

    if (contactInfoType === 'email') {
      searchQuery.emailAddress = { exact: contactInfo };
    } else {
      // Format phone number for Square API if needed
      searchQuery.phoneNumber = { exact: contactInfo };
    }

    const response = await client.customersApi.searchCustomers({
      query: {
        filter: searchQuery,
      },
    });

    // If customer exists, return their ID
    if (response.result.customers && response.result.customers.length > 0) {
      // Use JSONBigInt to handle BigInt serialization
      const serializedCustomer = JSONBigInt.stringify(response.result.customers[0]);
      return new Response(serializedCustomer, {
        headers: corsHeaders,
      });
    } else {
      return new Response(JSON.stringify({ message: 'No customer found' }), {
        status: 200,
        headers: corsHeaders,
      });
    }
  } catch (error: any) {
    console.error('Error searching customers:', error);
    return new Response(JSON.stringify({ error: error.message || 'Failed to search customers' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
}
