import { NextRequest, NextResponse } from 'next/server';

import { client } from '../_squareConfig';

/**
 * Fetches an image from Square API and returns it
 *
 * @param req The incoming request with imageId query parameter
 * @returns The image binary data
 */
export async function GET(req: NextRequest) {
  try {
    // NOTE: No need to add App Check here as it's not a sensitive operation
    const imageId = req.nextUrl.searchParams.get('imageId');

    if (!imageId) {
      return NextResponse.json({ error: 'Image ID is required' }, { status: 400 });
    }

    // Fetch the image from Square
    const response = await client.catalogApi.retrieveCatalogObject(imageId);

    if (!response.result || !response.result.object || !response.result.object.imageData) {
      throw new Error('Image not found');
    }

    const imageUrl = response.result.object.imageData.url;

    if (!imageUrl) {
      throw new Error('Image URL not found');
    }

    // Fetch the actual image data
    const imageResponse = await fetch(imageUrl);
    const imageBuffer = await imageResponse.arrayBuffer();

    // Determine content type
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';

    // Return the image with proper content type
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
      },
    });
  } catch (error) {
    console.error('Error fetching image from Square:', error);
    return NextResponse.json({ error: 'Failed to fetch image' }, { status: 500 });
  }
}
