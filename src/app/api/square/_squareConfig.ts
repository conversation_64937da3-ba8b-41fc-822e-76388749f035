import { Client, Environment } from 'square';

export const client = new Client({
  environment:
    process.env.NEXT_SQUARE_ENVIRONMENT === 'production'
      ? Environment.Production
      : Environment.Sandbox,
  accessToken:
    process.env.NEXT_SQUARE_ACCESS_TOKEN ??
    'EAAAl8ZiyMqr4YpedfGst_ujd1emXzJ1smgqcYh-bxL182jGIby2MK5MOVOPDnM8',
});

export const JSONBigInt = {
  stringify: (obj: any) => {
    return JSON.stringify(obj, (_, value) =>
      typeof value === 'bigint' ? value.toString() : value
    );
  },
  parse: JSON.parse,
};
