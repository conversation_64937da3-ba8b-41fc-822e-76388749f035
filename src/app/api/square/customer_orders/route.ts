import { NextRequest } from 'next/server';
import { Order } from 'square';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

import { client, JSONBigInt } from '../_squareConfig';

// Define allowed origins from environment variable
const allowedOrigins =
  process.env.NEXT_ALLOWED_ORIGINS && JSON.parse(process.env.NEXT_ALLOWED_ORIGINS);

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin') || '';

  // Only allow listed origins
  if (!allowedOrigins.includes(origin)) {
    return new Response(null, { status: 403 });
  }

  return new Response(null, {
    headers: {
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Firebase-AppCheck',
    },
  });
}

export async function POST(req: NextRequest) {
  // Handle CORS
  const origin = req.headers.get('origin') || '';

  // Only proceed if origin is allowed
  if (!allowedOrigins.includes(origin)) {
    return new Response(JSON.stringify({ error: 'Origin not allowed' }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const corsHeaders = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': origin,
  };

  try {
    // Verify App Check token
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return new Response(JSON.stringify({ error: ERROR_MESSAGES.INVALID_REQUEST }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    const { customerId, emailAddress } = await req.json();

    if (!customerId || !emailAddress) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Get customer cards
    // NOTE: The undefined is mendatory in this api endpoint if I am removing then its throwing an error.
    const {
      result: { cards = [] },
    } = await client.cardsApi.listCards(undefined, customerId);

    // Collect all payments from customer cards
    const allPayments = [];
    const orderIds = new Set();

    // Process cards in parallel for better performance
    // NOTE: The undefined is mendatory in this api endpoint if I am removing then its throwing an error.
    await Promise.all(
      cards.map(async card => {
        const {
          result: { payments = [] },
        } = await client.paymentsApi.listPayments(
          undefined,
          undefined,
          undefined,
          undefined,
          process.env.NEXT_SQUARE_LOCATION_ID || '',
          undefined,
          card?.last4,
          card?.cardBrand
        );

        // Filter payments by email and collect order IDs
        payments.forEach(payment => {
          if (payment.note?.includes(emailAddress)) {
            orderIds.add(payment.orderId);
          }
        });

        allPayments.push(...payments);
      })
    );

    let allOrders: Order[] = [];
    const locationId = process.env.NEXT_SQUARE_LOCATION_ID || '';

    // Fetch orders by IDs if any found
    if (orderIds.size > 0) {
      const {
        result: { orders = [] },
      } = await client.ordersApi.batchRetrieveOrders({
        orderIds: Array.from(orderIds) as string[],
        locationId,
      });

      allOrders = [...orders];
    }

    // Search for additional orders by customer ID
    const {
      result: { orders = [] },
    } = await client.ordersApi.searchOrders({
      query: {
        filter: {
          customerFilter: {
            customerIds: [customerId],
          },
        },
      },
      locationIds: [locationId],
    });

    // Add unique orders from second API call
    const existingOrderIds = new Set(allOrders.map(order => order.id));
    orders.forEach(order => {
      if (!existingOrderIds.has(order.id)) {
        allOrders.push(order);
      }
    });

    // Return response
    return new Response(
      allOrders.length > 0
        ? JSONBigInt.stringify(allOrders)
        : JSON.stringify({ message: 'No orders found for this customer' }),
      {
        status: 200,
        headers: corsHeaders,
      }
    );
  } catch (error: any) {
    console.error('Error fetching customer orders:', error);
    return new Response(
      JSON.stringify({ error: error.message || 'Failed to fetch customer orders' }),
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}
