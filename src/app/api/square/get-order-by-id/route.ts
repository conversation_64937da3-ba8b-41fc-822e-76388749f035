import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

import { client, JSONBigInt } from '../_squareConfig';

// Define allowed origins from environment variable
const allowedOrigins =
  process.env.NEXT_ALLOWED_ORIGINS && JSON.parse(process.env.NEXT_ALLOWED_ORIGINS);

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin') || '';

  // Only allow listed origins
  if (!allowedOrigins.includes(origin)) {
    return new Response(null, { status: 403 });
  }

  return new Response(null, {
    headers: {
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Firebase-AppCheck',
    },
  });
}

export async function POST(req: NextRequest) {
  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Extract orderId from request body
    const { orderId } = await req.json();

    // Validate that orderId is provided
    if (!orderId) {
      return NextResponse.json({ error: 'Order ID is required' }, { status: 400 });
    }

    // Retrieve order from Square
    const response = await client.ordersApi.retrieveOrder(orderId);

    if (!response.result || !response.result.order) {
      console.error('No order found in response:', JSONBigInt.stringify(response));
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    // Return successful response with order data
    return NextResponse.json({
      order: JSONBigInt.stringify(response.result.order),
      status: 'success',
    });
  } catch (error) {
    console.error('Error fetching Square order:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_FETCH_ORDER }, { status: 500 });
  }
}
