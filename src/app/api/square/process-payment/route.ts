import { DocumentReference, Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { AGE_GROUPS, COLLECTIONS, ERROR_MESSAGES, ROUTES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Users } from '@/src/types/Users';

// Import Square API client configuration
import { client, JSONBigInt } from '../_squareConfig';

/**
 * Handles POST requests to create Square payment links for event tickets
 * This endpoint creates a customized checkout page for ticket purchases
 *
 * @param {NextRequest} req - The incoming HTTP request
 * @returns {Promise<NextResponse>} JSON response with payment link or error
 */
export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Validate that the request contains JSON content
    if (!req.headers.get('content-type')?.includes('application/json')) {
      return new NextResponse(
        JSON.stringify({
          error: 'Request must be application/json',
        }),
        { status: 400 }
      );
    }

    // Safely parse the JSON request body
    let body;
    try {
      body = await req.json();
    } catch (e) {
      return new NextResponse(
        JSON.stringify({
          error: e || 'Invalid JSON in request body',
        }),
        { status: 400 }
      );
    }

    // Extract required fields from the request body
    const {
      event,
      schedule,
      guests,
      booker,
    }: {
      event: Events | null;
      schedule: EventSchedules | null;
      guests: Users[];
      booker: Users | null;
    } = body;
    // Validate required fields
    if (!event || !schedule || !guests || !booker) {
      return new NextResponse(
        JSON.stringify({
          error: 'Missing required fields',
        }),
        { status: 400 }
      );
    }

    // Function to get or create user
    const getOrCreateUser = async (userData: Users) => {
      // Only check for existing user if age_group is adult
      if (userData.age_group === AGE_GROUPS.ADULT) {
        const usersRef = adminDb.collection(COLLECTIONS.USERS);
        const querySnapshot = await usersRef.where('email', '==', userData.email).get();

        if (!querySnapshot.empty) {
          // User exists, return the first matching user
          return querySnapshot.docs[0].ref;
        }
      }

      // Create new user (for children or new adult users)
      const newUser = {
        first_name: userData.first_name || '',
        last_name: userData.last_name || '',
        email: userData.email || '',
        role: 'customer',
        age_group: userData.age_group,
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
      };

      return await adminDb.collection(COLLECTIONS.USERS).add(newUser);
    };

    // Process booker
    const bookerRef = await getOrCreateUser(booker);

    // Process guests
    const guestRefs = await Promise.all(guests.map(guest => getOrCreateUser(guest)));

    // Create document references for event and schedule
    const eventRef = adminDb
      .collection(COLLECTIONS.EVENTS)
      .doc(event?.id || '') as DocumentReference<Events>;
    const scheduleRef = adminDb
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(schedule?.id || '') as DocumentReference<EventSchedules>;

    // Count adults and children from guests - Move this up before order creation
    const adultCount = guests.filter(guest => guest.age_group === AGE_GROUPS.ADULT).length;
    const childrenCount = guests.filter(guest => guest.age_group === AGE_GROUPS.CHILD).length;

    // Create order
    const orderRef = await adminDb.collection(COLLECTIONS.ORDERS).add({
      order_by: bookerRef,
      item_type: 'ticket',
      item: {
        event_id: eventRef,
        schedule_id: scheduleRef,
        guests: guests.length,
      },
      quantity: guests.length,
      event_id: eventRef,
      event_schedule_id: scheduleRef,
      total_amount:
        adultCount * (schedule?.price_per_adult || 0) +
        childrenCount * (schedule?.price_per_child || 0),
      status: 'pending',
      order_date: getPacificTimeNow(),
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
    });

    // Create tickets for each guest
    await Promise.all(
      guestRefs.map(async guestRef => {
        const event_id = event?.id;
        const event_schedule_id = schedule?.id;

        if (!event_id || !event_schedule_id) {
          throw new Error('event_id and event_schedule_id are required');
        }

        const guestDoc = await guestRef.get();
        const guestData = guestDoc.data();
        const amount =
          guestData?.age_group === AGE_GROUPS.ADULT
            ? schedule?.price_per_adult || 0
            : schedule?.price_per_child || 0;

        const ticketData = {
          order_id: orderRef,
          user_id: guestRef,
          event_id: eventRef,
          event_schedule_id: scheduleRef,
          status: 'draft',
          amount,
          created_at: Timestamp.now(),
          updated_at: Timestamp.now(),
          ticket_id: `VM-${Array.from(crypto.getRandomValues(new Uint8Array(2)))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('')
            .toUpperCase()}`,
          check_in: {
            method: 'pending',
          },
        };
        return adminDb.collection('tickets').add(ticketData);
      })
    );

    // Create line items array based on ticket types
    const paymentLineItems = [];

    if (adultCount > 0) {
      paymentLineItems.push({
        name: `U-Pick Ticket | Adult`,
        quantity: String(adultCount),
        catalogObjectId: process.env.NEXT_ADULT_CATALOG_ID,
        basePriceMoney: {
          amount: BigInt((schedule?.price_per_adult || 0) * 100),
          currency: 'USD',
        },
      });
    }

    if (childrenCount > 0) {
      paymentLineItems.push({
        name: `U-Pick Ticket | Child`,
        quantity: String(childrenCount),
        catalogObjectId: process.env.NEXT_CHILD_CATALOG_ID,
        basePriceMoney: {
          amount: BigInt((schedule?.price_per_child || 0) * 100),
          currency: 'USD',
        },
      });
    }

    // Construct the Square payment link request object
    const orderRequest = {
      idempotencyKey: crypto.randomUUID(),
      enableCoupon: false,
      checkoutOptions: {
        redirectUrl: `${process.env.NEXT_PUBLIC_BASE_URL}${ROUTES.ORDER_CONFIRMATION(orderRef.id)}`,
        askForShippingAddress: false,
        customerCanTransactOnline: true,
        enableCoupon: false,
        acceptedPaymentMethods: {
          applePay: true,
          googlePay: true,
          cashAppPay: false,
          afterpayClearpay: false,
          card: true,
        },
        paymentType: 'FULL',
        redirectConfirmationBehavior: 'CONFIRM_AND_REDIRECT',
      },
      prePopulatedData: {
        buyerEmail: booker.email || '',
        buyerPhoneNumber: booker.phone || '',
        buyerAddress: {
          firstName: booker.first_name || '',
          lastName: booker.last_name || '',
          addressLine1: '',
          addressLine2: '',
          postalCode: '',
          country: 'US',
        },
      },
      order: {
        locationId: process.env.NEXT_SQUARE_LOCATION_ID || 'LZWDV4VZDEHVM',
        lineItems: paymentLineItems,
        state: 'OPEN',
        source: {
          name: process.env.NEXT_PUBLIC_BASE_URL,
        },
        metadata: {
          order_id: orderRef.id,
        },
        referenceId: orderRef.id,
      },
    };

    // Send request to Square API to create the payment link
    const response = await client.checkoutApi.createPaymentLink(orderRequest);

    if (process.env.NEXT_SQUARE_ENVIRONMENT === 'sandbox') {
      // Get the payment link ID and Square order ID from the response
      const paymentLinkId = response.result?.paymentLink?.id;
      const squareOrderId = response.result?.paymentLink?.orderId;

      // If we have both IDs, update the payment link with the Square order ID in the redirect URL
      if (paymentLinkId && squareOrderId) {
        const updatedRedirectUrl = `${process.env.NEXT_PUBLIC_BASE_URL}${ROUTES.ORDER_CONFIRMATION(orderRef.id)}?orderId=${squareOrderId}&transactionId=${squareOrderId}`;

        try {
          await client.checkoutApi.updatePaymentLink(paymentLinkId, {
            paymentLink: {
              version: 1,
              checkoutOptions: {
                redirectUrl: updatedRedirectUrl,
              },
            },
          });
        } catch (updateError) {
          console.error(
            'Error updating payment link redirect URL:',
            JSONBigInt.stringify(updateError)
          );
        }
      }
    }

    if (!response.result || !response.result.paymentLink) {
      console.error('No payment link found in response:', JSONBigInt.stringify(response));
      throw new Error('Failed to create payment link');
    }

    return new NextResponse(JSONBigInt.stringify(response.result), {
      status: 200,
    });
  } catch (error) {
    console.error('Error creating Square payment link:', JSONBigInt.stringify(error));
    return new NextResponse(
      JSON.stringify({
        error: 'Failed to create payment link',
      }),
      { status: 500 }
    );
  }
}
