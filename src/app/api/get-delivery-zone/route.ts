import { Timestamp as ClientTimestamp } from 'firebase/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
/**
 * Helper function to convert Admin Firestore Timestamp to client-compatible Timestamp
 * @param adminTimestamp The Admin Firestore timestamp to convert
 * @returns A client-compatible Timestamp object
 */
function convertTimestamp(adminTimestamp: any): ClientTimestamp | null {
  if (!adminTimestamp) return null;
  if (adminTimestamp.seconds !== undefined && adminTimestamp.nanoseconds !== undefined) {
    return new ClientTimestamp(adminTimestamp.seconds, adminTimestamp.nanoseconds);
  }
  return null;
}

/**
 * Converts all Timestamp fields in an object to client-compatible Timestamps
 * @param data The object containing potential Timestamp fields
 * @returns A new object with converted Timestamp fields
 */
function convertTimestampFields(data: any): any {
  if (!data) return data;

  const result = { ...data };
  for (const key in result) {
    if (result[key] && typeof result[key] === 'object') {
      if (result[key].seconds !== undefined && result[key].nanoseconds !== undefined) {
        result[key] = convertTimestamp(result[key]);
      } else if (Array.isArray(result[key])) {
        result[key] = result[key].map((item: any) =>
          typeof item === 'object' ? convertTimestampFields(item) : item
        );
      } else {
        result[key] = convertTimestampFields(result[key]);
      }
    }
  }
  return result;
}

export async function GET(req: NextRequest) {
  try {
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    //  App Check
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Query active delivery zones using Admin SDK
    const deliveryZonesQuery = adminDb
      .collection(COLLECTIONS.DELIVERY_ZONES)
      .where('is_active', '==', true);

    const deliveryZonesSnapshot = await deliveryZonesQuery.get();

    if (deliveryZonesSnapshot.empty) {
      return NextResponse.json({
        message: SUCCESS_MESSAGES.GET_DELIVERY_ZONES,
        data: [],
      });
    }

    // Transform delivery zones data with timestamp conversion
    const deliveryZones = deliveryZonesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...convertTimestampFields(doc.data()),
    }));

    return NextResponse.json({
      message: SUCCESS_MESSAGES.GET_DELIVERY_ZONES,
      data: deliveryZones,
    });
  } catch (err: { message: string } | any) {
    console.error('Error fetching delivery zones:', err);
    return NextResponse.json(
      { error: err.message || ERROR_MESSAGES.FAILED_TO_FETCH_DELIVERY_ZONES },
      { status: 500 }
    );
  }
}
