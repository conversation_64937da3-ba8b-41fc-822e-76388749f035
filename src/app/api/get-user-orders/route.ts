import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';
import { Orders } from '@/src/types/Orders';

/**
 * Handles GET requests to retrieve all orders associated with a specific user
 */
export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract userId from URL query parameters
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const firebaseId = searchParams.get('firebaseId');

    // Validate that userId is provided
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Check if the authenticated user is requesting their own data
    if (authResult.decodedToken.uid !== firebaseId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Create reference to user document and verify it exists
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
    const userDoc = await userRef.get();

    // Return 404 if user doesn't exist
    if (!userDoc.exists) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Query orders collection for all orders associated with this user
    const ordersQuery = adminDb.collection(COLLECTIONS.ORDERS).where('order_by', '==', userRef);

    const ordersSnapshot = await ordersQuery.get();

    // Return empty array if no orders found
    if (ordersSnapshot.empty) {
      return NextResponse.json({
        orders: [],
        message: 'No orders found for user',
        status: 'success',
      });
    }

    // Transform orders data to include document ID
    const orders = ordersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Orders[];

    // Return successful response
    return NextResponse.json({
      orders,
      message: 'Orders retrieved successfully',
      status: 'success',
    });
  } catch (error) {
    console.error('Error fetching user orders:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.GENERIC_ERROR }, { status: 500 });
  }
}
