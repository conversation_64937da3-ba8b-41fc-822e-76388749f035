// Import required Firestore functions from Firebase Admin SDK
import { Timestamp } from 'firebase-admin/firestore';
// Import Next.js request type for API route handling
import { NextRequest, NextResponse } from 'next/server';

// Import custom constants and configurations
import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { EventSchedules } from '@/src/types/EventSchedules';

/**
 * GET endpoint to calculate available ticket stock for an event schedule
 *
 * This endpoint:
 * 1. Retrieves event schedule details including max capacity and sold tickets
 * 2. Checks for pending orders that might block tickets
 * 3. Calculates available tickets by subtracting sold and pending tickets from max capacity
 *
 * @param {NextRequest} req - The incoming HTTP request
 * @returns {Promise<Response>} JSON response with available tickets or error
 *
 * Query Parameters:
 * - event_id: ID of the event
 * - event_schedule_id: ID of the specific event schedule
 *
 * @example
 * GET /api/ticket-stock?event_id=123&event_schedule_id=456
 */
export const GET = async (req: NextRequest) => {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract query parameters from the request URL
    const { searchParams } = new URL(req.url);
    const event_id = searchParams.get('event_id');
    const event_schedule_id = searchParams.get('event_schedule_id');

    // Validate required query parameters
    if (!event_id || !event_schedule_id) {
      return new Response(
        JSON.stringify({
          error: 'Event ID and Event schedule ID are required as query parameters',
        }),
        { status: 400 }
      );
    }

    // Get event schedule details
    const eventScheduleRef = adminDb
      .collection(COLLECTIONS.EVENTS)
      .doc(event_id)
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(event_schedule_id);

    const eventScheduleSnap = await eventScheduleRef.get();

    if (!eventScheduleSnap.exists) {
      return new Response(JSON.stringify({ error: 'Event schedule not found' }), { status: 404 });
    }

    const eventSchedule = eventScheduleSnap.data() as EventSchedules;
    const maxTickets = eventSchedule.max_no_of_tickets || 0;
    const soldTickets = eventSchedule.tickets_sold || 0;

    // Check pending orders from last 5 minutes
    const fiveMinutesAgo = Timestamp.fromDate(new Date(Date.now() - 5 * 60 * 1000));
    const topLevelEventScheduleRef = adminDb
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(event_schedule_id);

    const pendingOrdersQuery = adminDb
      .collection(COLLECTIONS.ORDERS)
      .where('event_schedule_id', '==', topLevelEventScheduleRef)
      .where('status', '==', 'pending')
      .where('updated_at', '>=', fiveMinutesAgo);

    const pendingOrdersSnap = await pendingOrdersQuery.get();

    let pendingTicketsCount = 0;
    pendingOrdersSnap.forEach(orderDoc => {
      const orderData = orderDoc.data();
      pendingTicketsCount += orderData.quantity || 0;
    });

    const availableTickets = Math.max(0, maxTickets - (soldTickets + pendingTicketsCount));

    return new Response(
      JSON.stringify({
        available_tickets: availableTickets,
      }),
      { status: 200 }
    );
  } catch (error) {
    console.error('Error calculating ticket stock:', error);

    // Handle specific error for event schedule not found
    if (error instanceof Error && error.message === 'Event schedule not found') {
      return new Response(JSON.stringify({ error: 'Event schedule not found' }), { status: 404 });
    }

    return new Response(JSON.stringify({ error: 'Internal server error' }), { status: 500 });
  }
};
