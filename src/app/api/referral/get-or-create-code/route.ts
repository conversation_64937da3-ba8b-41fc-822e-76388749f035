import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES, FIREBASE_COLLECTIONS } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

import { adminDb } from '../../_firebaseAdmin';

export async function POST(req: NextRequest) {
  const logPrefix = '[Create Referral Code API]';
  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      console.warn(`App Check verification failed.`);
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const { email } = await req.json();

    // Basic field validation
    if (!email) {
      console.error(`${logPrefix} Missing required fields.`);
      return NextResponse.json(
        { error: 'Internal Error: Missing required fields' },
        { status: 400 }
      );
    }

    const normalizedEmail = email.trim().toLowerCase();
    try {
      // Check if user already has a referral code
      const userReferralDoc = await adminDb
        .collection(FIREBASE_COLLECTIONS.USER_REFERRALS)
        .doc(normalizedEmail)
        .get();

      if (userReferralDoc.exists) {
        const data = userReferralDoc.data();
        return NextResponse.json({
          data,
        });
      }

      // Generate unique 7-digit code
      let referralCode;
      let isUnique = false;

      while (!isUnique) {
        referralCode = generateRandomCode(7);

        // Check if code is unique across all USER_REFERRALS
        const existingCode = await adminDb
          .collection(FIREBASE_COLLECTIONS.USER_REFERRALS)
          .where('referralCode', '==', referralCode)
          .limit(1)
          .get();

        isUnique = existingCode.empty;
      }

      // Create new USER_REFERRALS document
      await adminDb.collection(FIREBASE_COLLECTIONS.USER_REFERRALS).doc(normalizedEmail).set({
        referral_code: referralCode,
        total_points_earned: 0,
        referrer_email: normalizedEmail,
        created_at: new Date(),
        updated_at: new Date(),
      });

      NextResponse.json({ referralCode, totalPointsEarned: 0 });
    } catch (error) {
      console.error('Error generating referral code:', error);
      NextResponse.json({ error: 'Internal server error' });
    }
  } catch (error) {
    console.error(`Error:`, error instanceof Error ? error.message : String(error));
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_GET_CODE }, { status: 500 });
  }
}

function generateRandomCode(length: number) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
