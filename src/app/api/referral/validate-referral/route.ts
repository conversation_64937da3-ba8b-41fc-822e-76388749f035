import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

import { adminDb } from '../../_firebaseAdmin';

export async function POST(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const { referralCode, refereeEmail, orderQuantity, orderSubtotalCents } = await req.json();

    if (!referralCode || !refereeEmail || !orderQuantity || !orderSubtotalCents) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const normalizedRefereeEmail = refereeEmail.toLowerCase();

    // Check if referee has already used a referral code
    const refereeHistoryDoc = await adminDb
      .collection('referee_history')
      .doc(normalizedRefereeEmail)
      .get();

    if (refereeHistoryDoc.exists) {
      return NextResponse.json(
        {
          error: 'You have already used a referral code and cannot use another one.',
        },
        { status: 400 }
      );
    }

    // Validate referral code exists
    const userReferralQuery = await adminDb
      .collection('user_referrals')
      .where('referral_code', '==', referralCode)
      .limit(1)
      .get();

    if (userReferralQuery.empty) {
      return NextResponse.json({ error: 'Invalid referral code' }, { status: 400 });
    }

    const referrerEmail = userReferralQuery.docs[0].id;

    // Prevent self-referral
    if (referrerEmail === normalizedRefereeEmail) {
      return NextResponse.json({ error: 'You cannot use your own referral code' }, { status: 400 });
    }

    // First, try to find a date-specific active policy
    const dateSpecificPolicies = await adminDb
      .collection('referral_policies')
      .where('is_active', '==', true)
      .where('is_default', '==', true)
      .limit(1)
      .get();

    if (dateSpecificPolicies.empty) {
      return NextResponse.json({ error: 'Unable to find the referral policy!' });
    }

    const policy = dateSpecificPolicies.docs[0].data();

    // Check minimum order value requirement
    if (
      policy.min_order_value_for_referee_benefit_cents &&
      orderSubtotalCents < policy.min_order_value_for_referee_benefit_cents
    ) {
      const minAmount = policy.min_order_value_for_referee_benefit_cents / 100;
      return NextResponse.json(
        {
          error: `Add $${minAmount.toFixed(2)} more to get your referral discount!`,
          requiresMinimum: true,
          currentTotal: orderSubtotalCents / 100,
          minimumRequired: minAmount,
        },
        { status: 400 }
      );
    }

    // Check minimum quantity requirement
    if (
      policy.min_quantity_for_referee_benefit &&
      orderQuantity < policy.min_quantity_for_referee_benefit
    ) {
      return NextResponse.json(
        {
          error: `Add ${policy.min_quantity_for_referee_benefit - orderQuantity} more items to get your referral discount!`,
          requiresMinimum: true,
        },
        { status: 400 }
      );
    }

    // Calculate referee benefit
    let refereeBenefitCents = 0;
    if (policy.referee_benefit_type === 'PERCENTAGE') {
      refereeBenefitCents = Math.floor((orderSubtotalCents * policy.referee_benefit_value) / 100);
    } else if (policy.referee_benefit_type === 'FIXED_AMOUNT') {
      refereeBenefitCents = policy.referee_benefit_value * 100;
    }

    const finalOrderSubtotal = orderSubtotalCents - refereeBenefitCents;

    return NextResponse.json(
      {
        valid: true,
        referrerEmail,
        policyId: dateSpecificPolicies.docs[0].id,
        discountCents: refereeBenefitCents,
        originalSubtotalCents: orderSubtotalCents,
        finalSubtotalCents: finalOrderSubtotal,
        discountType: policy.referee_benefit_type,
        discountValue: policy.referee_benefit_value,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Check-in status error:', error);
    return NextResponse.json({ error: 'Failed to get the policy!' }, { status: 500 });
  }
}
