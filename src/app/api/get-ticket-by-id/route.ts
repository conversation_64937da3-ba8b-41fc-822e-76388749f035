import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Users } from '@/src/types/Users';

/**
 * Handles GET requests to retrieve detailed ticket information
 *
 * @param {Request} req - The incoming HTTP request with ticketId as query parameter
 * @returns {Promise<Response>} JSON response with ticket details including user, event and schedule information
 *
 * Success Response (200):
 * - ticket: Ticket object with user, event and schedule details
 *
 * Error Responses:
 * - 400: Missing ticket ID
 * - 404: Ticket not found
 * - 500: Server error during processing
 */
export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Extract ticketId from URL query parameters
    const { searchParams } = new URL(req.url);
    const ticketId = searchParams.get('ticketId');

    // Validate that ticketId is provided
    if (!ticketId) {
      return NextResponse.json(
        {
          error: 'Ticket ID is required',
          status: 'error',
        },
        { status: 400 }
      );
    }

    // Get ticket document using Admin SDK
    const ticketRef = adminDb.collection(COLLECTIONS.TICKETS).doc(ticketId);
    const ticketDoc = await ticketRef.get();

    // Check if ticket exists
    if (!ticketDoc.exists) {
      return NextResponse.json(
        {
          error: 'Ticket not found',
          status: 'error',
        },
        { status: 404 }
      );
    }

    const ticketData = ticketDoc.data();

    // Check if ticketData exists
    if (!ticketData) {
      return NextResponse.json(
        {
          error: 'Ticket data is empty',
          status: 'error',
        },
        { status: 404 }
      );
    }

    // Fetch user data
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(ticketData.user_id.id);
    const userDoc = await userRef.get();
    const userData = userDoc.exists ? userDoc.data() : null;

    // Fetch event data
    const eventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(ticketData.event_id.id);
    const eventDoc = await eventRef.get();
    const eventData = eventDoc.exists ? { id: eventDoc.id, ...eventDoc.data() } : null;

    // Fetch schedule data
    const scheduleRef = adminDb
      .collection(COLLECTIONS.EVENTS)
      .doc(eventDoc.id)
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(ticketData.event_schedule_id.id);
    const scheduleDoc = await scheduleRef.get();
    const scheduleData = scheduleDoc.exists ? { id: scheduleDoc.id, ...scheduleDoc.data() } : null;

    // Fetch order data
    const orderRef = adminDb.collection(COLLECTIONS.ORDERS).doc(ticketData.order_id.id);
    const orderDoc = await orderRef.get();
    const orderData = orderDoc.exists ? orderDoc.data() : null;

    // Fetch booker data with explicit typing
    const bookerRef = adminDb.collection(COLLECTIONS.USERS).doc(orderData?.order_by?.id);
    const bookerDoc = await bookerRef.get();
    const bookerData = bookerDoc.exists
      ? ({ ...bookerDoc.data(), id: bookerDoc.id } as Users)
      : null;

    const guests = [];
    const currentDate = getPacificTimeNow();
    currentDate.setHours(0, 0, 0, 0);

    // Only get guests if the current ticket user is the order booker
    if (orderData?.order_by?.id === userRef.id) {
      // Get all tickets for the same order (excluding current ticket)
      const ticketsQuery = adminDb
        .collection(COLLECTIONS.TICKETS)
        .where('order_id', '==', orderRef);
      const ticketsSnapshot = await ticketsQuery.get();

      // Process each ticket and get associated user data
      for (const doc of ticketsSnapshot.docs) {
        // Skip the current ticket
        if (doc.id === ticketId) continue;

        const ticketData = doc.data();
        const guestUserRef = adminDb.collection(COLLECTIONS.USERS).doc(ticketData.user_id.id);
        const guestUserDoc = await guestUserRef.get();

        if (guestUserDoc.exists) {
          const userData = guestUserDoc.data();
          let signed_waiver = false;

          if (userData && userData.age_group === 'child') {
            // Get the booker's email from the order
            const bookerEmail = bookerData?.email ?? '';

            if (bookerEmail) {
              // For children, check waiver using booker's email and child's name
              const waiverQuery = adminDb
                .collection(COLLECTIONS.WAIVERS)
                .where('email', '==', bookerEmail)
                .where('first_name', '==', userData.first_name)
                .where('last_name', '==', userData.last_name)
                .where('valid_until', '>', currentDate);
              const waiverSnapshot = await waiverQuery.get();
              signed_waiver = !waiverSnapshot.empty;
            }
          } else if (userData && userData.email) {
            // For adults, check waiver using their email and name
            const waiverQuery = adminDb
              .collection(COLLECTIONS.WAIVERS)
              .where('email', '==', userData.email)
              .where('first_name', '==', userData.first_name)
              .where('last_name', '==', userData.last_name)
              .where('valid_until', '>', currentDate);
            const waiverSnapshot = await waiverQuery.get();
            signed_waiver = !waiverSnapshot.empty;
          }

          if (userData) {
            guests.push({
              ...userData,
              signed_waiver,
              ticket_id: doc.id,
              ticket_number: ticketData.ticket_id,
              status: ticketData.status,
            });
          }
        }
      }
    }

    // Also check waiver status for the main ticket holder
    let mainUserSignedWaiver = false;
    if (userData && userData.email) {
      const waiverQuery = adminDb
        .collection(COLLECTIONS.WAIVERS)
        .where('email', '==', userData.email)
        .where('valid_until', '>', currentDate)
        .where('first_name', '==', userData.first_name)
        .where('last_name', '==', userData.last_name);
      const waiverSnapshot = await waiverQuery.get();
      mainUserSignedWaiver = !waiverSnapshot.empty;
    }

    // Construct complete ticket object with all associated data
    const ticket = {
      id: ticketDoc.id,
      ...ticketData,
      user: userData ? { ...userData, signed_waiver: mainUserSignedWaiver } : null,
      event: eventData,
      schedule: scheduleData,
      order: orderData,
      guests: guests,
      booker: bookerData,
    };

    // Return successful response
    return NextResponse.json({
      ticket,
      message: 'Ticket details retrieved successfully',
      status: 'success',
    });
  } catch (error) {
    console.error('Error fetching ticket details:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch ticket details',
        details: error instanceof Error ? error.message : 'Unknown error',
        status: 'error',
      },
      { status: 500 }
    );
  }
}
