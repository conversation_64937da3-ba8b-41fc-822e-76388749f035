import createDOMPurify from 'dompurify';
import { J<PERSON><PERSON> } from 'jsdom';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

function sanitizeHtml(htmlContent: string): string {
  const window = new JSDOM('').window;
  const DOMPurify = createDOMPurify(window);
  return DOMPurify.sanitize(htmlContent, {
    ALLOWED_TAGS: [
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'p',
      'a',
      'ul',
      'ol',
      'li',
      'b',
      'strong',
      'em',
      'strike',
      'code',
      'hr',
      'br',
      'div',
      'span',
    ],
    ALLOWED_ATTR: ['href', 'target', 'title', 'rel'],
    FORBID_TAGS: ['style', 'script'],
    ALLOW_DATA_ATTR: false,
  });
}

export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Query for active waiver versions using Admin SDK
    const waiversQuery = adminDb
      .collection(COLLECTIONS.WAIVER_VERSIONS)
      .where('status', '==', 'active')
      .orderBy('version', 'desc')
      .limit(1);

    const snapshot = await waiversQuery.get();

    if (snapshot.empty) {
      return NextResponse.json({ error: 'No waiver content found' }, { status: 404 });
    }

    const latestWaiver = snapshot.docs[0].data();

    // Sanitize the HTML content to prevent XSS attacks
    // Sanitize the HTML content to prevent XSS attacks
    // const window = new JSDOM('').window;
    // const DOMPurify = createDOMPurify(window);
    // const sanitizedContent = DOMPurify.sanitize(latestWaiver.html_content);
    // Sanitize the HTML content to prevent XSS attacks
    const sanitizedContent = sanitizeHtml(latestWaiver.html_content);

    return NextResponse.json({
      content: sanitizedContent,
      version: latestWaiver.version,
    });
  } catch (_error) {
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_FETCH_WAIVER }, { status: 500 });
  }
}
