import { NextRequest, NextResponse } from 'next/server';

import { verifyClaimTap } from '@/src/features/self-checkout/anonymous/server/services/verificationService';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  logServer(LogLevel.INFO, 'API: /self-checkout/anonymous/verify-tap', 'Request received');

  try {
    // 1. Verify App Check
    const appCheckResult = await verifyAppCheckToken(req); // Replay protection likely not needed for verification
    if (appCheckResult) {
      logServer(
        LogLevel.WARN,
        'API: /self-checkout/anonymous/verify-tap',
        'App Check verification failed',
        { error: appCheckResult.error }
      );
      return NextResponse.json({ error: appCheckResult.error }, { status: appCheckResult.status });
    }
    logServer(LogLevel.DEBUG, 'API: /self-checkout/anonymous/verify-tap', 'App Check verified');

    // 2. Verify Attempt Token Header
    const attemptToken = req.headers.get('X-Attempt-Token');
    if (!attemptToken) {
      logServer(
        LogLevel.WARN,
        'API: /self-checkout/anonymous/verify-tap',
        'Missing X-Attempt-Token header'
      );
      return NextResponse.json({ error: 'Missing required attempt token.' }, { status: 401 });
    }

    // 3. Call Verification Service
    const result = await verifyClaimTap(attemptToken);

    const duration = Date.now() - startTime;
    logServer(
      LogLevel.INFO,
      'API: /self-checkout/anonymous/verify-tap',
      `Request processed successfully in ${duration}ms`,
      { success: result.success }
    );

    // 4. Return Success Response (contains { success: true/false })
    return NextResponse.json(result, { status: 200 });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    logServer(
      LogLevel.ERROR,
      'API: /self-checkout/anonymous/verify-tap',
      `Request failed after ${duration}ms`,
      { error: error.message, stack: error.stack }
    );

    // Determine status code based on error type if possible, otherwise default to 500
    const statusCode = error.statusCode || 500; // Use status code attached by the service
    const errorMessage = error.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR;

    // For verification, even if an error occurs (like 404 burn order), maybe return { success: false }?
    // Let's stick to returning errors for actual exceptions.
    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}
