import { NextRequest, NextResponse } from 'next/server';

import { initializeFlow } from '@/src/features/self-checkout/anonymous/server/services/initializationService';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  logServer(LogLevel.INFO, 'API: /self-checkout/anonymous/init', 'Request received');

  try {
    // 1. Verify App Check
    const appCheckResult = await verifyAppCheckToken(req);
    if (appCheckResult) {
      logServer(
        LogLevel.WARN,
        'API: /self-checkout/anonymous/init',
        'App Check verification failed',
        { error: appCheckResult.error }
      );
      return NextResponse.json({ error: appCheckResult.error }, { status: appCheckResult.status });
    }
    logServer(LogLevel.DEBUG, 'API: /self-checkout/anonymous/init', 'App Check verified');

    // 2. Get Existing Token (if any)
    // The claim token identifies the user's device/browser session for listing past purchases.
    const existingClaimToken = req.headers.get('X-Claim-Attempt-Token') ?? undefined;

    // 3. Get locationId from request body (if provided by client)
    // This locationId is used to fetch the dynamic catalog for that specific location.
    let locationIdFromRequest: string | undefined;
    try {
      // Check if request has a body and attempt to parse it
      if (req.body) {
        const body = await req.json();
        locationIdFromRequest = body?.locationId;
        logServer(
          LogLevel.DEBUG,
          'API: /self-checkout/anonymous/init',
          'Parsed locationId from request body',
          { locationIdFromRequest }
        );
      } else {
        logServer(
          LogLevel.DEBUG,
          'API: /self-checkout/anonymous/init',
          'No request body found for locationId.'
        );
      }
    } catch (e) {
      // If parsing fails (e.g., empty body, not JSON), log but don't fail the request.
      // initializeFlow will use default if locationIdFromRequest is undefined.
      logServer(
        LogLevel.DEBUG,
        'API: /self-checkout/anonymous/init',
        'Could not parse locationId from request body, or body empty.',
        { error: (e as Error).message }
      );
    }

    // 4. Call Initialization Service
    const result = await initializeFlow(existingClaimToken, locationIdFromRequest);

    const duration = Date.now() - startTime;
    logServer(
      LogLevel.INFO,
      'API: /self-checkout/anonymous/init',
      `Request processed successfully in ${duration}ms`
    );

    // 5. Return Success Response
    return NextResponse.json(result, { status: 200 });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    logServer(
      LogLevel.ERROR,
      'API: /self-checkout/anonymous/init',
      `Request failed after ${duration}ms`,
      { error: error.message, stack: error.stack }
    );

    // Determine status code based on error type if possible, otherwise default to 500
    const statusCode = error.statusCode || 500;
    const errorMessage = error.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR;

    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}
