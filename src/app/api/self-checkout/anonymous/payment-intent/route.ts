import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

import { paymentIntentPayloadSchema } from '@/src/features/self-checkout/anonymous/server/anonymousValidationSchemas';
import { createPaymentIntent } from '@/src/features/self-checkout/anonymous/server/services/paymentIntentService';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  logServer(LogLevel.INFO, 'API: /self-checkout/anonymous/payment-intent', 'Request received');

  try {
    // 1. Verify App Check (with replay protection)
    const appCheckResult = await verifyAppCheckToken(req, true); // Enable replay protection
    if (appCheckResult) {
      logServer(
        LogLevel.WARN,
        'API: /self-checkout/anonymous/payment-intent',
        'App Check verification failed',
        { error: appCheckResult.error }
      );
      return NextResponse.json({ error: appCheckResult.error }, { status: appCheckResult.status });
    }
    logServer(LogLevel.DEBUG, 'API: /self-checkout/anonymous/payment-intent', 'App Check verified');

    // 2. Verify Claim Token Header
    const claimToken = req.headers.get('X-Claim-Attempt-Token');
    if (!claimToken) {
      logServer(
        LogLevel.WARN,
        'API: /self-checkout/anonymous/payment-intent',
        'Missing X-Claim-Attempt-Token header'
      );
      return NextResponse.json({ error: 'Missing required session token.' }, { status: 401 });
    }

    // 3. Parse and Validate Request Body
    let parsedBody; // Renamed to avoid conflict with service function params
    try {
      const rawBody = await req.json();
      parsedBody = paymentIntentPayloadSchema.parse(rawBody); // Validate the entire body
      logServer(
        LogLevel.DEBUG,
        'API: /self-checkout/anonymous/payment-intent',
        'Request body validated',
        { receivedLocationId: parsedBody.locationId } // Log if locationId was received
      );
    } catch (error: any) {
      if (error instanceof ZodError) {
        logServer(
          LogLevel.WARN,
          'API: /self-checkout/anonymous/payment-intent',
          'Request body validation failed',
          { errors: error.flatten() }
        );
        return NextResponse.json(
          { error: 'Invalid request data.', details: error.flatten() },
          { status: 400 }
        );
      }
      logServer(
        LogLevel.ERROR,
        'API: /self-checkout/anonymous/payment-intent',
        'Failed to parse request body',
        { error: error.message }
      );
      return NextResponse.json({ error: 'Invalid request format.' }, { status: 400 });
    }

    // Destructure after validation
    const { email, selectedItems, locationId } = parsedBody;

    // 4. Get Configuration
    // SELF_CHECKOUT_CLAMSHELL_PURCHASE_LIMIT is now a global per-item limit.
    // SELF_CHECKOUT_CLAMSHELL_VARIATION_ID is no longer used here to determine *what* can be bought.
    // The `configuredVariationId` below might be used for specific validation if a single-item flow was still partially supported,
    // but for dynamic catalog, this specific ID's role changes or is removed from this service's direct use.
    const configuredLimit = parseInt(process.env.SELF_CHECKOUT_CLAMSHELL_PURCHASE_LIMIT || '0', 10);

    if (configuredLimit <= 0) {
      logServer(
        LogLevel.ERROR,
        'API: /self-checkout/anonymous/payment-intent',
        'Server configuration missing or invalid (Limit/VariationID)'
      );
      // return NextResponse.json({ error: ERROR_MESSAGES.INTERNAL_SERVER_ERROR }, { status: 500 }); // VariationID check removed for dynamic catalog
    }

    // 5. Call Payment Intent Service
    // Pass the extracted locationId (which can be undefined) to the service function
    const result = await createPaymentIntent(
      email,
      selectedItems,
      claimToken,
      configuredLimit,
      locationId // Pass the locationId from the parsed body
    );

    const duration = Date.now() - startTime;
    logServer(
      LogLevel.INFO,
      'API: /self-checkout/anonymous/payment-intent',
      `Request processed successfully in ${duration}ms`
    );

    // 6. Return Success Response
    return NextResponse.json(result, { status: 200 });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    logServer(
      LogLevel.ERROR,
      'API: /self-checkout/anonymous/payment-intent',
      `Request failed after ${duration}ms`,
      { error: error.message, stack: error.stack }
    );

    const statusCode = error.statusCode || (error.message?.includes('limit') ? 400 : 500);
    const errorMessage = error.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR;

    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}
