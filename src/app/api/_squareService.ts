import { CalculateOrderRequest, Order, OrderLineItemDiscount, CatalogObject } from 'square'; // Removed unused Money and OrderLineItemAppliedDiscount

import { client as squareClient } from '@/src/app/api/square/_squareConfig';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import {
  ItemDiscountDetailsResult,
  ItemDiscountValidationResult,
  DiscountTargetType,
} from '@/src/types/ItemDiscount';

import { adminDb } from './_firebaseAdmin';

// Helper function to get product price
async function getOriginalProductPrice(productVariationId: string): Promise<number | null> {
  if (!productVariationId) return null;
  try {
    const response = await squareClient.catalogApi.retrieveCatalogObject(productVariationId, true);
    const variationData = response.result.object?.itemVariationData;
    if (
      variationData?.priceMoney?.amount !== undefined &&
      variationData?.priceMoney?.amount !== null
    ) {
      return Number(variationData.priceMoney.amount);
    }
    console.warn(`Price not found for variation ${productVariationId}`);
    return null;
  } catch (error: unknown) {
    const errorDetails = error instanceof Error ? error.message : String(error);
    const statusCode = (error as any)?.statusCode;
    console.error(
      `Error fetching original price for variation ${productVariationId}:`,
      errorDetails
    );
    if (statusCode !== 404) {
      console.error(`Non-404 error (${statusCode}) fetching price for ${productVariationId}`);
    }
    return null;
  }
}

// --- Service Functions ---

/**
 * Validates a discount code, determines target, calculates potential discount via Square,
 * applies manual capping, and returns detailed validation results.
 */
export async function validateDiscount(
  discountCode: string,
  productVariationId: string,
  discountId?: string
): Promise<ItemDiscountValidationResult> {
  const logPrefix = '[Validate Discount Service]';
  try {
    if (!discountId) {
      // 1. Search for the discount by code
      const searchResponse = await squareClient.catalogApi.searchCatalogObjects({
        objectTypes: ['DISCOUNT'],
        query: { textQuery: { keywords: [discountCode] } },
        includeDeletedObjects: false,
      });

      const matchingDiscountSearchResult = searchResponse.result.objects?.find(
        obj =>
          obj.discountData?.name?.toLowerCase() === discountCode.toLowerCase() && !obj.isDeleted
      );

      if (!matchingDiscountSearchResult?.id) {
        return { isValid: false, message: 'Invalid or expired discount code.' };
      }
      discountId = matchingDiscountSearchResult.id;
    }
    // 2. Retrieve the full discount object
    const discountDetails = await fetchDiscountDetails(discountId);
    if (!discountDetails) {
      console.warn(`${logPrefix} Could not retrieve full details for discount ID: ${discountId}`);
      return { isValid: false, message: 'Could not retrieve full discount details.' };
    }
    const fullDiscountObject = discountDetails.fullObject;
    const discountData = fullDiscountObject?.discountData;
    if (!discountData) {
      console.warn(`${logPrefix} Discount data missing for ID: ${discountId}`);
      return { isValid: false, message: 'Discount data missing.' };
    }

    // 3. Determine Target Type
    const discountName = discountData.name ?? discountCode;

    // Check Firestore for custom targeting rules
    let targetType: DiscountTargetType = 'PRODUCT'; // Default to product

    try {
      const discountRef = adminDb.collection(COLLECTIONS.DISCOUNT_CODES).doc(discountName);
      const discountDoc = await discountRef.get();

      if (discountDoc.exists) {
        const firestoreData = discountDoc.data();
        const applyOn = firestoreData?.apply_on || [];

        // If delivery fee variation ID is in the apply_on array, target delivery fee
        const deliveryFeeVariationId =
          process.env.NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID;

        if (applyOn.length > 0 && applyOn.includes(deliveryFeeVariationId)) {
          targetType = 'DELIVERY_FEE';
        }
      }
    } catch (error) {
      console.warn(
        `${logPrefix} Error checking Firestore discount targeting, using PRODUCT:`,
        error
      );
    }

    // 4. Get Variation IDs and Original Prices
    const deliveryFeeVariationId = process.env.NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID;
    if (!deliveryFeeVariationId) {
      console.error(`${logPrefix} Delivery Fee Variation ID not configured.`);
      return { isValid: false, message: 'Server configuration error (delivery fee).' };
    }
    if (!productVariationId) {
      console.error(`${logPrefix} Product Variation ID is missing.`);
      return { isValid: false, message: 'Product information missing.' };
    }

    const [originalProductPrice, originalDeliveryFeePrice] = await Promise.all([
      getOriginalProductPrice(productVariationId),
      getOriginalProductPrice(deliveryFeeVariationId),
    ]);

    if (originalProductPrice === null || originalDeliveryFeePrice === null) {
      console.error(`${logPrefix} Could not fetch original prices.`);
      return { isValid: false, message: 'Could not retrieve product/delivery price information.' };
    }

    // 5. Use CalculateOrder API to get potential discount amount
    let calculatedDiscountAmount: number | undefined; // In cents

    const locationId = process.env.NEXT_SQUARE_LOCATION_ID;
    if (!locationId) {
      console.error(`${logPrefix} Square Location ID not configured.`);
      return { isValid: false, message: 'Server configuration error (location).' };
    }

    const productLineItemUid = `product-li-${crypto.randomUUID()}`;
    const deliveryLineItemUid = `delivery-li-${crypto.randomUUID()}`;
    const discountUid = `applied-disc-${crypto.randomUUID()}`;

    const calcOrderRequest: CalculateOrderRequest = {
      order: {
        locationId: locationId,
        lineItems: [
          {
            catalogObjectId: productVariationId,
            quantity: '1',
            uid: productLineItemUid,
            appliedDiscounts: targetType === 'PRODUCT' ? [{ discountUid }] : undefined,
          },
          {
            catalogObjectId: deliveryFeeVariationId,
            quantity: '1',
            uid: deliveryLineItemUid,
            appliedDiscounts: targetType === 'DELIVERY_FEE' ? [{ discountUid }] : undefined,
          },
        ],
        discounts: [
          {
            uid: discountUid,
            catalogObjectId: discountId,
            scope: 'LINE_ITEM',
          } as OrderLineItemDiscount,
        ],
      } as Order,
    };

    try {
      const calcResponse = await squareClient.ordersApi.calculateOrder(calcOrderRequest);

      if (!calcResponse.result.order?.lineItems || !calcResponse.result.order?.totalMoney) {
        console.error(
          `${logPrefix} Invalid CalculateOrder response structure.`,
          calcResponse.result
        );
        return {
          isValid: false,
          message: 'Failed to calculate discount preview (invalid response).',
        };
      }

      const targetLineItemUid = targetType === 'PRODUCT' ? productLineItemUid : deliveryLineItemUid;
      const targetLineItem = calcResponse.result.order.lineItems.find(
        item => item.uid === targetLineItemUid
      );

      if (!targetLineItem?.totalDiscountMoney?.amount) {
        console.warn(`${logPrefix} No discount amount found on target line item (${targetType}).`);
        calculatedDiscountAmount = 0;
      } else {
        calculatedDiscountAmount = Number(targetLineItem.totalDiscountMoney.amount);
      }
      // Removed unused assignment: calculatedFirstCycleTotal = Number(calcResponse.result.order.totalMoney.amount);
    } catch (calcError: unknown) {
      const errorBody = (calcError as any)?.body || '';
      const errorMessage = calcError instanceof Error ? calcError.message : String(calcError);
      console.error(`${logPrefix} Error calling CalculateOrder API:`, errorBody || errorMessage);
      return { isValid: false, message: 'Could not calculate discount preview.' };
    }

    // 6. Manual Capping Logic
    let finalDiscountAmount = calculatedDiscountAmount;
    const maxAmountMoney = discountData.maximumAmountMoney;
    const hasMaxAmount = maxAmountMoney?.amount !== null && maxAmountMoney?.amount !== undefined;

    if (hasMaxAmount && calculatedDiscountAmount !== undefined) {
      const maxDiscountAmount = Number(maxAmountMoney.amount);
      finalDiscountAmount = Math.min(calculatedDiscountAmount, maxDiscountAmount);
    }

    // 7. Recalculate First Cycle Total based on the *final* discount amount
    const finalFirstCycleTotal =
      originalProductPrice + originalDeliveryFeePrice - (finalDiscountAmount ?? 0);
    const finalCappedTotal = Math.max(0, finalFirstCycleTotal);

    // 8. Determine Discount Value for Response
    let responseValue: string | bigint;
    if (
      discountData.discountType === 'FIXED_PERCENTAGE' ||
      discountData.discountType === 'VARIABLE_PERCENTAGE'
    ) {
      responseValue = discountData.percentage ?? '0';
    } else {
      responseValue = discountData.amountMoney?.amount ?? BigInt(0);
    }

    // 9. Return Detailed Result
    return {
      isValid: true,
      message: 'Discount valid.',
      discountId: discountId,
      discountCodeName: discountName,
      type: discountData.discountType,
      value: responseValue,
      targetType: targetType,
      firstCycleTotalPreview: finalCappedTotal,
      discountAmount: finalDiscountAmount,
      originalProductPrice: originalProductPrice,
      originalDeliveryFeePrice: originalDeliveryFeePrice,
    };
  } catch (error: unknown) {
    const errorBody = (error as any)?.body || '';
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`${logPrefix} Unexpected error:`, errorBody || errorMessage);
    return { isValid: false, message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR };
  }
}

/**
 * Fetches discount details by ID, including maximumAmountMoney.
 */
export async function fetchDiscountDetails(
  discountId: string
): Promise<(ItemDiscountDetailsResult & { fullObject?: CatalogObject }) | null> {
  try {
    const response = await squareClient.catalogApi.retrieveCatalogObject(discountId);
    const obj = response.result.object;

    if (!obj || obj.isDeleted || !obj.discountData?.name) {
      return null;
    }
    return {
      id: obj.id,
      codeName: obj.discountData.name,
      maximumAmountMoney: obj.discountData.maximumAmountMoney,
      fullObject: obj,
    };
  } catch (error: unknown) {
    const errorBody = (error as any)?.body || '';
    const errorMessage = error instanceof Error ? error.message : String(error);
    const statusCode = (error as any)?.statusCode;
    console.error(`Error fetching discount details for ${discountId}:`, errorBody || errorMessage);
    if (statusCode === 404) return null;
    throw error;
  }
}
