import { DocumentReference } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';
import { Orders } from '@/src/types/Orders';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

/**
 * Handles GET requests to retrieve all tickets for a specific user and their orders
 *
 * @returns {Promise<Response>} JSON response with tickets
 */
export async function GET(req: NextRequest) {
  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Extract userId from URL query parameters
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    // Validate that userId is provided
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Create reference to user document and verify it exists
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
    const userDoc = await userRef.get();

    // Return 404 if user doesn't exist
    if (!userDoc.exists) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get all tickets for the user
    const userTicketsQuery = adminDb
      .collection(COLLECTIONS.TICKETS)
      .where('user_id', '==', userRef)
      .where('status', 'not-in', ['cancelled', 'draft', 'reserved', 'voided']);

    const userTicketsSnapshot = await userTicketsQuery.get();

    if (userTicketsSnapshot.empty) {
      return NextResponse.json(
        {
          tickets: [],
          message: 'No tickets found for user',
        },
        { status: 200 }
      );
    }

    // Extract unique order references from user's tickets
    const orderRefsMap = new Map();
    userTicketsSnapshot.forEach(doc => {
      const ticketData = doc.data();
      if (ticketData.order_id) {
        orderRefsMap.set(
          ticketData.order_id.id || ticketData.order_id.path.split('/').pop(),
          ticketData.order_id
        );
      }
    });

    if (orderRefsMap.size === 0) {
      return NextResponse.json(
        {
          tickets: [],
          message: 'No valid orders found for user tickets',
        },
        { status: 200 }
      );
    }

    // Cache for orders and bookers to avoid duplicate fetches
    const orderCache = new Map();
    const bookerCache = new Map();

    // Process tickets with optimized data fetching
    const tickets = await Promise.all(
      userTicketsSnapshot.docs.map(async ticketDoc => {
        const ticketData = ticketDoc.data() as Tickets;
        const ticketId = ticketDoc.id;

        // Get order data with caching
        let orderData = null;
        if (ticketData.order_id) {
          const orderId = ticketData.order_id.id || ticketData.order_id.path.split('/').pop();

          if (!orderCache.has(orderId)) {
            // Cast the reference to the correct type for Admin SDK
            const orderRef = ticketData.order_id as unknown as DocumentReference;
            const orderDoc = await orderRef.get();

            if (orderDoc.exists) {
              const orderDocData = orderDoc.data() as Orders;

              // Get booker data with caching
              let bookerData = null;
              if (orderDocData.order_by) {
                const bookerId =
                  orderDocData.order_by.id || orderDocData.order_by.path.split('/').pop();

                if (!bookerCache.has(bookerId)) {
                  // Cast the reference to the correct type for Admin SDK
                  const bookerRef = orderDocData.order_by as unknown as DocumentReference;
                  const bookerDoc = await bookerRef.get();

                  if (bookerDoc.exists) {
                    bookerData = {
                      id: bookerDoc.id,
                      ...bookerDoc.data(),
                    } as Users;
                    bookerCache.set(bookerId, bookerData);
                  }
                } else {
                  bookerData = bookerCache.get(bookerId);
                }
              }

              // Create order object with all order data and booker
              orderData = {
                id: orderDoc.id,
                ...orderDocData,
                booker: bookerData,
              };
              orderCache.set(orderId, orderData);
            }
          } else {
            orderData = orderCache.get(orderId);
          }
        }

        return {
          id: ticketId,
          ...ticketData,
          user: {
            id: userId,
            ...userDoc.data(),
          } as Users,
          order_id: ticketData.order_id?.id || ticketData.order_id?.path?.split('/').pop(),
          order: orderData,
        };
      })
    );

    return NextResponse.json(
      {
        tickets,
        message: 'Tickets retrieved successfully',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return NextResponse.json({ error: 'Failed to retrieve tickets' }, { status: 500 });
  }
}
