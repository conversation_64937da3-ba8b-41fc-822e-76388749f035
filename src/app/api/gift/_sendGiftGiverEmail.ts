import sgMail from '@sendgrid/mail';

// Initialize SendGrid
if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

interface SendGiftGiverEmailParams {
  gifter_email: string;
  gifter_name: string;
  recipients: Array<{
    firstName: string;
    lastName: string;
    email: string;
  }>;
  productName: string;
  variationName: string;
  totalAmount: number;
  quantity: number;
  isMultipleGift: boolean;
}

/**
 * Sends a gift confirmation email to the giver using SendGrid
 */
export default async function sendGiftGiverEmail({
  gifter_email,
  gifter_name,
  recipients,
  productName,
  variationName,
  totalAmount,
  quantity,
  isMultipleGift,
}: SendGiftGiverEmailParams): Promise<boolean> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      console.error('SENDGRID_API_KEY is not defined');
      return false;
    }

    if (!process.env.SENDGRID_FROM_EMAIL || !process.env.SENDGRID_SUBSCRIPTION_GIVER_TEMPLATE_ID) {
      console.error('Missing required SendGrid configuration');
      return false;
    }

    const msg = {
      to: gifter_email,
      from: {
        email: process.env.SENDGRID_FROM_EMAIL,
        name: process.env.SENDGRID_FROM_NAME,
      },
      templateId: process.env.SENDGRID_SUBSCRIPTION_GIVER_TEMPLATE_ID,
      dynamicTemplateData: {
        gifter_name,
        recipients,
        product_name: productName,
        variation_name: variationName,
        total_amount: totalAmount,
        quantity,
        my_verymulberry_link: process.env.NEXT_PUBLIC_BASE_URL,
        isMultipleGift,
        isOneTime: true,
      },
    };

    await sgMail.send(msg);
    return true;
  } catch (error) {
    console.error('Error sending gift giver email:', JSON.stringify(error));
    return false;
  }
}
