import crypto from 'crypto';

import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';
import { Payment as SquarePayment, Order as SquareOrder } from 'square';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import sendGiftGiverEmail from '@/src/app/api/gift/_sendGiftGiverEmail';
import sendGiftRecipientEmail from '@/src/app/api/gift/_sendGiftRecipientEmail';
import { client, JSONBigInt } from '@/src/app/api/square/_squareConfig';
import { COLLECTIONS, ERROR_MESSAGES, SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

interface Recipient {
  firstName: string;
  lastName: string;
  email: string;
  giftMessage?: string;
}

export async function POST(req: NextRequest) {
  const logPrefix = '[Create Gift Order API]';
  let orderRef: FirebaseFirestore.DocumentReference | null = null;
  let cardId: string | undefined;
  let locationId: string | undefined;

  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      console.warn(`${logPrefix} App Check verification failed.`);
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Parse request data
    const requestData = await req.json();
    const {
      variationId,
      variationName,
      variationImage,
      productName,
      productPrice,
      deliveryCharge,
      recipients,
      giftMessage,
      sourceId,
      firstName,
      lastName,
      email,
    } = requestData;

    // Basic field validation
    if (
      !variationId ||
      !productName ||
      !productPrice ||
      !deliveryCharge ||
      !recipients?.length ||
      !sourceId ||
      !firstName ||
      !lastName ||
      !email
    ) {
      console.error(`${logPrefix} Missing required fields.`);
      return NextResponse.json(
        { error: 'Internal Error: Missing required fields' },
        { status: 400 }
      );
    }

    // --- Product & Delivery Fee Info ---
    const deliveryFeeVariationId = process.env.NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID;
    const productId = process.env.NEXT_SQUARE_SUBSCRIPTION_PRODUCT_ID;
    locationId = process.env.NEXT_SQUARE_LOCATION_ID;

    if (!deliveryFeeVariationId || !productId || !locationId) {
      console.error(
        `${logPrefix} Essential Env Vars missing: ProductID, DeliveryFeeVarID, or LocationID.`
      );
      return NextResponse.json({ error: 'Server configuration error.' }, { status: 500 });
    }

    // Get or create customer
    let customerId: string;
    try {
      // Search for existing customer by email
      const searchResponse = await client.customersApi.searchCustomers({
        query: {
          filter: {
            emailAddress: {
              exact: email,
            },
          },
        },
      });

      const existingCustomer = searchResponse.result.customers?.[0];

      if (existingCustomer?.id) {
        customerId = existingCustomer.id;
      } else {
        // Create new customer if not found
        const createResponse = await client.customersApi.createCustomer({
          idempotencyKey: crypto.randomUUID(),
          givenName: firstName,
          familyName: lastName,
          emailAddress: email,
        });

        if (!createResponse.result.customer?.id) {
          throw new Error('Failed to create customer');
        }

        customerId = createResponse.result.customer.id;
      }
    } catch (err) {
      console.error(`${logPrefix} Failed to get or create customer.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to get or create customer' }, { status: 500 });
    }

    // --- Create Square Card ---
    const displayName = `${firstName} ${lastName}`;
    const cardParams = {
      idempotencyKey: crypto.randomUUID(),
      sourceId: sourceId as string,
      card: { customerId, cardholderName: displayName },
    };

    try {
      const cardResponse = await client.cardsApi.createCard(cardParams);
      cardId = cardResponse.result.card?.id;
    } catch (err) {
      console.error(`${logPrefix} Failed to create card.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Invalid payment card data' }, { status: 500 });
    }

    const baseLineItems = [
      {
        catalogObjectId: variationId as string,
        quantity: String(recipients.length),
      },
      {
        catalogObjectId: deliveryFeeVariationId as string,
        quantity: '1',
      },
    ];

    const orderParams: any = {
      idempotencyKey: crypto.randomUUID(),
      order: {
        locationId,
        customerId,
        lineItems: baseLineItems,
        source: { name: 'My Very Mulberry Home Delivery' },
        metadata: {
          giftBy: displayName,
          orderFor: SUBSCRIPTION_ORDER_TYPES.MULTIPLE_GIFT,
          ...(giftMessage && { gift_message: giftMessage }),
        },
      },
    };

    let orderObject: SquareOrder | undefined;
    let orderId, totalMoney;
    try {
      // Create the order
      const orderResponse = await client.ordersApi.createOrder(orderParams);
      orderObject = orderResponse.result.order;
      orderId = orderResponse.result.order?.id;
      totalMoney = orderResponse.result.order?.totalMoney;
    } catch (err) {
      console.error(`${logPrefix} Failed to create order.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to create order' }, { status: 500 });
    }

    let paymentObject: SquarePayment | undefined;
    try {
      // Process payment for the order
      const paymentParams = {
        idempotencyKey: crypto.randomUUID(),
        sourceId: cardId as string,
        orderId: orderId,
        customerId,
        locationId: locationId,
        amountMoney: totalMoney,
      };

      const paymentResponse = await client.paymentsApi.createPayment(paymentParams);
      paymentObject = paymentResponse.result.payment;
    } catch (err) {
      console.error(`${logPrefix} Failed to process payment.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to process payment' }, { status: 500 });
    }

    // Create individual orders for each recipient
    let recipientOrders;
    try {
      recipientOrders = await Promise.all(
        recipients.map(async (recipient: Recipient) => {
          if (!locationId) {
            throw new Error('Location ID is required');
          }

          if (!paymentObject?.id) {
            throw new Error('Payment ID is required');
          }

          if (!orderId) {
            throw new Error('Order ID is required');
          }

          const recipientOrder = await client.ordersApi.createOrder({
            idempotencyKey: crypto.randomUUID(),
            order: {
              locationId,
              customerId,
              lineItems: [
                {
                  catalogObjectId: variationId as string,
                  quantity: '1',
                },
              ],
              state: 'DRAFT',
              source: {
                name: 'My Very Mulberry Home Delivery',
              },
              fulfillments: [
                {
                  type: 'SHIPMENT',
                  shipmentDetails: {
                    recipient: {
                      displayName: `${recipient.firstName} ${recipient.lastName}`,
                      emailAddress: recipient.email,
                    },
                  },
                },
              ],
              metadata: {
                parentOrderId: orderId,
                parentPaymentId: paymentObject.id,
                giftBy: displayName,
                orderFor: SUBSCRIPTION_ORDER_TYPES.MULTIPLE_GIFT,
                ...(giftMessage && { gift_message: giftMessage }),
              },
            },
          });

          return recipientOrder.result.order;
        })
      );
    } catch (err) {
      console.error(`${logPrefix} Failed to create recipient orders.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to create recipient orders' }, { status: 500 });
    }

    // Store recipient orders in Firestore
    try {
      await Promise.all(
        recipientOrders.map(async recipientOrder => {
          const recipientOrderData = {
            square_order_id: recipientOrder?.id,
            square_payment_id: paymentObject?.id,
            square_product_id: productId,
            square_variation_id: variationId,
            product_name: productName,
            variation_name: variationName,
            variation_image: variationImage,
            product_price: productPrice,
            delivery_charge: deliveryCharge,
            status: 'draft',
            gift_by: displayName,
            order_for: SUBSCRIPTION_ORDER_TYPES.MULTIPLE_GIFT,
            parent_order_id: orderId,
            parent_payment_id: paymentObject?.id,
            created_at: Timestamp.now(),
            updated_at: Timestamp.now(),
            square_order: JSON.parse(JSONBigInt.stringify(recipientOrder)),
          };

          return adminDb.collection(COLLECTIONS.GIFT_ORDERS).add(recipientOrderData);
        })
      );
    } catch (err) {
      console.error(
        `${logPrefix} Failed to store recipient orders in Firestore.`,
        JSONBigInt.stringify(err)
      );
      return NextResponse.json({ error: 'Failed to store recipient orders' }, { status: 500 });
    }

    // After creating the order and payment, and before returning the response
    if (orderId && paymentObject) {
      try {
        // Send email to gift giver
        await sendGiftGiverEmail({
          gifter_email: email,
          gifter_name: displayName,
          recipients: recipients.map((r: Recipient) => ({
            name: `${r.firstName} ${r.lastName}`,
            email: r.email,
            giftMessage: giftMessage,
          })),
          productName: productName,
          variationName: variationName,
          totalAmount: Number(totalMoney?.amount ?? 0),
          quantity: recipients.length,
          isMultipleGift: true,
        });

        // Prepare recipient email data with individual order IDs
        const recipientEmailData = recipients.map((recipient: Recipient, index: number) => ({
          gifter_name: displayName,
          gifter_email: email,
          recipient_name: `${recipient.firstName} ${recipient.lastName}`,
          recipient_email: recipient.email,
          productName: productName,
          variationName: variationName,
          giftMessage: giftMessage,
          isMultipleGift: true,
          squareOrderId: recipientOrders[index]?.id || '',
        }));

        // Send emails to all recipients in bulk
        await sendGiftRecipientEmail({ recipients: recipientEmailData });
      } catch (emailError) {
        console.error(
          `${logPrefix} Error sending confirmation emails:`,
          emailError instanceof Error ? emailError.message : String(emailError)
        );
        // Continue with the order process even if email sending fails
      }
    }

    // Create Firestore record
    try {
      const orderData: any = {
        square_order_id: orderId,
        square_payment_id: paymentObject?.id,
        square_product_id: productId,
        square_variation_id: variationId,
        product_name: productName,
        variation_name: variationName,
        variation_image: variationImage,
        product_price: productPrice,
        delivery_charge: deliveryCharge,
        recipients: recipients,
        gift_message: giftMessage,
        gift_by: displayName,
        order_for: SUBSCRIPTION_ORDER_TYPES.MULTIPLE_GIFT,
        status: 'completed',
        created_at: Timestamp.now(),
        updated_at: Timestamp.now(),
        square_order: JSON.parse(JSONBigInt.stringify(orderObject)),
      };

      orderRef = await adminDb.collection(COLLECTIONS.GIFT_ORDERS).add(orderData);

      // Return success response
      return NextResponse.json({
        success: true,
        orderId: orderRef.id,
        squareOrderId: orderId,
      });
    } catch (err) {
      console.error(`${logPrefix} Failed to store order in Firestore.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to store order' }, { status: 500 });
    }
  } catch (error: unknown) {
    console.error(`${logPrefix} Error:`, error instanceof Error ? error.message : String(error));
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_CREATE_ORDER }, { status: 500 });
  }
}
