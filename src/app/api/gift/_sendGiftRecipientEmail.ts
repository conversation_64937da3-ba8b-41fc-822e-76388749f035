import sgMail from '@sendgrid/mail';

// Initialize SendGrid
if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

interface RecipientEmailData {
  gifter_name: string;
  gifter_email: string;
  recipient_name: string;
  recipient_email: string;
  productName: string;
  giftMessage?: string;
  isMultipleGift: boolean;
  squareOrderId: string;
}

interface SendGiftRecipientEmailParams {
  recipients: RecipientEmailData[];
}

/**
 * Sends gift notification emails to multiple recipients using SendGrid's bulk send feature
 */
export default async function sendGiftRecipientEmail({
  recipients,
}: SendGiftRecipientEmailParams): Promise<boolean> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      console.error('SENDGRID_API_KEY is not defined');
      return false;
    }

    if (
      !process.env.SENDGRID_FROM_EMAIL ||
      !process.env.SENDGRID_SUBSCRIPTION_RECIPIENT_TEMPLATE_ID
    ) {
      console.error('Missing required SendGrid configuration');
      return false;
    }

    const fromEmail = process.env.SENDGRID_FROM_EMAIL;
    const fromName = process.env.SENDGRID_FROM_NAME || 'Very Mulberry';
    const templateId = process.env.SENDGRID_SUBSCRIPTION_RECIPIENT_TEMPLATE_ID;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || '';

    // Prepare messages for bulk send
    const messages = recipients.map(recipient => ({
      to: recipient.recipient_email,
      from: {
        email: fromEmail,
        name: fromName,
      },
      templateId,
      dynamicTemplateData: {
        gifter_name: recipient.gifter_name,
        gifter_email: recipient.gifter_email,
        recipient_name: recipient.recipient_name,
        product_name: recipient.productName,
        gift_message: recipient.giftMessage || '',
        isMultipleGift: recipient.isMultipleGift,
        isOneTime: true,
        squareOrderId: recipient.squareOrderId,
        my_verymulberry_link: baseUrl,
      },
    }));

    // Send all emails in bulk
    await sgMail.send(messages);
    return true;
  } catch (error) {
    console.error('Error sending gift recipient emails:', JSON.stringify(error));
    return false;
  }
}
