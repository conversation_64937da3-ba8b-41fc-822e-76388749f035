import { NextRequest, NextResponse } from 'next/server';

import { client, JSONBigInt, client as squareClient } from '@/src/app/api/square/_squareConfig';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(req: NextRequest) {
  const logPrefix = '[Update Gift Order API]';

  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      console.warn(`${logPrefix} App Check verification failed.`);
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const {
      orderId,
      firstName,
      lastName,
      email,
      phone,
      addressLine1,
      addressLine2,
      city,
      zipCode,
      deliveryNotes,
    } = await req.json();

    if (
      !orderId ||
      !firstName ||
      !lastName ||
      !email ||
      !phone ||
      !addressLine1 ||
      !city ||
      !zipCode
    ) {
      return NextResponse.json({ error: 'Missing required fields is required' }, { status: 400 });
    }

    const locationId = process.env.NEXT_SQUARE_LOCATION_ID;

    if (!locationId) {
      console.error(`${logPrefix} Essential Env Vars missing: LocationID.`);
      return NextResponse.json({ error: 'Server configuration error.' }, { status: 500 });
    }

    const displayName = firstName + ' ' + lastName;

    // Retrieve the order first
    const {
      result: { order },
    } = await squareClient.ordersApi.retrieveOrder(orderId);

    if (!order) {
      return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_FETCH_ORDER }, { status: 404 });
    }

    // Get or create customer
    let customerId: string;
    try {
      // Search for existing customer by email
      const searchResponse = await client.customersApi.searchCustomers({
        query: {
          filter: {
            emailAddress: {
              exact: email,
            },
          },
        },
      });

      const existingCustomer = searchResponse.result.customers?.[0];

      if (existingCustomer?.id) {
        customerId = existingCustomer.id;
      } else {
        const address =
          addressLine1 && city && zipCode
            ? {
                addressLine1,
                addressLine2: addressLine2 || '',
                locality: city,
                administrativeDistrictLevel1: 'CA',
                postalCode: zipCode,
                country: 'US',
              }
            : undefined;

        // Create new customer if not found
        const createResponse = await client.customersApi.createCustomer({
          idempotencyKey: crypto.randomUUID(),
          emailAddress: email,
          givenName: firstName,
          familyName: lastName,
          phoneNumber: phone,
          referenceId: email,
          address: address,
        });

        if (!createResponse.result.customer?.id) {
          throw new Error('Failed to create customer');
        }

        customerId = createResponse.result.customer.id;
      }
    } catch (err) {
      console.error(`${logPrefix} Failed to get or create customer.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to get or create customer' }, { status: 500 });
    }

    const orderData: any = {
      order: {
        fulfillments: [
          {
            uid: order?.fulfillments?.[0]?.uid,
            shipmentDetails: {
              recipient: {
                customerId,
                phoneNumber: phone,
                displayName,
                email_address: email,
                address: {
                  addressLine1: addressLine1,
                  addressLine2: addressLine2 || '',
                  locality: city,
                  administrativeDistrictLevel1: 'CA',
                  postalCode: zipCode,
                  country: 'US',
                },
              },
            },
          },
        ],
        discounts: [
          {
            type: 'FIXED_PERCENTAGE',
            percentage: '100',
            scope: 'ORDER',
            name: 'Home Delivery Gift',
          },
        ],
        state: 'OPEN',
        version: order.version,
        locationId,
      },
      idempotencyKey: crypto.randomUUID(),
    };

    if (deliveryNotes) {
      orderData.order.fulfillments[0].shipmentDetails.shippingNote = deliveryNotes;
    }

    // Update the order
    const { result } = await squareClient.ordersApi.updateOrder(orderId, orderData);

    return NextResponse.json({ orderId: result?.order?.id });
  } catch (error) {
    console.error('Error updating gift order:', error);
    return NextResponse.json(
      { error: ERROR_MESSAGES.FAILED_TO_UPDATE_ORDER_STATUS },
      { status: 500 }
    );
  }
}
