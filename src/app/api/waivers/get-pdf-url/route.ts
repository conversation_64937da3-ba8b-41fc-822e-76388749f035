import { getStorage } from 'firebase-admin/storage';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const pdfPath = searchParams.get('pdfPath');

    if (!pdfPath) {
      return NextResponse.json({ error: 'PDF path is required' }, { status: 400 });
    }

    const bucket = getStorage().bucket(process.env.FIREBASE_PRIVATE_BUCKET);
    const file = bucket.file(pdfPath);
    const [url] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
    });

    return NextResponse.json({ url });
  } catch (error) {
    console.error('Error getting PDF URL:', error);
    return NextResponse.json({ error: 'Failed to get PDF URL' }, { status: 500 });
  }
}
