import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { searchParams } = new URL(req.url);
    const email = searchParams.get('email');
    const firstName = searchParams.get('firstName');
    const lastName = searchParams.get('lastName');

    if (!email || !firstName || !lastName) {
      return NextResponse.json(
        { error: 'Email, firstName, and lastName are required' },
        { status: 400 }
      );
    }

    const currentDate = getPacificTimeNow();
    currentDate.setHours(0, 0, 0, 0);

    const waiversRef = adminDb.collection(COLLECTIONS.WAIVERS);
    const waiverSnapshot = await waiversRef
      .where('email', '==', email)
      .where('valid_until', '>', currentDate)
      .where('first_name', '==', firstName)
      .where('last_name', '==', lastName)
      .get();

    const hasValidWaiver = !waiverSnapshot.empty;

    return NextResponse.json({ hasValidWaiver });
  } catch (error) {
    console.error('Error checking waiver:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.GENERIC_ERROR }, { status: 500 });
  }
}
