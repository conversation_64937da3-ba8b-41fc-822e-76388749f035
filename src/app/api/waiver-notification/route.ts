import sgMail from '@sendgrid/mail';
import { format } from 'date-fns';
import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

// Validate required environment variables
const requiredEnvVars = [
  'SENDGRID_API_KEY',
  'SENDGRID_FROM_EMAIL',
  'SENDGRID_WAIVER_NOTIFICATION_TEMPLATE_ID',
  'NEXT_PUBLIC_BASE_URL',
] as const;

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`${envVar} is not defined`);
  }
}

if (!process.env.SENDGRID_API_KEY) {
  throw new Error('SENDGRID_API_KEY is not defined');
}

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

export async function POST(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { email, token } = await req.json();

    if (!email || typeof email !== 'string') {
      return NextResponse.json({ error: 'Valid email is required' }, { status: 400 });
    }

    const templateData = {
      sign_waiver: `${process.env.NEXT_PUBLIC_BASE_URL}/sign-waiver?t=${token}`,
      subject: `Waiver Notification - ${format(getPacificTimeNow(), "dd-MMM-yyyy HH:mm 'UTC'").toUpperCase()}`,
    };

    const msg = {
      to: email,
      from: process.env.SENDGRID_FROM_EMAIL as string,
      templateId: process.env.SENDGRID_WAIVER_NOTIFICATION_TEMPLATE_ID as string,
      dynamicTemplateData: templateData,
    } as const;

    try {
      const [_response] = await sgMail.send(msg);
    } catch (sendGridError: any) {
      console.error('SendGrid Detailed Error:', {
        message: sendGridError.message,
        response: sendGridError.response?.body,
        code: sendGridError.code,
      });
      throw sendGridError;
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('SendGrid Error:', error);

    // Provide more specific error messages based on the error type
    const errorMessage =
      error.response?.body?.errors?.[0]?.message || 'Failed to send waiver notification';

    return NextResponse.json(
      { error: errorMessage, success: false },
      { status: error.code || 500 }
    );
  }
}
