import { DocumentReference, Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Orders } from '@/src/types/Orders';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

import { client, JSONBigInt } from '../square/_squareConfig';

export const POST = async (req: NextRequest) => {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { order_id, square_transaction_id, square_order_id } = await req.json();

    if (!order_id || !square_transaction_id || !square_order_id) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
      });
    }

    // Get Square order details using the order ID from request
    const squareOrder = await client.ordersApi.retrieveOrder(square_order_id);

    if (!squareOrder.result || !squareOrder.result.order) {
      console.error('No order found in response:', JSONBigInt.stringify(squareOrder));
      return new Response(JSON.stringify({ error: 'Order not found in Square' }), {
        status: 404,
      });
    }

    const order = squareOrder.result.order;

    // 2. Get payment information
    if (!order.tenders || order.tenders.length === 0) {
      console.error('No tenders found in order:', JSONBigInt.stringify(order));
      return new Response(
        JSON.stringify({ error: 'No payment information found for this order' }),
        {
          status: 404,
        }
      );
    }

    const paymentId = order?.tenders[0]?.paymentId;
    if (!paymentId) {
      return new Response(JSON.stringify({ error: 'Payment ID not found in order' }), {
        status: 404,
      });
    }

    const paymentResponse = await client.paymentsApi.getPayment(paymentId);

    if (!paymentResponse.result || !paymentResponse.result.payment) {
      console.error('No payment found in response:', JSONBigInt.stringify(paymentResponse));

      return new Response(JSON.stringify({ error: 'Payment details not found for this order' }), {
        status: 404,
      });
    }

    const payment = paymentResponse.result.payment;

    // Check if payment already exists
    const paymentsRef = adminDb.collection(COLLECTIONS.PAYMENTS);
    const paymentSnapshot = await paymentsRef.where('square_payment_id', '==', payment.id).get();

    const orderRef = adminDb.collection(COLLECTIONS.ORDERS).doc(order_id);

    const paymentData = {
      order_id: orderRef,
      method: payment.sourceType as 'credit_card' | 'apple_pay' | 'CASH',
      gateway: 'square',
      status: payment.status === 'COMPLETED' ? 'successful' : 'failed',
      amount: Number(payment.amountMoney?.amount || 0),
      payment_date: payment.createdAt ? new Date(payment.createdAt) : getPacificTimeNow(),
      square_payment_id: payment.id,
      metadata: {
        square_order_id,
        square_transaction_id,
      },
      created_at: new Date(),
      updated_at: new Date(),
    };

    if (paymentSnapshot.empty) {
      // Create payment document in Firestore only if it doesn't exist
      await paymentsRef.add(paymentData);
    }

    // 3. Check the payment status and pending amount for the order using Square netAmountDueMoney
    if (payment.status !== 'COMPLETED' && order?.netAmountDueMoney?.amount?.toString() !== '0') {
      return new Response(
        JSON.stringify({
          error: `Payment not completed or amount due remaining. payment status is ${payment.status} and Due amount is ${order?.netAmountDueMoney?.amount} Please retry the payment`,
        }),
        {
          status: 400,
        }
      );
    }

    // Get the order reference
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      return new Response(JSON.stringify({ error: 'Order not found' }), {
        status: 404,
      });
    }

    const orderData = orderDoc.data() as Orders;

    // Update order status to completed
    await orderRef.update({
      status: 'completed',
      updated_at: Timestamp.now(),
    });

    // Update schedule tickets_sold count
    const eventId =
      (orderData.event_id as any).id || (orderData.event_id as any).path.split('/').pop();
    const scheduleId =
      (orderData.event_schedule_id as any).id ||
      (orderData.event_schedule_id as any).path.split('/').pop();

    const scheduleRef = adminDb
      .collection(COLLECTIONS.EVENTS)
      .doc(eventId)
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(scheduleId);

    const scheduleDoc = await scheduleRef.get();
    if (scheduleDoc.exists && orderData.status === 'pending') {
      const scheduleData = scheduleDoc.data();
      const currentTicketsSold = scheduleData?.tickets_sold || 0;
      await scheduleRef.update({
        tickets_sold: currentTicketsSold + (orderData.quantity || 0),
        updated_at: Timestamp.now(),
      });
    }

    // Fetch the updated order
    const updatedOrderDoc = await orderRef.get();
    const updatedOrderData = updatedOrderDoc.data() as Orders;

    // Fetch event and schedule data for the order
    const orderEventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(eventId);
    const orderEventDoc = await orderEventRef.get();
    const orderEventData = orderEventDoc.exists
      ? { id: orderEventDoc.id, ...orderEventDoc.data() }
      : null;

    // Fetch updated schedule data
    const orderScheduleDoc = await scheduleRef.get();
    const orderScheduleData = orderScheduleDoc.exists
      ? { id: orderScheduleDoc.id, ...orderScheduleDoc.data() }
      : null;

    const orderWithDetails = {
      id: updatedOrderDoc.id,
      ...updatedOrderData,
      event: orderEventData,
      schedule: orderScheduleData,
      waiver_count: 0,
      booker: undefined,
      pending_child_waivers: undefined,
    } as Orders & {
      event: Events | null;
      schedule: EventSchedules | null;
      waiver_count: number;
      booker: Users | undefined;
      pending_child_waivers?: { first_name: string; last_name: string }[];
    };

    // Get and update associated tickets using document reference
    const ticketsSnapshot = await adminDb
      .collection(COLLECTIONS.TICKETS)
      .where('order_id', '==', orderRef)
      .get();

    const userRef = adminDb
      .collection(COLLECTIONS.USERS)
      .doc((updatedOrderData?.order_by as any).id);
    const userDoc = await userRef.get();

    if (userDoc.exists) {
      orderWithDetails.booker = userDoc.data() as Users;
      const waiverResult = await getWaiverCount(userDoc.data()?.email, orderRef);

      if (typeof waiverResult === 'number') {
        orderWithDetails.waiver_count = waiverResult;
      } else {
        orderWithDetails.waiver_count = waiverResult.waiver_count;
        orderWithDetails.pending_child_waivers = waiverResult.pendingChildWaivers;
      }
    }

    if (ticketsSnapshot.empty) {
      return new Response(
        JSON.stringify({
          error: 'No tickets found for order',
          order: orderWithDetails,
        }),
        { status: 404 }
      );
    }

    // Update each ticket individually
    const batch = adminDb.batch();
    const updatedTickets = [];

    for (const ticketDoc of ticketsSnapshot.docs) {
      try {
        // Update the ticket status
        batch.update(ticketDoc.ref, {
          status: 'reserved',
          updated_at: Timestamp.now(),
        });

        const ticketData = ticketDoc.data();

        // Fetch event data
        const eventId = (ticketData.event_id as any).id;
        const eventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(eventId);
        const eventDoc = await eventRef.get();
        const eventData = eventDoc.exists ? { id: eventDoc.id, ...eventDoc.data() } : null;

        // Fetch schedule data from event's subcollection
        const scheduleId = (ticketData.event_schedule_id as any).id;
        const scheduleRef = adminDb
          .collection(COLLECTIONS.EVENTS)
          .doc(eventId)
          .collection(COLLECTIONS.EVENT_SCHEDULES)
          .doc(scheduleId);
        const scheduleDoc = await scheduleRef.get();
        const scheduleData = scheduleDoc.exists
          ? { id: scheduleDoc.id, ...scheduleDoc.data() }
          : null;

        // Fetch user data
        const userId = (ticketData.user_id as any).id;
        const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
        const userDoc = await userRef.get();
        const userData = userDoc.exists ? (userDoc.data() as Users) : null;

        updatedTickets.push({
          id: ticketDoc.id,
          ...ticketData,
          order_id: orderRef,
          event: eventData,
          schedule: scheduleData,
          user: userData,
        });
      } catch (error) {
        throw new Error(
          `Failed to update ticket ${ticketDoc.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }

    // Commit all the updates
    await batch.commit();

    // Format the tickets to match the Ticket interface
    const formattedTickets = updatedTickets.map(ticket => ({
      ...ticket,
      // Convert order_id to the expected type
      order_id: ticket.order_id,
    })) as unknown as (Tickets & {
      event: Events | null;
      schedule: EventSchedules | null;
      user: Users | null;
    })[];

    return new Response(
      JSON.stringify({
        tickets: formattedTickets,
        order: orderWithDetails,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Error updating order status:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update order status';
    return new Response(
      JSON.stringify({
        error: errorMessage,
        details: error instanceof Error ? error.stack : undefined,
      }),
      {
        status: 500,
      }
    );
  }
};

async function getWaiverCount(email: string | undefined, orderRef: DocumentReference) {
  if (!email) return 0;

  const currentDate = getPacificTimeNow();
  currentDate.setHours(0, 0, 0, 0);

  // First check for adult waiver
  const waiverSnapshot = await adminDb
    .collection(COLLECTIONS.WAIVERS)
    .where('email', '==', email)
    .where('valid_until', '>', currentDate)
    .get();

  // Check for any pending child waivers
  const ticketsSnapshot = await adminDb
    .collection(COLLECTIONS.TICKETS)
    .where('order_id', '==', orderRef)
    .get();

  const pendingChildWaivers: { first_name: string; last_name: string }[] = [];

  for (const ticketDoc of ticketsSnapshot.docs) {
    const ticketData = ticketDoc.data();
    const userId = (ticketData.user_id as any).id;
    const ticketUserDoc = await adminDb.collection(COLLECTIONS.USERS).doc(userId).get();
    const ticketUserData = ticketUserDoc.data();

    if (ticketUserData?.age_group === 'child') {
      // Check if child waiver exists
      const childWaiverSnapshot = await adminDb
        .collection(COLLECTIONS.WAIVERS)
        .where('email', '==', email)
        .where('first_name', '==', ticketUserData.first_name)
        .where('last_name', '==', ticketUserData.last_name)
        .where('valid_until', '>', currentDate)
        .get();

      if (childWaiverSnapshot.empty) {
        pendingChildWaivers.push({
          first_name: ticketUserData.first_name,
          last_name: ticketUserData.last_name,
        });
      }
    }
  }

  if (pendingChildWaivers.length > 0) {
    return {
      waiver_count: waiverSnapshot.size,
      pendingChildWaivers,
    };
  }

  return waiverSnapshot.size;
}
