import { WhereFilterOp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS } from '@/src/lib/constants';

export async function POST(req: NextRequest) {
  try {
    const { email, uid } = await req.json();

    if (!email && !uid) {
      return NextResponse.json({ error: 'Email or UID is required' }, { status: 400 });
    }

    // Query users collection using Admin SDK
    const usersRef = adminDb.collection(COLLECTIONS.USERS);

    // Build query conditions with proper typing
    const conditions: Array<{ field: string; operator: WhereFilterOp; value: string }> = [];
    if (email) conditions.push({ field: 'email', operator: '==', value: email });
    if (uid) conditions.push({ field: 'firebase_user_id', operator: '==', value: uid });

    // Execute query with OR conditions
    let userSnapshot;
    if (conditions.length > 1) {
      // Multiple conditions - need to use OR query
      const queries = conditions.map(c => usersRef.where(c.field, c.operator, c.value).get());

      const snapshots = await Promise.all(queries);
      const docs = snapshots.flatMap(s => s.docs);

      // Remove duplicates (same doc might match multiple conditions)
      const uniqueDocs = [...new Map(docs.map(d => [d.id, d])).values()];
      userSnapshot = { empty: uniqueDocs.length === 0, docs: uniqueDocs };
    } else {
      // Single condition - simple query
      userSnapshot = await usersRef
        .where(conditions[0].field, conditions[0].operator, conditions[0].value)
        .get();
    }

    if (!userSnapshot.empty) {
      const userDoc = userSnapshot.docs[0];
      return NextResponse.json({
        success: true,
        data: {
          ...userDoc.data(),
          id: userDoc.id,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: null,
    });
  } catch (error: any) {
    console.error('Error finding user:', error);
    return NextResponse.json({ error: error.message || 'Failed to find user' }, { status: 500 });
  }
}
