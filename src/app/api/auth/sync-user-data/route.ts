import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS } from '@/src/lib/constants';
import { convertTimestampFields } from '@/src/lib/utils';

export async function POST(req: NextRequest) {
  try {
    const { uid, email, displayName, phoneNumber, photoURL, provider } = await req.json();

    // First check if user exists by email or firebase_user_id
    const usersRef = adminDb.collection(COLLECTIONS.USERS);

    // Query by email
    let querySnapshot = await usersRef.where('email', '==', email).get();

    // If not found by email, try by firebase_user_id
    if (querySnapshot.empty && uid) {
      querySnapshot = await usersRef.where('firebase_user_id', '==', uid).get();
    }

    if (!querySnapshot.empty) {
      // User exists, update their document if needed
      const existingUserDoc = querySnapshot.docs[0];
      const existingUserData = existingUserDoc.data();

      // Only update if firebase_user_id is different or doesn't exist
      if (existingUserData.firebase_user_id !== uid) {
        const updatedUserData = {
          ...existingUserData,
          firebase_user_id: uid,
          updated_at: Timestamp.now(),
          marketing_consent: true,
        };

        await usersRef.doc(existingUserDoc.id).update(updatedUserData);
      }

      // Return user data
      return NextResponse.json({
        success: true,
        data: {
          ...convertTimestampFields(existingUserData),
          id: existingUserDoc.id,
          firebase_user_id: uid,
        },
      });
    }

    // No existing user found, create new user document
    const now = Timestamp.now();
    const firstName = displayName?.split(' ')[0] || '';
    const lastName = displayName?.split(' ')[1] || '';

    const newUserData = {
      firebase_user_id: uid,
      email: email || '',
      phone: phoneNumber || '',
      role: 'customer',
      first_name: firstName,
      last_name: lastName,
      profile_url: photoURL || '',
      age_group: 'adult',
      login_provider: provider,
      created_at: now,
      updated_at: now,
      marketing_consent: true,
    };

    const newUserRef = await usersRef.add(newUserData);

    return NextResponse.json({
      success: true,
      data: {
        ...convertTimestampFields(newUserData),
        id: newUserRef.id,
      },
    });
  } catch (error: any) {
    console.error('Error syncing user data:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to sync user data' },
      { status: 500 }
    );
  }
}
