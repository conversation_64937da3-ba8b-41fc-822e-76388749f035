import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const { email, uid, provider, displayName } = await req.json();

    // Query users collection using Admin SDK
    const usersRef = adminDb.collection(COLLECTIONS.USERS);

    // First try to find by email
    let userSnapshot = await usersRef.where('email', '==', email).get();

    // If not found by email, try by firebase_user_id
    if (userSnapshot.empty && uid) {
      userSnapshot = await usersRef.where('firebase_user_id', '==', uid).get();
    }

    if (!userSnapshot.empty) {
      // User exists
      const userDoc = userSnapshot.docs[0];
      const userData = userDoc.data();

      return NextResponse.json({
        success: true,
        data: {
          ...userData,
          id: userDoc.id,
        },
      });
    }

    // Create new user
    const firstName = displayName?.split(' ')[0] || '';
    const lastName = displayName?.split(' ')[1] || '';

    const userData = {
      firebase_user_id: uid,
      email: email || '',
      phone: '',
      role: 'customer',
      first_name: firstName,
      last_name: lastName,
      profile_url: '',
      age_group: 'adult',
      login_provider: provider,
      agree_terms: null,
      opt_in_email: null,
      marketing_consent: true,
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
    };

    const newUserRef = await adminDb.collection(COLLECTIONS.USERS).add(userData);

    return NextResponse.json({
      success: true,
      data: {
        ...userData,
        id: newUserRef.id,
      },
    });
  } catch (error: any) {
    console.error('Error finding/creating user:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process user data' },
      { status: 500 }
    );
  }
}
