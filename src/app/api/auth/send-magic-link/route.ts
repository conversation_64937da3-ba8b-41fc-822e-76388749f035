import sgMail from '@sendgrid/mail';
import { format } from 'date-fns';
import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

import { adminAuth } from '../../_firebaseAdmin';

// Validate required environment variables
const requiredEnvVars = [
  'SENDGRID_API_KEY',
  'SENDGRID_FROM_EMAIL',
  'SENDGRID_MAGIC_LINK_TEMPLATE_ID',
  'NEXT_PUBLIC_BASE_URL',
] as const;

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`${envVar} is not defined`);
  }
}

if (!process.env.SENDGRID_API_KEY) {
  throw new Error('SENDGRID_API_KEY is not defined');
}

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { email, returnUrl } = await req.json();

    if (!email || typeof email !== 'string') {
      return NextResponse.json({ error: 'Valid email is required' }, { status: 400 });
    }

    // Generate Firebase sign-in link with email
    const actionCodeSettings = {
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/auth/login?email=${encodeURIComponent(
        email
      )}&nextUrl=${encodeURIComponent(returnUrl)}`,
      handleCodeInApp: true,
    };

    // Generate the sign-in link without sending email
    const signInLink = await adminAuth.generateSignInWithEmailLink(email, actionCodeSettings);

    const timestamp = format(new Date(), 'yyyy MMMM dd HH:mm');

    const templateData = {
      signin_url: signInLink,
      email: email,
      timestamp: timestamp,
    };

    const msg = {
      to: email,
      from: {
        email: process.env.SENDGRID_FROM_EMAIL as string,
        name: process.env.SENDGRID_FROM_NAME as string,
      },
      templateId: process.env.SENDGRID_MAGIC_LINK_TEMPLATE_ID as string,
      dynamicTemplateData: templateData,
    } as const;

    try {
      const [_response] = await sgMail.send(msg);
    } catch (sendGridError: any) {
      console.error('SendGrid Detailed Error:', {
        message: sendGridError.message,
        response: sendGridError.response?.body,
        code: sendGridError.code,
      });
      throw sendGridError;
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('SendGrid Error:', error);

    // Provide more specific error messages based on the error type
    const errorMessage =
      error.response?.body?.errors?.[0]?.message || 'Failed to send magic link email';

    return NextResponse.json({ error: errorMessage }, { status: error.code || 500 });
  }
}
