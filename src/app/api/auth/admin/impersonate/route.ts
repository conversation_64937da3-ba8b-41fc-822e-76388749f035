import { NextRequest, NextResponse } from 'next/server';

import { adminDb, adminAuth } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';

/**
 * Admin impersonation endpoint
 *
 * This API route allows administrators to impersonate other users by generating
 * a custom authentication token. The token can be used to sign in as the specified user.
 *
 * @route POST /api/auth/admin/impersonate
 * @param {Object} req - The request object containing the user email to impersonate
 * @param {string} req.body.email - Email address of the user to impersonate
 * @returns {Object} JSON response with success status and authentication token
 * @throws Will log errors if user lookup or token creation fails
 *
 * @security This endpoint should be protected with proper admin authentication middleware
 */
export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Middleware to verify logged in user token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const uid = authResult.decodedToken.uid;

    // Get the user document from Firestore using Admin SDK
    const usersRef = adminDb.collection(COLLECTIONS.USERS);
    const userQuery = usersRef.where('firebase_user_id', '==', uid);
    const userSnapshot = await userQuery.get();

    if (userSnapshot.empty) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const userData = userSnapshot.docs[0].data();

    // Check if user has admin role
    if (userData.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized. Admin access required' }, { status: 403 });
    }

    // Extract email from request body
    const { email } = await req.json();
    // Get user details from Firebase Admin SDK using email
    const user = await adminAuth.getUserByEmail(email);
    // Create a custom authentication token for the user
    const token = await adminAuth.createCustomToken(user.uid);
    // Return the token in the response
    return NextResponse.json({ success: true, token });
  } catch (error) {
    console.error('Impersonation Error:', error);
    return NextResponse.json({ error: 'Failed to impersonate user' }, { status: 500 });
  }
}
