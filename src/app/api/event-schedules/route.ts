//Check back
import { format } from 'date-fns';
import { Timestamp as ClientTimestamp } from 'firebase/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';

/**
 * Helper function to convert Admin Firestore Timestamp to client-compatible Timestamp
 * @param adminTimestamp The Admin Firestore timestamp to convert
 * @returns A client-compatible Timestamp object
 */
function convertTimestamp(adminTimestamp: any): ClientTimestamp | null {
  if (!adminTimestamp) return null;
  if (adminTimestamp.seconds !== undefined && adminTimestamp.nanoseconds !== undefined) {
    return new ClientTimestamp(adminTimestamp.seconds, adminTimestamp.nanoseconds);
  }
  return null;
}

/**
 * Converts all Timestamp fields in an object to client-compatible Timestamps
 * @param data The object containing potential Timestamp fields
 * @returns A new object with converted Timestamp fields
 */
function convertTimestampFields(data: any): any {
  if (!data) return data;

  const result = { ...data };
  for (const key in result) {
    if (result[key] && typeof result[key] === 'object') {
      if (result[key].seconds !== undefined && result[key].nanoseconds !== undefined) {
        result[key] = convertTimestamp(result[key]);
      } else if (Array.isArray(result[key])) {
        result[key] = result[key].map((item: any) =>
          typeof item === 'object' ? convertTimestampFields(item) : item
        );
      } else {
        result[key] = convertTimestampFields(result[key]);
      }
    }
  }
  return result;
}

/**
 * GET /api/event-schedules
 *
 * Retrieves active events and their associated schedules from Firestore.
 * Filters out past events and deleted records, returning only current and future events.
 *
 * @param _request - Next.js Request object (unused but required for Next.js API routes)
 *
 * @returns NextResponse
 * - Success: { message: string, data: Events[] }
 * - Error: { error: string } with 500 status code
 *
 * @filters
 * - Events: end_date >= today AND deleted_at is null
 * - Schedules: event_date >= today AND deleted_at is null
 *
 * @example Response data structure:
 * {
 *   message: "Event schedules retrieved successfully",
 *   data: [
 *     {
 *       id: "event1",
 *       title: "Sample Event",
 *       event_location: { ... },
 *       event_schedules: [
 *         {
 *           id: "schedule1",
 *           title: "Morning Session",
 *           start_time: "09:00",
 *           end_time: "12:00",
 *           event_date: "2024-03-20"
 *         }
 *       ]
 *     }
 *   ]
 * }
 */
export async function GET(_request: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(_request);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const today = format(getPacificTimeNow(), 'yyyy-MM-dd');

    // Query active events (not ended and not deleted) using Admin SDK
    const eventsQuery = adminDb
      .collection(COLLECTIONS.EVENTS)
      .where('end_date', '>=', today)
      .where('deleted_at', '==', null);

    // Query all schedules for future dates across all events using Admin SDK
    const schedulesQuery = adminDb
      .collectionGroup(COLLECTIONS.EVENT_SCHEDULES)
      .where('event_date', '>=', today)
      .where('deleted_at', '==', null);

    // TODO Need to add pagination also sorting and date filter, some filter like close, open events
    // Execute both queries in parallel
    const [eventsSnapshot, schedulesSnapshot] = await Promise.all([
      eventsQuery.get(),
      schedulesQuery.get(),
    ]);

    if (eventsSnapshot.empty) {
      return NextResponse.json({
        message: SUCCESS_MESSAGES.GET_EVENT_SCHEDULES,
        data: [],
      });
    }

    // Create a map of events for faster lookup and initial processing
    const eventsMap = new Map(
      eventsSnapshot.docs.map(doc => {
        const eventData = convertTimestampFields(doc.data()) as Events;
        eventData.id = doc.id;
        eventData.event_schedules = [];
        return [doc.id, eventData];
      })
    );

    // Process all schedules and attach them to their respective events
    schedulesSnapshot.docs.forEach(scheduleDoc => {
      const eventId = scheduleDoc.ref.parent.parent?.id;
      if (eventId && eventsMap.has(eventId)) {
        const schedule = {
          id: scheduleDoc.id,
          ...convertTimestampFields(scheduleDoc.data()),
        } as EventSchedules;
        eventsMap.get(eventId)?.event_schedules?.push(schedule);
      }
    });

    // Convert map to array, filtering out events with no schedules
    const events: Events[] = Array.from(eventsMap.values()).filter(
      event => Array.isArray(event?.event_schedules) && event.event_schedules.length > 0
    );

    // TODO Returns the required fields only from events and schedules. Do not send business fields to frontend
    // Return success response with filtered events and schedules
    return NextResponse.json({
      message: SUCCESS_MESSAGES.GET_EVENT_SCHEDULES,
      data: events,
    });
  } catch (error) {
    console.error('Error fetching event schedules:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_FETCH_EVENTS }, { status: 500 });
  }
}
