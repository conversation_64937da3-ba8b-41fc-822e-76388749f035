//Check back
import { Timestamp as ClientTimestamp } from 'firebase/firestore';
import { Timestamp as AdminTimestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { AGE_GROUPS, COLLECTIONS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { waiverSchema } from '@/src/schema/waiver.zod';

// Helper function to convert Admin Timestamp to Client Timestamp
function convertTimestamp(adminTimestamp: AdminTimestamp): ClientTimestamp {
  return new ClientTimestamp(adminTimestamp.seconds, adminTimestamp.nanoseconds);
}

/**
 * POST handler for waiver creation
 *
 * @param req - The incoming request object containing waiver data
 * @returns NextResponse with success/error message and status code
 */
export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Parse and validate the request data
    const waiver = await req.json();
    const validationResult = waiverSchema.safeParse(waiver);

    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors[0]?.message || 'Invalid waiver data';
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    // Use the validated data
    const validatedWaiver = validationResult.data;

    // Validate required fields
    if (!validatedWaiver) {
      return NextResponse.json({ error: ERROR_MESSAGES.MISSING_REQUIRED_FIELDS }, { status: 400 });
    }

    // Array to store all created waivers
    const createdWaivers = [];

    // Create adult waiver first
    const currentDate = getPacificTimeNow();
    currentDate.setHours(0, 0, 0, 0);

    const waiversRef = adminDb.collection(COLLECTIONS.WAIVERS);
    const existingWaivers = await waiversRef
      .where('email', '==', validatedWaiver.email)
      .where('valid_until', '>', currentDate)
      .where('first_name', '==', validatedWaiver.first_name)
      .where('last_name', '==', validatedWaiver.last_name)
      .where('waiver_version', '==', validatedWaiver.waiver_version)
      .get();

    // Only create adult waiver if it doesn't exist
    if (existingWaivers.empty) {
      // Create the adult waiver object with metadata
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { children, ...waiverWithoutChildren } = validatedWaiver;

      // Create admin timestamp
      const now = AdminTimestamp.now();
      // Convert to client timestamp for the response
      const clientNow = convertTimestamp(now);

      const adultWaiverData = {
        ...waiverWithoutChildren,
        ip_address: validatedWaiver.ip_address,
        device_info: validatedWaiver.device_info,
        mailingList: validatedWaiver.mailingList ?? false,
        signed_at: now,
        waiver_version: validatedWaiver.waiver_version,
        age_group: validatedWaiver.age_group ?? AGE_GROUPS.ADULT,
        valid_until: new Date(getPacificTimeNow().getFullYear(), 11, 31, 23, 59, 59),
        created_at: now,
        deleted_at: null,
        booker_email: validatedWaiver.email,
        waiver_accepted: validatedWaiver.agreementAccepted,
      };

      // const sanitizedEmail = validatedWaiver.email.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();

      // Upload signature to Storage using Admin SDK
      // const adminStorage = getStorage();
      // const signaturePath = `waivers/signatures/${sanitizedEmail}_${Date.now()}.png`;
      // const bucket = adminStorage.bucket(process.env.FIREBASE_PRIVATE_BUCKET);
      // const file = bucket.file(signaturePath);

      // Convert data URL to buffer
      // const base64Data = validatedWaiver.signature.replace(/^data:image\/\w+;base64,/, '');
      // const buffer = Buffer.from(base64Data, 'base64');

      // await file.save(buffer, {
      //   metadata: {
      //     contentType: 'image/png',
      //     metadata: {
      //       uploadedBy: validatedWaiver.email,
      //       uploadedAt: new Date().toISOString(),
      //     },
      //   },
      // });

      // Store the path instead of the actual signature
      // adultWaiverData.signature_path = signaturePath;
      // delete adultWaiverData.signature;

      // Save adult waiver to Firestore
      const adultDocRef = await waiversRef.add(adultWaiverData);

      // For the response, convert timestamps to client format
      createdWaivers.push({
        id: adultDocRef.id,
        ...waiverWithoutChildren,
        ip_address: waiver.ip_address,
        device_info: waiver.device_info,
        mailingList: waiver.mailingList ?? false,
        signed_at: clientNow,
        valid_until: new Date(getPacificTimeNow().getFullYear(), 11, 31, 23, 59, 59),
        waiver_version: waiver.waiver_version,
        age_group: waiver.age_group ?? AGE_GROUPS.ADULT,
        created_at: clientNow,
        deleted_at: null,
        booker_email: waiver.email,
        waiver_accepted: waiver.agreementAccepted,
      });
    }

    // Handle children waivers if they exist
    if (validatedWaiver.children && validatedWaiver.children.length > 0) {
      for (const child of validatedWaiver.children) {
        // Check for existing child waiver
        const existingChildWaivers = await waiversRef
          .where('email', '==', child.email || validatedWaiver.email)
          .where('first_name', '==', child.first_name)
          .where('last_name', '==', child.last_name)
          .where('valid_until', '>', currentDate)
          .where('waiver_version', '==', validatedWaiver.waiver_version)
          .get();

        if (existingChildWaivers.empty) {
          // Create admin timestamp
          const now = AdminTimestamp.now();

          // Create child waiver data
          const childWaiverData = {
            age_group: AGE_GROUPS.CHILD,
            first_name: child.first_name,
            last_name: child.last_name,
            email: child.email || validatedWaiver.email, // Use parent's email
            ip_address: validatedWaiver.ip_address,
            device_info: validatedWaiver.device_info,
            signed_at: now,
            waiver_version: validatedWaiver.waiver_version,
            valid_until: new Date(getPacificTimeNow().getFullYear(), 11, 31, 23, 59, 59),
            // signature_path:
            //   createdWaivers[0]?.signature_path ?? existingWaivers.docs[0]?.data().signature_path, // Use parent's signature
            mailingList: validatedWaiver.mailingList ?? false,
            created_at: now,
            deleted_at: null,
            booker_email: validatedWaiver.email,
            waiver_accepted: validatedWaiver.agreementAccepted,
          };

          // Save child waiver to Firestore
          const childDocRef = await waiversRef.add(childWaiverData);

          createdWaivers.push({ id: childDocRef.id, ...childWaiverData });
        }
      }
    }

    return NextResponse.json(
      {
        message: SUCCESS_MESSAGES.WAIVER_SUBMITTED,
        waivers: createdWaivers,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating waiver:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_SUBMIT_WAIVER }, { status: 500 });
  }
}
