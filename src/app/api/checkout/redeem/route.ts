import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { checkInTicketTailorTicket } from '@/src/app/api/ticket-tailor/_ticketTailerService';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';

export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Verify authentication
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { ticketIds } = await req.json();

    if (!ticketIds || !Array.isArray(ticketIds) || ticketIds.length === 0) {
      return NextResponse.json({ error: ERROR_MESSAGES.NO_CLAMSHELLS_SELECTED }, { status: 400 });
    }

    // Get the Firebase Auth ID from the auth token
    const firebaseAuthId = authResult.decodedToken.uid;

    // Find the user document that matches this Firebase Auth ID
    const usersRef = adminDb.collection(COLLECTIONS.USERS);
    const userSnapshot = await usersRef.where('firebase_user_id', '==', firebaseAuthId).get();

    if (userSnapshot.empty) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get the first matching user document
    const userDoc = userSnapshot.docs[0];
    const userId = userDoc.id;

    // Verify all tickets belong to the authenticated user
    const ticketsRef = adminDb.collection(COLLECTIONS.TICKETS);
    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);

    // Query to get all tickets that belong to the user
    const userTicketsSnapshot = await ticketsRef.where('user_id', '==', userRef).get();
    const userTicketIds = userTicketsSnapshot.docs.map(doc => doc.id);

    // Check if all requested ticket IDs belong to the user
    const unauthorizedTickets = ticketIds.filter(id => !userTicketIds.includes(id));

    if (unauthorizedTickets.length > 0) {
      return NextResponse.json(
        { error: 'You are not authorized to redeem these clamshells' },
        { status: 403 }
      );
    }

    // Check if any of the tickets are already checked in
    const alreadyCheckedInTickets = [];
    for (const ticketId of ticketIds) {
      const ticketDoc = await ticketsRef.doc(ticketId).get();

      if (ticketDoc.exists && ticketDoc.data()?.check_in === true) {
        alreadyCheckedInTickets.push(ticketId);
      }
    }

    if (alreadyCheckedInTickets.length > 0) {
      return NextResponse.json(
        {
          error:
            alreadyCheckedInTickets.length === 1
              ? 'This clamshell has already been redeemed'
              : `${alreadyCheckedInTickets.length} clamshells have already been redeemed`,
        },
        { status: 400 }
      );
    }

    // Use batch for multiple Firestore updates
    if (ticketIds.length > 1) {
      const batch = adminDb.batch();

      ticketIds.forEach(id => {
        const ticketRef = ticketsRef.doc(id);
        batch.update(ticketRef, {
          check_in: true,
          checked_in_at: Timestamp.now(),
        });
      });

      await batch.commit();
    } else {
      // Single update
      const ticketRef = ticketsRef.doc(ticketIds[0]);
      await ticketRef.update({
        check_in: true,
        checked_in_at: Timestamp.now(),
      });
    }

    // Get ticket_id values for each ticket to check in with Ticket Tailor API
    const ticketTailorPromises = await Promise.all(
      ticketIds.map(async id => {
        try {
          const ticketDoc = await ticketsRef.doc(id).get();

          if (ticketDoc.exists) {
            const ticketData = ticketDoc.data();
            const ticketTailorId = ticketData?.ticket_id;

            if (ticketTailorId) {
              return checkInTicketTailorTicket(ticketTailorId).catch(error => {
                console.error(
                  `Failed to check in ticket ${ticketTailorId} with Ticket Tailor:`,
                  error
                );
                return null; // Prevent Promise.all from failing
              });
            }
          }
          return null;
        } catch (error) {
          console.error(`Error processing ticket ${id}:`, error);
          return null; // Prevent Promise.all from failing
        }
      })
    );

    // Execute all Ticket Tailor API calls in parallel (don't block response)
    Promise.all(ticketTailorPromises).catch(error => {
      console.error('Error in batch Ticket Tailor check-in operations:', error);
    });

    return NextResponse.json({
      success: true,
      ticketIds,
      message: 'Clamshells redeemed successfully',
    });
  } catch (error) {
    console.error('Error redeeming clamshells:', error);
    return NextResponse.json(
      {
        error: ERROR_MESSAGES.FAILED_TO_REDEEM_CLAMSHELL,
      },
      { status: 500 }
    );
  }
}
