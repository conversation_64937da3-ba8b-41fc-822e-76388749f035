import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';
import { Waivers } from '@/src/types/Waivers';

/**
 * Handles GET requests to retrieve all waivers signed by a user
 * including both their personal waivers and waivers for their children
 *
 * @param req - The incoming HTTP request with email as query parameter
 * @returns NextResponse with all waivers data or error message
 *
 * Success Response (200):
 * - userWaivers: Array of user's personal waivers
 * - childWaivers: Array of waivers for children associated with user's email
 *
 * Error Responses:
 * - 400: Bad Request - No email provided
 * - 500: Server error during processing
 */
export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Verify authentication token
    const authResult = await verifyAuthToken(req);

    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { searchParams } = new URL(req.url);
    const email = searchParams.get('email');

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Verify email matches authenticated user's email
    if (authResult.decodedToken.email !== email) {
      return NextResponse.json({ error: 'Unauthorized access' }, { status: 401 });
    }

    const currentDate = getPacificTimeNow();
    currentDate.setHours(0, 0, 0, 0);

    // Query waivers collection for all waivers with user's email
    const waiversRef = adminDb.collection(COLLECTIONS.WAIVERS);
    const waiverQuery = waiversRef
      .where('email', '==', email)
      .where('valid_until', '>', currentDate);

    const waiverSnapshot = await waiverQuery.get();

    if (waiverSnapshot.empty) {
      return NextResponse.json({
        userWaivers: [],
        childWaivers: [],
        message: 'No waivers found for this user',
        status: 'success',
      });
    }

    // Transform waiver data to include document ID
    const waivers = waiverSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Waivers[];

    // Separate adult and child waivers
    const userWaivers = waivers.filter(waiver => waiver.age_group === 'adult');
    const childWaivers = waivers.filter(waiver => waiver.age_group === 'child');

    return NextResponse.json({
      userWaivers,
      childWaivers,
      message: 'Waivers retrieved successfully',
      status: 'success',
    });
  } catch (error) {
    console.error('Error fetching user waivers:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.GENERIC_ERROR }, { status: 500 });
  }
}
