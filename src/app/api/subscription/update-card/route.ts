import crypto from 'crypto';

import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';
import { updateCardSchema } from '@/src/schema/subscription.zod';

import { client, JSONBigInt } from '../../square/_squareConfig';

/**
 * Updates a subscription's payment method in Square
 *
 * @route POST /api/subscription/update-card
 * @param req The incoming request with subscriptionId and sourceId in the request body
 * @returns Result of the update operation
 */
export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const requestData = await req.json();

    // Validate request data
    const validationResult = updateCardSchema.safeParse(requestData);

    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors[0]?.message || 'Invalid request data';
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    // Use validated data
    const { subscriptionId, sourceId } = validationResult.data;

    // Get the subscription to retrieve the customer ID
    const subscriptionResponse = await client.subscriptionsApi.retrieveSubscription(subscriptionId);

    if (!subscriptionResponse.result.subscription?.customerId) {
      return NextResponse.json(
        { error: 'Customer ID not found for this subscription' },
        { status: 404 }
      );
    }

    const customerId = subscriptionResponse.result.subscription.customerId;

    // Verify the subscription belongs to the authenticated user
    // Query Firestore to check if this subscription belongs to the user
    const subscriptionsRef = adminDb.collection(COLLECTIONS.SUBSCRIPTIONS);
    const subscriptionQuery = subscriptionsRef
      .where('square_subscription_id', '==', subscriptionId)
      .where('email', '==', authResult.decodedToken.email);

    const subscriptionSnapshot = await subscriptionQuery.get();

    if (subscriptionSnapshot.empty) {
      return NextResponse.json(
        { error: 'You are not authorized to update this subscription' },
        { status: 403 }
      );
    }

    // Create a new card for the customer
    const cardResponse = await client.cardsApi.createCard({
      idempotencyKey: crypto.randomUUID(),
      sourceId: sourceId,
      card: {
        customerId: customerId,
      },
    });

    if (!cardResponse.result || !cardResponse.result.card) {
      return NextResponse.json({ error: 'Failed to create new card' }, { status: 500 });
    }

    const cardId = cardResponse.result.card.id;

    // Update the subscription with the new card ID
    const updateResponse = await client.subscriptionsApi.updateSubscription(subscriptionId, {
      subscription: {
        cardId: cardId,
      },
    });

    // Update Firestore with the new subscription response
    try {
      const subscriptionDoc = subscriptionSnapshot.docs[0];
      await subscriptionDoc.ref.update({
        square_subscription_response: JSON.parse(JSONBigInt.stringify(updateResponse.result)),
        updated_at: Timestamp.now(),
      });
    } catch (firestoreError) {
      console.error('Error updating Firestore:', firestoreError);
      // Continue with the response even if Firestore update fails
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Payment method updated successfully',
        cardDetails: {
          cardBrand: cardResponse.result.card.cardBrand,
          last4: cardResponse.result.card.last4,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating subscription card:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Failed to update payment method',
      },
      { status: 500 }
    );
  }
}
