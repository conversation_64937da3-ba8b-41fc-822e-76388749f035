import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { isSubscriptionCancellationAllowed } from '@/src/lib/utils';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';

import { client, JSONBigInt } from '../../square/_squareConfig';

/**
 * Cancels a user's subscription in Square
 *
 * @route POST /api/subscription/cancel
 * @param req The incoming request with subscriptionId in the request body
 * @returns Result of the cancellation operation
 */
export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { subscriptionId, firebase_user_id } = await req.json();

    // Validate required fields
    if (!subscriptionId || !firebase_user_id) {
      return NextResponse.json({ error: 'Missing the required parameters' }, { status: 400 });
    }

    // Verify the authenticated user is the same as the user whose subscription is being cancelled
    if (authResult.decodedToken.uid !== firebase_user_id) {
      return NextResponse.json({ error: 'Unauthorized access' }, { status: 401 });
    }

    // Check if cancellation is allowed based on the day of the week
    if (!isSubscriptionCancellationAllowed()) {
      return NextResponse.json(
        {
          error: ERROR_MESSAGES.SUBSCRIPTION_CANCELLATION_NOT_ALLOWED,
        },
        { status: 403 }
      );
    }

    // Clear any existing cancellation date
    await client.subscriptionsApi.updateSubscription(subscriptionId, {
      subscription: {
        canceledDate: null,
      },
    });

    // Call Square API to cancel the subscription
    const response = await client.subscriptionsApi.cancelSubscription(subscriptionId);

    if (!response.result || !response.result.subscription) {
      throw new Error('Failed to cancel subscription');
    }

    // After canceling the subscription but before updating Firestore
    // Find the subscription document to get subscription response
    const subscriptionsRef = adminDb.collection(COLLECTIONS.SUBSCRIPTIONS);
    const querySnapshot = await subscriptionsRef
      .where('square_subscription_id', '==', subscriptionId)
      .get();

    if (!querySnapshot.empty) {
      // Get order template IDs from the subscription response
      const squareResponse = response.result;
      const locationId = squareResponse?.subscription?.locationId || '';
      const phases = squareResponse?.subscription?.phases || [];

      // Collect all unique order template IDs from phases
      const orderTemplateIds = new Set<string>();
      phases.forEach(phase => {
        if (phase.orderTemplateId) {
          orderTemplateIds.add(phase.orderTemplateId);
        }
      });

      // Cancel each order template
      for (const templateId of orderTemplateIds) {
        try {
          // First retrieve the order to get its version and fulfillments
          const orderResponse = await client.ordersApi.retrieveOrder(templateId);
          const order = orderResponse.result.order;
          const orderVersion = order?.version;
          const fulfillments = order?.fulfillments || [];
          const orderState = order?.state;

          if (orderVersion !== undefined) {
            if (orderState === 'DRAFT') {
              // For DRAFT orders, we need to handle fulfillments differently
              // First, update the order state to OPEN
              await client.ordersApi.updateOrder(templateId, {
                order: {
                  state: 'OPEN',
                  version: orderVersion,
                  locationId: locationId,
                },
                idempotencyKey: crypto.randomUUID(),
              });

              // Retrieve the order again to get the updated version
              const updatedOrderResponse = await client.ordersApi.retrieveOrder(templateId);
              const updatedOrder = updatedOrderResponse.result.order;
              const updatedVersion = updatedOrder?.version;

              if (updatedVersion !== undefined) {
                // Now update fulfillments to CANCELED
                const updatedFulfillments = fulfillments.map(fulfillment => ({
                  uid: fulfillment.uid,
                  state: 'CANCELED',
                }));

                await client.ordersApi.updateOrder(templateId, {
                  order: {
                    fulfillments: updatedFulfillments,
                    version: updatedVersion,
                    locationId: locationId,
                  },
                  idempotencyKey: crypto.randomUUID(),
                });

                // Get the final version and cancel the order
                const finalOrderResponse = await client.ordersApi.retrieveOrder(templateId);
                const finalVersion = finalOrderResponse.result.order?.version;

                if (finalVersion !== undefined) {
                  await client.ordersApi.updateOrder(templateId, {
                    order: {
                      state: 'CANCELED',
                      version: finalVersion,
                      locationId: locationId,
                    },
                    idempotencyKey: crypto.randomUUID(),
                  });
                  console.log(`Draft order template ${templateId} canceled successfully`);
                }
              }
            } else {
              // For non-DRAFT orders, proceed as before
              // Check if we need to update fulfillments first
              const needsFulfillmentUpdate = fulfillments.some(
                fulfillment =>
                  !['COMPLETED', 'CANCELED', 'FAILED'].includes(fulfillment.state as string)
              );

              if (needsFulfillmentUpdate) {
                // Update all fulfillments to CANCELED state first
                const updatedFulfillments = fulfillments.map(fulfillment => ({
                  uid: fulfillment.uid,
                  state: 'CANCELED',
                }));

                await client.ordersApi.updateOrder(templateId, {
                  order: {
                    fulfillments: updatedFulfillments,
                    version: orderVersion,
                    locationId: locationId,
                  },
                  idempotencyKey: crypto.randomUUID(),
                });

                // Retrieve the order again to get the updated version
                const updatedOrderResponse = await client.ordersApi.retrieveOrder(templateId);
                const updatedVersion = updatedOrderResponse.result.order?.version;

                if (updatedVersion !== undefined) {
                  // Now cancel the order with the updated version
                  await client.ordersApi.updateOrder(templateId, {
                    order: {
                      state: 'CANCELED',
                      version: updatedVersion,
                      locationId: locationId,
                    },
                    idempotencyKey: crypto.randomUUID(),
                  });
                  console.log(
                    `Order template ${templateId} canceled successfully after updating fulfillments`
                  );
                }
              } else {
                // No fulfillment update needed, directly cancel the order
                await client.ordersApi.updateOrder(templateId, {
                  order: {
                    state: 'CANCELED',
                    version: orderVersion,
                    locationId: locationId,
                  },
                  idempotencyKey: crypto.randomUUID(),
                });
                console.log(`Order template ${templateId} canceled successfully`);
              }
            }
          } else {
            console.warn(`Could not retrieve version for order template ${templateId}`);
          }
        } catch (orderError) {
          console.error(`Error canceling order template ${templateId}:`, orderError);
        }
      }

      // Update Firestore with the new subscription response
      const subscriptionDoc = querySnapshot.docs[0];
      await subscriptionDoc.ref.update({
        square_subscription_response: JSON.parse(JSONBigInt.stringify(response.result)),
        updated_at: Timestamp.now(),
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Subscription cancelled successfully',
      subscription: {
        id: response.result.subscription.id,
        status: response.result.subscription.status,
        canceledDate: response.result.subscription.canceledDate,
        chargedThroughDate: response.result.subscription.chargedThroughDate,
      },
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    return NextResponse.json(
      {
        error: 'Failed to cancel subscription',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
