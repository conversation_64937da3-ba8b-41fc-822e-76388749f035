import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

/**
 * Fetches delivery zone details by ID
 *
 * @route GET /api/subscription/delivery-zone?zoneId={zoneId}
 * @param {Request} request - Contains zoneId in the query parameters
 * @returns {Promise<NextResponse>} JSON response with delivery zone details
 */
export async function GET(request: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(request);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { searchParams } = new URL(request.url);
    const zoneId = searchParams.get('zoneId');

    if (!zoneId) {
      return NextResponse.json({ error: 'Delivery zone ID is required' }, { status: 400 });
    }

    // Fetch the delivery zone document using Admin SDK
    const zoneRef = adminDb.collection(COLLECTIONS.DELIVERY_ZONES).doc(zoneId);
    const zoneSnap = await zoneRef.get();

    if (!zoneSnap.exists) {
      return NextResponse.json({ error: 'Delivery zone not found' }, { status: 404 });
    }

    // Get zone data and add the ID
    const zoneData = zoneSnap.data();

    return NextResponse.json({
      id: zoneId,
      name: zoneData?.zone_name || '',
      deliveryDays: zoneData?.delivery_days || [],
      zipCodes: zoneData?.zip_codes || [],
      isActive: zoneData?.is_active || false,
      startDate: zoneData?.start_date || null,
      endDate: zoneData?.end_date || null,
    });
  } catch (error) {
    console.error('Error fetching delivery zone:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.GENERIC_ERROR }, { status: 500 });
  }
}
