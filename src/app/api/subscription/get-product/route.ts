import { NextRequest, NextResponse } from 'next/server';

import { API_ENDPOINTS, ERROR_MESSAGES, SUBSCRIPTION_DEFAULTS } from '@/src/lib/constants';
import { extractCustomAttributes } from '@/src/lib/utils';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

import { client } from '../../square/_squareConfig';

/**
 * Fetches product details from Square API
 *
 * @param req The incoming request
 * @returns Product details including name, description, and price
 */
export async function GET(_req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(_req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const productId = process.env.NEXT_SQUARE_SUBSCRIPTION_PRODUCT_ID;

    if (!productId) {
      return NextResponse.json({ error: 'Product not configured' }, { status: 500 });
    }

    // Fetch the catalog item from Square
    const response = await client.catalogApi.retrieveCatalogObject(productId, true);

    if (!response.result || !response.result.object) {
      throw new Error('Product not found');
    }

    const catalogObject = response.result.object;
    const itemData = catalogObject.itemData;

    if (!itemData) {
      throw new Error('Invalid product data');
    }

    // Get the first variation (assuming it's the default one)
    const variations = itemData.variations || [];
    const defaultVariation = variations.length > 0 ? variations[0] : null;

    let deliveryCharge;
    try {
      deliveryCharge = await getDeliveryFee();
    } catch (deliveryFeeError) {
      console.error('Failed to get delivery fee:', deliveryFeeError);
      return NextResponse.json({ error: 'Failed to get delivery fee' }, { status: 500 });
    }

    // Format variations with direct image property
    const formattedVariations = variations
      .filter(
        variation =>
          variation.itemVariationData?.sellable !== false &&
          variation.itemVariationData?.sku !== '9'
      ) // Only include sellable variations and skip SKU 9
      .map(variation => {
        // Extract basic variation data
        const variationData = {
          id: variation.id,
          name: variation.itemVariationData?.name || '',
          price: variation.itemVariationData?.priceMoney?.amount
            ? Number(variation.itemVariationData.priceMoney.amount) / 100
            : 0,
          // Add image directly to variation object
          image:
            variation.itemVariationData?.imageIds && variation.itemVariationData.imageIds.length > 0
              ? `${API_ENDPOINTS.GET_PRODUCT_IMAGE}?imageId=${variation.itemVariationData.imageIds[0]}`
              : itemData.imageIds && itemData.imageIds.length > 0
                ? `${API_ENDPOINTS.GET_PRODUCT_IMAGE}?imageId=${itemData.imageIds[0]}`
                : SUBSCRIPTION_DEFAULTS.PRODUCT_FALLBACK_IMAGE_URL,
        };

        return variationData;
      });

    const customAttributes = extractCustomAttributes(catalogObject.customAttributeValues || {});

    // Create formatted product data
    const productData = {
      id: catalogObject.id,
      name: itemData.name,
      description: itemData.description,
      price: defaultVariation?.itemVariationData?.priceMoney?.amount
        ? Number(defaultVariation.itemVariationData.priceMoney.amount) / 100
        : 0,
      image:
        itemData.imageIds && itemData.imageIds.length > 0
          ? `${API_ENDPOINTS.GET_PRODUCT_IMAGE}?imageId=${itemData.imageIds[0]}`
          : SUBSCRIPTION_DEFAULTS.PRODUCT_FALLBACK_IMAGE_URL,
      deliveryCharge: deliveryCharge, // Use the delivery charge from the separate catalog item
      variations: formattedVariations,
      customAttributes,
    };

    return NextResponse.json(productData);
  } catch (error) {
    console.error('Error fetching product from Square:', error);
    return NextResponse.json({ error: 'Failed to fetch product details' }, { status: 500 });
  }
}

/**
 * Fetches the delivery fee from Square catalog
 *
 * @returns The delivery fee amount in dollars
 * @throws Error if delivery fee cannot be retrieved
 */
async function getDeliveryFee(): Promise<number> {
  try {
    const deliveryFeeId = process.env.NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_PRODUCT_ID;

    if (!deliveryFeeId) {
      throw new Error('Delivery fee product ID not configured');
    }

    // Fetch the delivery fee catalog item from Square
    const response = await client.catalogApi.retrieveCatalogObject(deliveryFeeId, true);

    if (!response.result || !response.result.object) {
      throw new Error('Delivery fee product not found');
    }

    const catalogObject = response.result.object;
    const itemData = catalogObject.itemData;

    if (!itemData) {
      throw new Error('Invalid delivery fee product data');
    }

    // Get the first variation (assuming it's the default one)
    const variations = itemData.variations || [];
    const defaultVariation = variations.length > 0 ? variations[0] : null;

    // Get price from the variation
    if (defaultVariation && defaultVariation.itemVariationData?.priceMoney?.amount) {
      return Number(defaultVariation.itemVariationData.priceMoney.amount) / 100; // Convert cents to dollars
    }

    throw new Error('No price found for delivery fee');
  } catch (error) {
    console.error('Error fetching delivery fee from Square:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
}
