import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function GET(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Extract subscriptionId from URL query parameters
    const { searchParams } = new URL(req.url);
    const subscriptionId = searchParams.get('subscriptionId');

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Get subscription from Firestore using Admin SDK
    const subscriptionRef = adminDb.collection(COLLECTIONS.SUBSCRIPTIONS).doc(subscriptionId);
    const subscriptionDoc = await subscriptionRef.get();

    if (!subscriptionDoc.exists) {
      return NextResponse.json({ error: 'Subscription not found' }, { status: 404 });
    }

    const subscriptionData = subscriptionDoc.data();

    // Helper function to safely convert Firestore timestamps
    const formatTimestamp = (timestamp: any) => {
      if (timestamp && typeof timestamp.toDate === 'function') {
        return timestamp.toDate().toISOString();
      } else if (timestamp instanceof Date) {
        return timestamp.toISOString();
      } else if (timestamp) {
        // If it's already a string or another format
        return timestamp;
      }
      return null;
    };

    return NextResponse.json({
      id: subscriptionDoc.id,
      ...subscriptionData,
      // Safely convert timestamps
      created_at: formatTimestamp(subscriptionData?.created_at),
      updated_at: formatTimestamp(subscriptionData?.updated_at),
      start_date: formatTimestamp(subscriptionData?.start_date),
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json({ error: 'Failed to fetch subscription details' }, { status: 500 });
  }
}
