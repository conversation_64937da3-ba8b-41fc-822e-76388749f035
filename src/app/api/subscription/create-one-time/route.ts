import crypto from 'crypto';

import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';
import { Payment as SquarePayment, Order as SquareOrder } from 'square';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import {
  convertToISOWithZeroTime,
  isGiftHomeDelivery,
  isOneTimeHomeDelivery,
  getSubscriptionDetailsFromOrder,
} from '@/src/lib/utils';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { subscriptionRequestSchema as reqSchema } from '@/src/schema/subscription.zod';
import { ItemDiscountValidationResult } from '@/src/types/ItemDiscount';

import {
  fetchDiscountDetails,
  validateDiscount as validateDiscountService,
} from '../../_squareService';
import { client, JSONBigInt } from '../../square/_squareConfig';
import { recordDiscountRedemption } from '../_recordDiscountRedemption';
import sendSubscriptionConfirmation from '../_sendConfirmationEmail';
import sendGiftGiverEmail from '../_sendGiftGiverEmail';
import sendGiftRecipientEmail from '../_sendGiftRecipientEmail';

interface ReferralPolicy {
  referee_benefit_type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  referee_benefit_value: number;
  name?: string;
}

export async function POST(req: NextRequest) {
  const logPrefix = '[Create One-Time Order API]';
  let orderRef: FirebaseFirestore.DocumentReference | null = null;
  let cardId: string | undefined;
  let locationId: string | undefined;

  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      console.warn(`${logPrefix} App Check verification failed.`);
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Parse and validate request data
    const requestData = await req.json();
    const validationResult = reqSchema.safeParse(requestData);

    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors[0]?.message || 'Invalid request data';
      console.error(
        `${logPrefix} Validation failed:`,
        JSON.stringify(validationResult.error.flatten())
      );
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }
    const validatedData = validationResult.data;

    // Destructure validated data
    const {
      variationId,
      deliveryDate,
      zoneId,
      email,
      phone,
      addressLine1,
      addressLine2,
      city,
      state,
      zipCode,
      sourceId,
      customerId,
      firstName,
      lastName,
      deliveryNotes,
      quantity = 1,
      discountId,
      recipientFirstName,
      recipientLastName,
      recipientEmail,
      recipientPhone,
      recipientGiftMessage,
      recipientSquareCustomerId,
      orderFor,
      targetType,
      referralPolicyId,
      referralCode,
    } = validatedData;

    // Basic field validation
    if (
      !variationId ||
      !zoneId ||
      !email ||
      !sourceId ||
      !customerId ||
      !zipCode ||
      !firstName ||
      !lastName ||
      !city ||
      !state ||
      !addressLine1 ||
      !quantity
    ) {
      console.error(`${logPrefix} Missing required fields after validation.`);
      return NextResponse.json(
        { error: 'Internal Error: Missing required fields' },
        { status: 400 }
      );
    }
    let referralPolicyInformation: ReferralPolicy | undefined;
    // Get the referral code discount policy form firebase
    if (referralPolicyId) {
      const referralPolicyRef = adminDb
        .collection(COLLECTIONS.REFERRAL_POLICIES)
        .doc(referralPolicyId);
      const referralPolicyDoc = await referralPolicyRef.get();

      if (!referralPolicyDoc.exists) {
        console.error(`${logPrefix} Referral policy not found: ${referralPolicyId}`);
        return NextResponse.json({ error: 'Invalid referral policy' }, { status: 400 });
      }

      referralPolicyInformation = referralPolicyDoc.data() as ReferralPolicy;
    }

    const isGift = isGiftHomeDelivery(orderFor);

    // Verify this is actually a one-time order
    if (!isOneTimeHomeDelivery(orderFor)) {
      console.error(`${logPrefix} Invalid order type for one-time order: ${orderFor}`);
      return NextResponse.json({ error: 'Invalid order type for one-time order' }, { status: 400 });
    }

    // --- Discount Validation ---
    let validatedDiscountDetails = null;
    if (discountId) {
      validatedDiscountDetails = await fetchDiscountDetails(discountId);
      if (!validatedDiscountDetails) {
        console.warn(
          `${logPrefix} Discount ID ${discountId} provided by client is no longer valid.`
        );
        return NextResponse.json(
          { error: 'Provided discount is no longer valid. Please remove it and try again.' },
          { status: 400 }
        );
      }
      if (!targetType) {
        console.error(`${logPrefix} targetType is missing when discountId is present.`);
        return NextResponse.json({ error: 'Discount target type missing.' }, { status: 400 });
      }
    }
    // --- End Discount Validation ---

    // --- Product & Delivery Fee Info ---
    const deliveryFeeVariationId = process.env.NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID;
    const productId = process.env.NEXT_SQUARE_SUBSCRIPTION_PRODUCT_ID;
    locationId = process.env.NEXT_SQUARE_LOCATION_ID;

    if (!deliveryFeeVariationId || !productId || !locationId) {
      console.error(
        `${logPrefix} Essential Env Vars missing: ProductID, DeliveryFeeVarID, or LocationID.`
      );
      return NextResponse.json({ error: 'Server configuration error.' }, { status: 500 });
    }

    // --- Get product details ---
    const catalogResponse = await client.catalogApi.retrieveCatalogObject(variationId, true);

    const productItemData = catalogResponse.result.relatedObjects?.find(obj => obj.type === 'ITEM');

    // --- Create Square Card ---
    const displayName = `${firstName} ${lastName}`;
    const cardParams = {
      idempotencyKey: crypto.randomUUID(),
      sourceId: sourceId as string,
      card: { customerId: customerId as string, cardholderName: displayName },
    };

    try {
      const cardResponse = await client.cardsApi.createCard(cardParams);
      cardId = cardResponse.result.card?.id;
    } catch (err) {
      console.error(`${logPrefix} Failed to create card.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Invalid payment card data' }, { status: 500 });
    }

    // --- Create Firestore Order Record ---
    const zoneRef = adminDb.collection(COLLECTIONS.DELIVERY_ZONES).doc(zoneId);
    const zoneSnap = await zoneRef.get();
    const zoneData = zoneSnap.data();
    if (!zoneData) {
      console.error(`${logPrefix} Zone not found in Firestore: ${zoneId}`);
      throw new Error('Zone not found');
    }

    const deliveryWindow = zoneData.delivery_window;
    const startDate = deliveryDate ? convertToISOWithZeroTime(deliveryDate) : null;

    // --- Create Square Order ---
    const recipientDisplayName = isGift
      ? `${recipientFirstName} ${recipientLastName}`
      : displayName;

    const fulfillmentDetails = {
      type: 'SHIPMENT',
      shipmentDetails: {
        recipient: {
          customerId: isGift ? recipientSquareCustomerId : customerId,
          displayName: isGift ? recipientDisplayName : displayName,
          emailAddress: isGift ? recipientEmail : email,
          phoneNumber: isGift ? recipientPhone : phone,
          address: {
            addressLine1,
            locality: city,
            administrativeDistrictLevel1: state,
            postalCode: zipCode,
            country: 'US',
            ...(addressLine2 && { addressLine2 }),
          },
        },
        ...(deliveryDate && { expectedShippedAt: convertToISOWithZeroTime(deliveryDate) }),
        ...(deliveryNotes && { shippingNote: deliveryNotes }),
      },
    };

    const productLineItemUid = `prod-${crypto.randomUUID()}`;
    const deliveryLineItemUid = `del-${crypto.randomUUID()}`;
    const discountUid = `disc-${crypto.randomUUID()}`;

    const baseLineItems = [
      {
        catalogObjectId: variationId as string,
        quantity: String(quantity),
        uid: productLineItemUid,
        ...(discountId &&
          targetType === 'PRODUCT' && {
            appliedDiscounts: [{ discountUid }],
          }),
      },
      {
        catalogObjectId: deliveryFeeVariationId as string,
        quantity: '1',
        uid: deliveryLineItemUid,
        ...(discountId &&
          targetType === 'DELIVERY_FEE' && {
            appliedDiscounts: [{ discountUid }],
          }),
      },
    ];

    const orderParams: any = {
      idempotencyKey: crypto.randomUUID(),
      order: {
        locationId,
        customerId: customerId as string,
        lineItems: baseLineItems,
        source: { name: 'My Very Mulberry Home Delivery' },
        fulfillments: [fulfillmentDetails],
        ...(referralPolicyInformation && {
          discounts: [
            {
              type:
                referralPolicyInformation.referee_benefit_type === 'PERCENTAGE'
                  ? 'FIXED_PERCENTAGE'
                  : 'FIXED_AMOUNT',
              name: 'Referral discount',
              scope: 'ORDER',
              ...(referralPolicyInformation.referee_benefit_type === 'PERCENTAGE' && {
                percentage: String(referralPolicyInformation.referee_benefit_value),
              }),
              ...(referralPolicyInformation.referee_benefit_type === 'FIXED_AMOUNT' && {
                amountMoney: {
                  amount: BigInt(referralPolicyInformation.referee_benefit_value * 100),
                  currency: 'USD',
                },
              }),
            },
          ],
        }),
        ...(discountId &&
          validatedDiscountDetails && {
            discounts: [
              {
                uid: discountUid,
                catalogObjectId: discountId,
                scope: 'LINE_ITEM',
              },
            ],
          }),
        metadata: {
          ...(recipientGiftMessage && { gift_message: recipientGiftMessage }),
          zoneId,
          orderFor,
          ...(isGift && { giftBy: displayName }),
          ...(referralPolicyInformation && { referralCode, referralPolicyId }),
        },
      },
    };

    let orderObject: SquareOrder | undefined;
    let orderId, totalMoney;
    try {
      // Create the order
      const orderResponse = await client.ordersApi.createOrder(orderParams);
      orderObject = orderResponse.result.order;
      orderId = orderResponse.result.order?.id;
      totalMoney = orderResponse.result.order?.totalMoney;
    } catch (err) {
      console.error(`${logPrefix} Failed to create order.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to create order' }, { status: 500 });
    }

    let paymentObject: SquarePayment | undefined;
    try {
      // Process payment for the order
      const paymentParams = {
        idempotencyKey: crypto.randomUUID(),
        sourceId: cardId as string,
        orderId: orderId,
        customerId: customerId,
        locationId: locationId,
        amountMoney: totalMoney,
      };

      const paymentResponse = await client.paymentsApi.createPayment(paymentParams);
      paymentObject = paymentResponse.result.payment;
    } catch (err) {
      console.error(`${logPrefix} Failed to process payment.`, JSONBigInt.stringify(err));
      return NextResponse.json({ error: 'Failed to process payment' }, { status: 500 });
    }

    // Create Firestore record
    const orderData: any = {
      square_order_id: orderId,
      square_payment_id: paymentObject?.id,
      square_product_id: productId,
      square_variation_id: variationId,
      product_name: productItemData?.itemData?.name ?? 'One-Time Very Mulberries',
      delivery_window: deliveryWindow || '',
      delivery_notes: deliveryNotes || '',
      delivery_date: startDate,
      zone_id: zoneId,
      email: email,
      customerId,
      address: {
        addressLine1,
        city,
        state,
        zipCode,
        ...(addressLine2 && { addressLine2 }),
      },
      ...(!isGift && {
        phone: phone,
      }),
      ...(isGift && {
        recipient_gift_message: recipientGiftMessage,
      }),
      order_for: orderFor,
      ...(isGift && {
        recipient: {
          square_customer_id: recipientSquareCustomerId,
          first_name: recipientFirstName,
          last_name: recipientLastName,
          email_address: recipientEmail,
          phone_number: recipientPhone,
        },
        ...(recipientGiftMessage && { gift_message: recipientGiftMessage }),
      }),
      quantity: quantity,
      status: 'completed',
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
      ...(validatedDiscountDetails && {
        applied_discount: {
          id: validatedDiscountDetails.id,
          codeName: validatedDiscountDetails.codeName,
          targetType: targetType,
        },
      }),
      square_order: JSON.parse(JSONBigInt.stringify(orderObject)),
      ...(referralPolicyInformation && { referralCode, referralPolicyId }),
    };

    orderRef = await adminDb.collection(COLLECTIONS.SUBSCRIPTIONS).add(orderData);

    // --- Send Confirmation Email ---
    try {
      // --- Re-validate discount details for email ---
      let discountDetailsForEmail: ItemDiscountValidationResult | null = null;
      if (validatedData.discountId && validatedData.variationId) {
        try {
          discountDetailsForEmail = await validateDiscountService(
            '',
            validatedData.variationId,
            validatedData.discountId
          );
          if (!discountDetailsForEmail?.isValid) {
            console.warn(
              `[Create One-Time API] Re-validation failed for discount ID: ${validatedData.discountId}. Proceeding without discount details in email.`
            );
            discountDetailsForEmail = null; // Reset on failure
          }
        } catch (revalError) {
          console.warn(
            `[Create One-Time API] Error during discount re-validation: ${revalError}. Proceeding without discount details in email.`
          );
          discountDetailsForEmail = null; // Reset on error
        }
      }
      // --- End Re-validation ---

      // Record discount redemption if a discount was applied
      if (validatedDiscountDetails && discountId) {
        try {
          const discountAmount = (discountDetailsForEmail?.discountAmount ?? 0) / 100;
          await recordDiscountRedemption(
            validatedDiscountDetails.codeName,
            discountAmount,
            email,
            customerId,
            orderId
          );
        } catch (discountRecordError) {
          console.warn(
            `${logPrefix} Failed to record discount redemption:`,
            discountRecordError instanceof Error
              ? discountRecordError.message
              : String(discountRecordError)
          );
          // Non-blocking error - continue with the order process
        }
      }

      // Get order details for the email
      const orderDetailsForEmailResponse = await client.ordersApi
        .retrieveOrder(orderId as string)
        .catch(e => {
          console.warn(
            `${logPrefix} Failed to retrieve order ${orderId} for email:`,
            (e as any).body || (e as Error).message
          );
          return null;
        });

      if (orderDetailsForEmailResponse?.result?.order) {
        const orderDataForEmail = getSubscriptionDetailsFromOrder(
          orderDetailsForEmailResponse.result.order as SquareOrder
        );
        const deliveryAddress = addressLine2
          ? `${addressLine1}, ${addressLine2}, ${city}, ${state} ${zipCode}`
          : `${addressLine1}, ${city}, ${state} ${zipCode}`;

        let variationNameForEmail = 'Selected Pack';
        if (variationId) {
          try {
            const variationObj = await client.catalogApi.retrieveCatalogObject(variationId);
            variationNameForEmail =
              variationObj.result?.object?.itemVariationData?.name ?? variationNameForEmail;
          } catch {
            /* Ignore */
          }
        }

        if (isGift) {
          if (recipientEmail) {
            await sendGiftRecipientEmail({
              gifter_name: displayName,
              gifter_email: email,
              recipient_name: recipientDisplayName,
              recipient_email: recipientEmail,
              delivery_address: deliveryAddress,
              delivery_notes: deliveryNotes || '',
              gift_message: recipientGiftMessage || '',
              isOneTime: true,
            });
            await sendGiftGiverEmail({
              gifter_name: displayName,
              gifter_email: email,
              recipient_name: recipientDisplayName,
              recipient_email: recipientEmail,
              isOneTime: true,
            });
          }
        } else {
          await sendSubscriptionConfirmation({
            referralApplied: !!referralPolicyInformation,
            subscriptionId: orderId as string,
            customerEmail: email,
            customerName: displayName,
            customerPhone: phone as string,
            deliveryAddress,
            productName: `${productItemData?.itemData?.name ?? 'Product'} - ${variationNameForEmail}`,
            totalAmount: orderDataForEmail.total,
            productPrice: orderDataForEmail.productPrice,
            deliveryFee: referralPolicyInformation
              ? orderDataForEmail.originalDeliveryFee
              : orderDataForEmail.deliveryFee,
            quantity: quantity,
            discountAmount: referralPolicyInformation
              ? orderDataForEmail.totalDiscount
              : discountDetailsForEmail?.discountAmount,
            firstCycleTotal: discountDetailsForEmail?.firstCycleTotalPreview,
            discountType: discountDetailsForEmail?.type,
            discountValue: discountDetailsForEmail?.value,
            discountCodeName: discountDetailsForEmail?.discountCodeName,
            targetType: discountDetailsForEmail?.targetType,
            originalProductPrice: discountDetailsForEmail?.originalProductPrice,
            originalDeliveryFeePrice: discountDetailsForEmail?.originalDeliveryFeePrice,
            originalTotalAmount: orderDataForEmail.originalTotalAmount,
            isOneTime: true,
          });
        }
      } else {
        console.error(
          `${logPrefix} Could not retrieve order details (Order ID: ${orderId}) for confirmation email.`
        );
      }
    } catch (emailError: unknown) {
      console.error(
        `${logPrefix} Error sending confirmation email:`,
        emailError instanceof Error ? emailError.message : String(emailError)
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      orderId: orderRef.id,
      squareOrderId: orderId,
    });
  } catch (error: any) {
    console.error(`${logPrefix} Error:`, JSONBigInt.stringify(error));

    // Update Firestore record with error if it was created
    if (orderRef) {
      await orderRef
        .update({
          status: 'failed',
          error_message: error.message || 'Unknown error occurred',
          updated_at: Timestamp.now(),
        })
        .catch(e => console.error(`${logPrefix} Failed to update order with error:`, e));
    }

    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_CREATE_ORDER }, { status: 500 });
  }
}
