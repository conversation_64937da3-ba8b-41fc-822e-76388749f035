import sgMail from '@sendgrid/mail';

// Initialize SendGrid
if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

interface SendGiftRecipientEmailParams {
  gifter_name: string;
  gifter_email: string;
  delivery_address: string;
  delivery_notes: string;
  gift_message: string;
  recipient_name: string;
  recipient_email: string;
  isOneTime: boolean;
}

/**
 * Sends a subscription confirmation email using SendGrid
 */
export default async function sendGiftRecipientEmail({
  gifter_name,
  gifter_email,
  delivery_address,
  delivery_notes,
  gift_message,
  recipient_name,
  recipient_email,
  isOneTime,
}: SendGiftRecipientEmailParams): Promise<boolean> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      console.error('SENDGRID_API_KEY is not defined');
      return false;
    }

    if (
      !process.env.SENDGRID_FROM_EMAIL ||
      !process.env.SENDGRID_SUBSCRIPTION_RECIPIENT_TEMPLATE_ID
    ) {
      console.error('Missing required SendGrid configuration');
      return false;
    }

    const msg = {
      to: recipient_email,
      from: {
        email: process.env.SENDGRID_FROM_EMAIL,
        name: process.env.SENDGRID_FROM_NAME,
      },
      templateId: process.env.SENDGRID_SUBSCRIPTION_RECIPIENT_TEMPLATE_ID,
      dynamicTemplateData: {
        gifter_name,
        gifter_email,
        delivery_address,
        delivery_notes,
        gift_message,
        recipient_name,
        isOneTime,
        my_verymulberry_link: process.env.NEXT_PUBLIC_BASE_URL,
      },
    };

    await sgMail.send(msg);
    return true;
  } catch (error) {
    console.error('Error sending gift recipient email:', JSON.stringify(error));
    return false;
  }
}
