import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(request: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(request);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { data, addressId } = await request.json();

    if (!data || !addressId) {
      return NextResponse.json(
        { error: ERROR_MESSAGES.MISSING_REQUIRED_SUBSCRIPTION_ADDRESS_FIELDS },
        { status: 400 }
      );
    }

    const missingAddressRef = adminDb.collection(COLLECTIONS.MISSING_DELIVERY_ADDRESSES);
    await missingAddressRef.doc(addressId).update({ ...data, updated_at: Timestamp.now() });

    return NextResponse.json({
      success: true,
      message: 'Subscription: Update missing address logged successfully',
    });
  } catch (error) {
    console.error('Error logging update missing address:', error);
    return NextResponse.json(
      { error: ERROR_MESSAGES.FAILED_TO_LOG_MISSING_ADDRESS },
      { status: 500 }
    );
  }
}
