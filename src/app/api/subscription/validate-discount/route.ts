import { NextRequest, NextResponse } from 'next/server';

import { validateDiscount as validateDiscountService } from '@/src/app/api/_squareService';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { ItemDiscountValidationResult, DiscountTargetType } from '@/src/types/ItemDiscount';

import { validateFirestoreDiscountRestrictions } from '../_validateFirestoreDiscountRestrictions';

interface ValidateDiscountRequest {
  discountCode: string;
  productVariationId?: string;
  squareCustomerId?: string;
  email?: string;
}

// Define the API response structure, ensuring value is string | null
interface ValidateDiscountApiResponse extends Omit<ItemDiscountValidationResult, 'value'> {
  isValid: boolean;
  message: string;
  discountId?: string;
  discountCodeName?: string;
  type?: string | null;
  value: string | null; // Value will be sent as string or null
  targetType?: DiscountTargetType;
  firstCycleTotalPreview?: number; // Cents
  discountAmount?: number; // Cents
  originalProductPrice?: number; // Cents
  originalDeliveryFeePrice?: number; // Cents
}
type ValidateDiscountResponse = ValidateDiscountApiResponse | { isValid: false; message: string };

export async function POST(req: NextRequest): Promise<NextResponse<ValidateDiscountResponse>> {
  const logPrefix = '[Validate Discount API]';
  try {
    // App Check Verification
    const appCheckResult = await verifyAppCheckToken(req);
    if (appCheckResult) {
      console.warn(`${logPrefix} App Check failed: ${appCheckResult.error}`);
      return NextResponse.json(
        { isValid: false, message: appCheckResult.error },
        { status: appCheckResult.status }
      );
    }

    const body: ValidateDiscountRequest = await req.json();
    const { discountCode, productVariationId, squareCustomerId, email } = body;

    if (!discountCode) {
      console.warn(`${logPrefix} Missing discount code.`);
      return NextResponse.json(
        { isValid: false, message: 'Discount code is required.' },
        { status: 400 }
      );
    }
    if (!productVariationId) {
      console.warn(`${logPrefix} Missing product variation ID.`);
      return NextResponse.json(
        { isValid: false, message: 'Product information is required.' },
        { status: 400 }
      );
    }

    // If Firestore validation passes, proceed with Square validation
    const validationResult: ItemDiscountValidationResult = await validateDiscountService(
      discountCode,
      productVariationId
    );

    if (!validationResult.isValid) {
      const status = validationResult.message === 'Invalid or expired discount code.' ? 404 : 400;
      return NextResponse.json({ isValid: false, message: validationResult.message }, { status });
    }

    const orderAmount =
      (validationResult.originalProductPrice ?? 0) +
      (validationResult.originalDeliveryFeePrice ?? 0);
    // First validate against Firestore restrictions
    const firestoreValidation = await validateFirestoreDiscountRestrictions(
      discountCode,
      productVariationId,
      squareCustomerId,
      email,
      orderAmount
    );

    if (!firestoreValidation.isValid) {
      return NextResponse.json(
        { isValid: false, message: firestoreValidation.message },
        { status: 400 }
      );
    }

    const responsePayload: ValidateDiscountApiResponse = {
      ...validationResult,
      isValid: true,
      value:
        typeof validationResult.value === 'bigint'
          ? validationResult.value.toString()
          : (validationResult.value as string | null),
    };

    return NextResponse.json(responsePayload);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`${logPrefix} Error:`, errorMessage);
    return NextResponse.json(
      { isValid: false, message: ERROR_MESSAGES.INTERNAL_SERVER_ERROR },
      { status: 500 }
    );
  }
}
