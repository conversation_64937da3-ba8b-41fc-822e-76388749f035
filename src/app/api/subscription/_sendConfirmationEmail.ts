import sgMail from '@sendgrid/mail';

import { DiscountTargetType } from '@/src/types/ItemDiscount'; // Import DiscountTargetType

// Initialize SendGrid
if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

interface SubscriptionConfirmationParams {
  customerEmail: string;
  subscriptionId: string;
  deliveryAddress: string;
  customerName: string;
  customerPhone: string;
  productName: string;
  totalAmount: number;
  productPrice: number;
  deliveryFee: number;
  quantity: number;
  // Optional discount fields
  discountAmount?: number; // Amount discounted in cents
  firstCycleTotal?: number; // First cycle total in cents
  discountType?: string | null; // Type of discount (e.g., FIXED_PERCENTAGE)
  discountValue?: string | bigint | null; // Value of discount (percentage or amount in cents)
  discountCodeName?: string; // Name/code of the applied discount
  targetType?: DiscountTargetType; // Target of the discount (PRODUCT or DELIVERY_FEE)
  originalProductPrice?: number; // Original price in cents before discount
  originalDeliveryFeePrice?: number; // Original fee in cents before discount
  isOneTime: boolean;
  referralApplied: boolean;
  originalTotalAmount: number;
}

/**
 * Sends a subscription confirmation email using SendGrid
 */
export default async function sendSubscriptionConfirmation({
  customerEmail,
  subscriptionId,
  deliveryAddress,
  customerName,
  customerPhone,
  productName,
  totalAmount,
  productPrice,
  deliveryFee,
  quantity,
  // Destructure optional discount params
  discountAmount,
  firstCycleTotal,
  discountType,
  discountValue,
  discountCodeName,
  targetType,
  originalProductPrice,
  originalDeliveryFeePrice,
  isOneTime,
  referralApplied,
  originalTotalAmount,
}: SubscriptionConfirmationParams): Promise<boolean> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      console.error('SENDGRID_API_KEY is not defined');
      return false;
    }

    if (
      !process.env.SENDGRID_FROM_EMAIL ||
      !process.env.SENDGRID_SUBSCRIPTION_CONFIRMATION_TEMPLATE_ID
    ) {
      console.error('Missing required SendGrid configuration');
      return false;
    }

    // --- Prepare Discount Data for Template ---
    let discountDataForTemplate: Record<string, any> = {};
    let discountLabel = '';

    if (discountAmount && discountAmount > 0 && discountCodeName && targetType) {
      const formattedDiscountAmount = (discountAmount / 100).toFixed(2);
      const formattedFirstCycleTotal = firstCycleTotal ? (firstCycleTotal / 100).toFixed(2) : null;
      const formattedOriginalProductPrice = originalProductPrice
        ? (originalProductPrice / 100).toFixed(2)
        : null;
      const formattedOriginalDeliveryFeePrice = originalDeliveryFeePrice
        ? (originalDeliveryFeePrice / 100).toFixed(2)
        : null;

      const targetText = targetType === 'DELIVERY_FEE' ? 'Delivery Fee' : 'Product';

      if (discountType?.includes('PERCENTAGE') && discountValue) {
        discountLabel = `Discount (${discountValue}% off ${targetText})`;
      } else {
        discountLabel = `Discount (-$${formattedDiscountAmount} off ${targetText})`;
      }

      discountDataForTemplate = {
        discount_applied: true,
        discount_label: discountLabel,
        discount_amount: formattedDiscountAmount,
        first_cycle_total: formattedFirstCycleTotal,
        original_product_price: formattedOriginalProductPrice,
        original_delivery_fee_price: formattedOriginalDeliveryFeePrice,
        regular_total: (
          (originalProductPrice ?? 0) / 100 +
          (originalDeliveryFeePrice ?? 0) / 100
        ).toFixed(2),
      };
    }

    const msg = {
      to: customerEmail,
      from: {
        email: process.env.SENDGRID_FROM_EMAIL,
        name: process.env.SENDGRID_FROM_NAME,
      },
      templateId: process.env.SENDGRID_SUBSCRIPTION_CONFIRMATION_TEMPLATE_ID,
      dynamicTemplateData: {
        subscription_id: subscriptionId,
        delivery_address: deliveryAddress,
        customer_email: customerEmail,
        customer_name: customerName || customerEmail.split('@')[0],
        customer_phone: customerPhone,
        product_name: productName,
        product_price: productPrice,
        delivery_fee: discountDataForTemplate?.original_delivery_fee_price ?? deliveryFee,
        total_amount: referralApplied
          ? originalTotalAmount
          : (discountDataForTemplate?.regular_total ?? totalAmount),
        quantity: quantity,
        subtotal: productPrice * quantity,
        my_verymulberry_link: process.env.NEXT_PUBLIC_BASE_URL,
        discountApplied: referralApplied
          ? referralApplied
          : (discountDataForTemplate?.discount_applied ?? false),
        discountPrice: referralApplied ? discountAmount : discountDataForTemplate?.discount_amount,
        discountLabel: referralApplied
          ? 'Referral Discount'
          : discountDataForTemplate?.discount_label,
        firstCycleTotal: discountDataForTemplate?.first_cycle_total ?? totalAmount,
        isOneTime,
      },
    };

    await sgMail.send(msg);
    return true;
  } catch (error) {
    console.error('Error sending subscription confirmation email:', error);
    return false;
  }
}
