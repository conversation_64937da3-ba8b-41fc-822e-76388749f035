import { NextRequest, NextResponse } from 'next/server';
import { Card, Order } from 'square';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';

import { client, JSONBigInt } from '../../square/_squareConfig';

/**
 * Fetches user subscription details from Square API
 *
 * @route POST /api/subscription/get-user-subscription
 * @param req The incoming request with email in the request body
 * @returns Subscription details for the user
 */
export async function POST(req: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }
    // Validate that the request contains JSON content
    if (!req.headers.get('content-type')?.includes('application/json')) {
      return NextResponse.json({ error: 'Request must be application/json' }, { status: 400 });
    }

    // Safely parse the JSON request body
    let body;
    try {
      body = await req.json();
    } catch (e) {
      return NextResponse.json({ error: e || 'Invalid JSON in request body' }, { status: 400 });
    }

    const { email } = body;

    // Verify email matches authenticated user's email
    if (authResult.decodedToken.email !== email) {
      return NextResponse.json({ error: 'Unauthorized access' }, { status: 401 });
    }

    // Validate required fields
    if (!email) {
      return new NextResponse(
        JSON.stringify({
          error: 'Email is required',
        }),
        { status: 400 }
      );
    }

    try {
      // First, find the Square customer ID associated with this email
      const customerResponse = await client.customersApi.searchCustomers({
        query: {
          filter: {
            emailAddress: {
              exact: email,
            },
          },
        },
      });

      if (!customerResponse.result.customers || customerResponse.result.customers.length === 0) {
        return new NextResponse(
          JSON.stringify({
            subscription: null,
            message: 'No customer found with this email',
          }),
          { status: 200 }
        );
      }

      const customerId = customerResponse.result.customers[0].id;

      // Now fetch subscriptions for this customer
      try {
        const subscriptionsResponse = await client.subscriptionsApi.searchSubscriptions({
          include: ['actions'],
          query: {
            filter: {
              customerIds: [customerId as string],
            },
          },
        });

        if (
          !subscriptionsResponse.result.subscriptions ||
          subscriptionsResponse.result.subscriptions.length === 0
        ) {
          return new NextResponse(
            JSON.stringify({
              subscriptions: [],
              message: 'No active subscriptions found for this customer',
            }),
            { status: 200 }
          );
        }

        // Return all subscription data
        try {
          // Map through all subscriptions and create safe objects for each
          const safeSubscriptions = await Promise.all(
            subscriptionsResponse.result.subscriptions.map(async subscription => {
              // Get the orderTemplateId from the first phase
              const orderTemplateId = subscription.phases?.[0]?.orderTemplateId;
              const planVariationId = subscription.planVariationId;

              // Create promises for parallel execution
              const promises = [];

              // Add order details promise if orderTemplateId exists
              const orderPromise = orderTemplateId
                ? client.ordersApi.retrieveOrder(orderTemplateId).catch(error => {
                    console.error(
                      `Error fetching order for template ID ${orderTemplateId}:`,
                      error
                    );
                    return { result: { order: null } };
                  })
                : Promise.resolve({ result: { order: null } });
              promises.push(orderPromise);

              // Add card details promise if cardId exists
              const cardPromise = subscription.cardId
                ? client.cardsApi.retrieveCard(subscription.cardId).catch(error => {
                    console.error(
                      `Error fetching card details for card ID ${subscription.cardId}:`,
                      error
                    );
                    return { result: { card: null } };
                  })
                : Promise.resolve({ result: { card: null } });
              promises.push(cardPromise);

              // Add catalog object promise if planVariationId exists
              const catalogPromise = planVariationId
                ? client.catalogApi.retrieveCatalogObject(planVariationId).catch(error => {
                    console.error(
                      `Error fetching catalog object for ID ${planVariationId}:`,
                      error
                    );
                    return { result: { object: null } };
                  })
                : Promise.resolve({ result: { object: null } });
              promises.push(catalogPromise);

              // Execute all promises in parallel
              const [orderResponse, cardResponse, catalogResponse] = (await Promise.all(
                promises
              )) as [
                { result: { order: Order | null } },
                { result: { card: Card | null } },
                { result: { object: any | null } },
              ];

              // Process phases without including order details
              const processedPhases = (subscription.phases || []).map(phase => ({
                uid: phase.uid || '',
                ordinal: phase.ordinal || 0,
                orderTemplateId: phase.orderTemplateId || null,
                planPhaseUid: phase.planPhaseUid || null,
              }));

              // Fetch invoice details if there are invoice IDs
              const invoiceDetails = await Promise.all(
                subscription.invoiceIds?.map(async invoiceId => {
                  try {
                    const invoiceResponse = await client.invoicesApi.getInvoice(invoiceId);
                    return invoiceResponse.result.invoice;
                  } catch (error) {
                    console.error(`Error fetching invoice ${invoiceId}:`, error);
                    return null;
                  }
                }) || []
              );

              // Filter out any null values from failed requests
              const invoices = invoiceDetails.filter(invoice => invoice !== null);

              return {
                id: subscription.id,
                actions: subscription.actions ?? [],
                locationId: subscription.locationId,
                customerId: subscription.customerId,
                startDate: subscription.startDate,
                status: subscription.status,
                createdAt: subscription.createdAt,
                planVariationId: subscription.planVariationId,
                cardId: subscription.cardId || null,
                cardDetails: cardResponse.result.card || null,
                phases: processedPhases,
                orderDetails: orderResponse.result.order || null,
                catalogDetails: catalogResponse.result.object || null,
                invoice_ids: subscription.invoiceIds || [],
                invoices: invoices,
                ...(subscription.canceledDate && {
                  canceledDate: subscription.canceledDate,
                }),
                ...(subscription.chargedThroughDate && {
                  chargedThroughDate: subscription.chargedThroughDate,
                }),
              };
            })
          );

          return new NextResponse(
            JSONBigInt.stringify({
              subscriptions: safeSubscriptions,
              message: 'Subscriptions retrieved successfully',
            }),
            { status: 200 }
          );
        } catch (jsonError) {
          console.error('Error processing subscription data:', jsonError);
          return new NextResponse(
            JSON.stringify({
              error: 'Failed to process subscription data',
              details: jsonError instanceof Error ? jsonError.message : 'Unknown error',
              subscriptions: [],
            }),
            { status: 500 }
          );
        }
      } catch (subscriptionError) {
        // Check if it's a "customer not found" error
        if (
          typeof subscriptionError === 'object' &&
          subscriptionError !== null &&
          'errors' in subscriptionError &&
          Array.isArray(subscriptionError.errors) &&
          subscriptionError.errors.some(err => err.code === 'CUSTOMER_NOT_FOUND')
        ) {
          return new NextResponse(
            JSON.stringify({
              subscription: null,
              message: 'No customer found with this ID in Square',
            }),
            { status: 200 }
          );
        }

        // Re-throw for other errors to be caught by the outer catch block
        throw subscriptionError;
      }
    } catch (squareError) {
      console.error('Square API error:', JSONBigInt.stringify(squareError));

      // Check if it's an authentication error
      if (
        typeof squareError === 'object' &&
        squareError !== null &&
        'statusCode' in squareError &&
        squareError.statusCode === 401
      ) {
        return new NextResponse(
          JSON.stringify({
            error: 'Square API authentication failed. Please check your API credentials.',
            subscription: null,
          }),
          { status: 401 }
        );
      }

      // Return a generic error message
      return new NextResponse(
        JSON.stringify({
          error: 'Square API error occurred',
          subscription: null,
        }),
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error fetching user subscription from Square:', error);
    return new NextResponse(
      JSON.stringify({
        error: 'Failed to fetch subscription details',
        details: error instanceof Error ? error.message : 'Unknown error',
        subscription: null,
      }),
      { status: 500 }
    );
  }
}
