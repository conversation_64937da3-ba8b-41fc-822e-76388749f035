import { format } from 'date-fns';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES, VALIDATION_ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

/**
 * Validates if a zip code is within any active delivery zone
 *
 * @route POST /api/subscription/validate-address
 * @param {Request} request - Contains zipCode in the request body
 * @returns {Promise<NextResponse>} JSON response indicating if delivery is available
 */
export async function POST(request: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(request);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { zipCode } = await request.json();

    if (!zipCode) {
      return NextResponse.json(
        { error: VALIDATION_ERROR_MESSAGES.ZIP_CODE_REQUIRED },
        { status: 400 }
      );
    }

    // Get current date for date range validation
    const currentDateStr = format(getPacificTimeNow(), 'yyyy-MM-dd');

    // Query all zones that contain the zip code using Admin SDK
    const deliveryZonesRef = adminDb.collection(COLLECTIONS.DELIVERY_ZONES);
    const querySnapshot = await deliveryZonesRef
      .where('zip_codes', 'array-contains', zipCode)
      .get();

    // If no zones found at all, return not available with status "not_found"
    if (querySnapshot.empty) {
      return NextResponse.json({
        isAvailable: false,
        status: 'not_found',
      });
    }

    // Filter for active zones
    const activeZones = querySnapshot.docs.filter(doc => {
      const data = doc.data();
      return data.is_active === true;
    });

    // Further filter active zones by date range
    const validZones = activeZones.filter(doc => {
      const data = doc.data();
      const endDate = data.end_date;

      // If no end date specified, consider it valid
      if (!endDate) return true;

      // Check end date if specified - only filter out if current date is after end date
      if (endDate && currentDateStr > endDate) {
        return false;
      }

      return true;
    });

    const isInActiveDeliveryZone = validZones.length > 0;

    // If in active delivery zone, get the delivery days and return available
    if (isInActiveDeliveryZone) {
      const zoneDoc = validZones[0];
      const zoneData = zoneDoc.data();
      const deliveryDays = zoneData.delivery_days || [];
      const zoneId = zoneDoc.id;
      const startDate = zoneData.start_date || null;
      const endDate = zoneData.end_date || null;
      const deliveryWindow = zoneData.delivery_window || null;

      return NextResponse.json({
        isAvailable: true,
        status: 'active',
        deliveryDays,
        zoneId,
        startDate,
        endDate,
        deliveryWindow,
      });
    }

    // If in a zone but not active, return not available with status "inactive"
    return NextResponse.json({
      isAvailable: false,
      status: 'inactive',
    });
  } catch (error) {
    console.error('Error validating delivery zone:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_VALIDATE_ADDRESS }, { status: 500 });
  }
}
