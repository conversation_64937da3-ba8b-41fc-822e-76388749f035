import { /* DocumentReference, */ DocumentData, Timestamp } from 'firebase-admin/firestore'; // Removed unused import
import { NextRequest, NextResponse } from 'next/server';
import { Order as SquareOrder /* OrderLineItemAppliedDiscount */ } from 'square'; // Commented unused import

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import {
  convertToISOWithZeroTime,
  getNextSundayDate,
  getSubscriptionDetailsFromOrder,
  isGiftHomeDelivery,
  isOneTimeHomeDelivery,
} from '@/src/lib/utils';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
// Updated import: use the correct schema name
import { subscriptionRequestSchema as reqSchema } from '@/src/schema/subscription.zod';
import { ItemDiscountValidationResult } from '@/src/types/ItemDiscount';

import {
  fetchDiscountDetails,
  validateDiscount as validateDiscountService,
} from '../../_squareService'; // Import validateDiscountService
import { client, JSONBigInt } from '../../square/_squareConfig';
import { recordDiscountRedemption } from '../_recordDiscountRedemption';
import sendSubscriptionConfirmation from '../_sendConfirmationEmail';
import sendGiftGiverEmail from '../_sendGiftGiverEmail';
import sendGiftRecipientEmail from '../_sendGiftRecipientEmail';

export async function POST(req: NextRequest) {
  const logPrefix = '[Create Sub API]';
  let subscriptionRef: FirebaseFirestore.DocumentReference | null = null;
  let regularOrderTemplateId: string | undefined;
  let discountedOrderTemplateId: string | undefined;
  let cardId: string | undefined;
  let locationId: string | undefined; // Declare locationId here

  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      console.warn(`${logPrefix} App Check verification failed.`);
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Parse and validate request data
    const requestData = await req.json();
    const validationResult = reqSchema.safeParse(requestData);

    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors[0]?.message || 'Invalid request data';
      console.error(
        `${logPrefix} Validation failed:`,
        JSON.stringify(validationResult.error.flatten())
      );
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }
    const validatedData = validationResult.data;

    // Destructure validated data
    const {
      variationId,
      deliveryDate,
      zoneId,
      email,
      phone,
      addressLine1,
      addressLine2,
      city,
      state,
      zipCode,
      sourceId,
      customerId,
      firstName,
      lastName,
      deliveryNotes,
      quantity = 1,
      discountId,
      recipientFirstName,
      recipientLastName,
      recipientEmail,
      recipientPhone,
      recipientGiftMessage,
      recipientSquareCustomerId,
      orderFor,
      targetType,
      referralPolicyId,
      referralCode,
    } = validatedData;

    // Basic field validation
    if (
      !variationId ||
      !zoneId ||
      !email ||
      !sourceId ||
      !customerId ||
      !zipCode ||
      !firstName ||
      !lastName ||
      !city ||
      !state ||
      !addressLine1 ||
      !quantity
    ) {
      console.error(`${logPrefix} Missing required fields after validation.`);
      return NextResponse.json(
        { error: 'Internal Error: Missing required fields' },
        { status: 400 }
      );
    }

    const isOneTime = isOneTimeHomeDelivery(orderFor);
    const isGift = isGiftHomeDelivery(orderFor);

    // Validate recipient data for gift orders
    if (
      isGift &&
      (!recipientFirstName ||
        !recipientLastName ||
        !recipientEmail ||
        !recipientPhone ||
        !recipientSquareCustomerId)
    ) {
      return NextResponse.json(
        { error: 'Missing required recipient fields for gift order' },
        { status: 400 }
      );
    }

    let referralPolicyInformation;
    // Get the referral code discount policy form firebase
    if (referralPolicyId) {
      const referralPolicyRef = adminDb
        .collection(COLLECTIONS.REFERRAL_POLICIES)
        .doc(referralPolicyId);
      const referralPolicyDoc = await referralPolicyRef.get();

      if (!referralPolicyDoc.exists) {
        console.error(`${logPrefix} Referral policy not found: ${referralPolicyId}`);
        return NextResponse.json({ error: 'Invalid referral policy' }, { status: 400 });
      }

      referralPolicyInformation = referralPolicyDoc.data();
    }

    // --- Discount Validation ---
    let validatedDiscountDetails: Awaited<ReturnType<typeof fetchDiscountDetails>> | null = null;
    if (discountId) {
      validatedDiscountDetails = await fetchDiscountDetails(discountId);
      if (!validatedDiscountDetails) {
        console.warn(
          `${logPrefix} Discount ID ${discountId} provided by client is no longer valid.`
        );
        return NextResponse.json(
          { error: 'Provided discount is no longer valid. Please remove it and try again.' },
          { status: 400 }
        );
      }
      if (!targetType) {
        console.error(`${logPrefix} targetType is missing when discountId is present.`);
        return NextResponse.json({ error: 'Discount target type missing.' }, { status: 400 });
      }
    } else if (targetType) {
      console.error(`${logPrefix} targetType provided without a discountId.`);
      return NextResponse.json(
        { error: 'Invalid request: Discount target specified without a discount code.' },
        { status: 400 }
      );
    }
    // --- End Discount Validation ---

    // --- Product & Delivery Fee Info ---
    const deliveryFeeVariationId = process.env.NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID;
    const productId = process.env.NEXT_SQUARE_SUBSCRIPTION_PRODUCT_ID;
    locationId = process.env.NEXT_SQUARE_LOCATION_ID; // Assign to outer scope variable

    if (!deliveryFeeVariationId || !productId || !locationId) {
      console.error(
        `${logPrefix} Essential Env Vars missing: ProductID, DeliveryFeeVarID, or LocationID.`
      );
      return NextResponse.json({ error: 'Server configuration error.' }, { status: 500 });
    }

    const productResponse = await client.catalogApi
      .retrieveCatalogObject(productId, true)
      .catch(e => {
        console.error(
          `${logPrefix} Failed to retrieve product container ${productId}:`,
          (e as any).body || (e as Error).message
        );
        return null;
      });
    const productItemData = productResponse?.result?.object?.itemData;

    const displayName = `${firstName || ''} ${lastName || ''}`.trim() || email.split('@')[0];

    // Create recipient display name if this is a gift order
    let recipientDisplayName = '';
    if (isGift) {
      recipientDisplayName =
        `${recipientFirstName || ''} ${recipientLastName || ''}`.trim() ||
        recipientEmail?.split('@')[0] ||
        '';
    }

    // --- Firestore Subscription Record ---
    const zoneRef = adminDb.collection(COLLECTIONS.DELIVERY_ZONES).doc(zoneId);
    const zoneSnap = await zoneRef.get();
    const zoneData = zoneSnap.data();
    if (!zoneData) {
      console.error(`${logPrefix} Zone not found in Firestore: ${zoneId}`);
      throw new Error('Zone not found');
    }

    // const zoneEndDate = zoneData.end_date;
    const deliveryWindow = zoneData.delivery_window;
    const startDate = deliveryDate ? convertToISOWithZeroTime(deliveryDate) : getNextSundayDate();

    const subscriptionInitialData: any = {
      square_product_id: productId,
      square_variation_id: variationId,
      product_name: productItemData?.name ?? 'Subscription Product',
      variation_name: 'N/A',
      delivery_window: deliveryWindow || '',
      delivery_notes: deliveryNotes || '',
      zone_id: zoneId,
      email: email,
      customerId,
      address: {
        addressLine1,
        city,
        state,
        zipCode,
      },
      ...(!isGift && {
        phone: phone,
      }),
      ...(isGift && {
        recipient_gift_message: recipientGiftMessage,
      }),
      order_for: orderFor,
      ...(isGift && {
        recipient: {
          square_customer_id: recipientSquareCustomerId,
          first_name: recipientFirstName,
          last_name: recipientLastName,
          email_address: recipientEmail,
          phone_number: recipientPhone,
        },
        ...(recipientGiftMessage && { gift_message: recipientGiftMessage }),
      }),
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
      square_subscription_id: null,
      order_template_id: null,
      discounted_order_template_id: null,
      ...(validatedDiscountDetails && {
        applied_discount: {
          id: validatedDiscountDetails.id,
          codeName: validatedDiscountDetails.codeName,
          targetType: targetType,
        },
      }),
      ...(referralPolicyInformation && { referralCode, referralPolicyId }),
    };
    subscriptionRef = await adminDb
      .collection(COLLECTIONS.SUBSCRIPTIONS)
      .add(subscriptionInitialData);

    // --- Create Square Card ---
    const cardParams = {
      idempotencyKey: crypto.randomUUID(),
      sourceId: sourceId as string,
      card: { customerId: customerId as string, cardholderName: displayName },
    };
    try {
      const cardResponse = await client.cardsApi.createCard(cardParams);
      if (!cardResponse.result?.card?.id) {
        console.error(
          `${logPrefix} Failed to create card on file. Status: ${cardResponse.statusCode}. Body:`,
          JSONBigInt.stringify(cardResponse)
        );
        throw new Error('Failed to create card on file');
      }
      cardId = cardResponse.result.card.id;
      // console.info(`${logPrefix} Card created successfully: ${cardId}`); // Less noise
    } catch (cardError: unknown) {
      // Use unknown
      const errorBody = (cardError as any)?.body || '';
      const errorMessage = cardError instanceof Error ? cardError.message : String(cardError);
      console.error(`${logPrefix} Error creating card:`, errorBody || errorMessage);
      await subscriptionRef.update({
        status: 'failed',
        error_message: 'Failed to create card',
        updated_at: Timestamp.now(),
      });
      throw cardError;
    }

    // --- Create Square Order Templates ---
    const productLineItemUid = `prod-${subscriptionRef.id}`;
    const deliveryLineItemUid = `del-${subscriptionRef.id}`;

    const fulfillmentDetails = {
      type: 'SHIPMENT',
      shipmentDetails: {
        recipient: {
          customerId: isGift ? recipientSquareCustomerId : customerId,
          displayName: isGift ? recipientDisplayName : displayName,
          emailAddress: isGift ? recipientEmail : email,
          phoneNumber: isGift ? recipientPhone : phone,
          address: {
            addressLine1,
            locality: city,
            administrativeDistrictLevel1: state,
            postalCode: zipCode,
            country: 'US',
            ...(addressLine2 && { addressLine2 }),
          },
        },
        ...(deliveryDate && { expectedShippedAt: convertToISOWithZeroTime(deliveryDate) }),
        ...(deliveryNotes && { shippingNote: deliveryNotes }),
      },
    };

    const baseLineItems = [
      {
        catalogObjectId: variationId as string,
        quantity: String(quantity),
        uid: productLineItemUid,
      }, // Cast to string
      {
        catalogObjectId: deliveryFeeVariationId as string,
        quantity: '1',
        uid: deliveryLineItemUid,
      }, // Cast to string
    ];

    // 1. Create Regular Order Template
    try {
      const regularTemplateParams = {
        idempotencyKey: crypto.randomUUID(),
        order: {
          locationId,
          customerId: customerId as string,
          state: 'DRAFT',
          lineItems: baseLineItems,
          fulfillments: [fulfillmentDetails],
          metadata: {
            ...(recipientGiftMessage && { gift_message: recipientGiftMessage }),
            zoneId,
            orderFor,
            ...(isGift && { giftBy: displayName }),
          },
        },
      }; // Cast customerId
      const regularOrderTemplateResponse =
        await client.ordersApi.createOrder(regularTemplateParams);
      if (!regularOrderTemplateResponse.result?.order?.id) {
        console.error(
          `${logPrefix} Failed to create regular order template.`,
          JSONBigInt.stringify(regularOrderTemplateResponse)
        );
        throw new Error('Failed to create regular order template');
      }
      regularOrderTemplateId = regularOrderTemplateResponse.result.order.id;
      // console.info(`${logPrefix} Regular order template created: ${regularOrderTemplateId}`); // Less noise
    } catch (e: unknown) {
      // Use unknown
      const errorBody = (e as any)?.body || '';
      const errorMessage = e instanceof Error ? e.message : String(e);
      console.error(
        `${logPrefix} Error creating regular order template:`,
        errorBody || errorMessage
      );
      await subscriptionRef.update({
        status: 'failed',
        error_message: 'Failed to prepare regular order',
        updated_at: Timestamp.now(),
      });
      throw e;
    }

    // 2. Create Discounted Order Template (if applicable)
    if (validatedDiscountDetails && targetType) {
      try {
        discountedOrderTemplateId = await createDiscountCodeOrderTemplate(
          subscriptionRef,
          baseLineItems,
          productLineItemUid,
          deliveryLineItemUid,
          locationId,
          customerId,
          targetType,
          validatedDiscountDetails,
          fulfillmentDetails,
          recipientGiftMessage,
          zoneId,
          orderFor,
          isGift,
          displayName,
          logPrefix
        );
      } catch (e: unknown) {
        // Use unknown
        const errorBody = (e as any)?.body || '';
        const errorMessage = e instanceof Error ? e.message : String(e);
        console.error(
          `${logPrefix} Error creating discounted order template:`,
          errorBody || errorMessage
        );
        await subscriptionRef.update({
          status: 'failed',
          error_message: 'Failed to prepare discounted order',
          updated_at: Timestamp.now(),
        });
        throw e;
      }
    }

    // 3. Create Referral Order Template (if applicable)
    if (referralPolicyInformation && referralCode && referralPolicyId) {
      try {
        discountedOrderTemplateId = await createReferralCodeOrderTemplate(
          baseLineItems,
          locationId,
          customerId,
          fulfillmentDetails,
          recipientGiftMessage,
          zoneId,
          orderFor,
          isGift,
          displayName,
          logPrefix,
          referralPolicyInformation,
          referralCode,
          referralPolicyId
        );
      } catch (e: unknown) {
        // Use unknown
        const errorBody = (e as any)?.body || '';
        const errorMessage = e instanceof Error ? e.message : String(e);
        console.error(
          `${logPrefix} Error creating referral order template:`,
          errorBody || errorMessage
        );
        await subscriptionRef.update({
          status: 'failed',
          error_message: 'Failed to prepare referral order',
          updated_at: Timestamp.now(),
        });
        throw e;
      }
    }

    // --- Create Square Subscription ---
    let planVariationIdToUse: string | undefined;
    let subscriptionPhases: any[];

    if (discountedOrderTemplateId) {
      planVariationIdToUse = process.env.NEXT_SQUARE_SUBSCRIPTION_TWO_PHASE_PLAN_VARIATION_ID;
      subscriptionPhases = [
        { ordinal: BigInt(0), periods: 1, orderTemplateId: discountedOrderTemplateId },
        { ordinal: BigInt(1), orderTemplateId: regularOrderTemplateId! }, // Use ! assertion as it's guaranteed if discounted one exists
      ];
      // console.info(`${logPrefix} Using TWO_PHASE plan variation.`); // Less noise
    } else {
      planVariationIdToUse = process.env.NEXT_SQUARE_SUBSCRIPTION_PLAN_VARIATION_ID;
      subscriptionPhases = [{ ordinal: BigInt(0), orderTemplateId: regularOrderTemplateId! }]; // Use ! assertion
      // console.info(`${logPrefix} Using SINGLE_PHASE plan variation.`); // Less noise
    }

    if (!planVariationIdToUse) {
      console.error(`${logPrefix} Required Plan Variation ID Env Var is missing.`);
      await subscriptionRef.update({
        status: 'failed',
        error_message: 'Missing plan configuration',
        updated_at: Timestamp.now(),
      });
      throw new Error('Required Square Subscription Plan Variation ID is not configured.');
    }

    if (!cardId) {
      console.error(`${logPrefix} Cannot create subscription without a valid card ID.`);
      throw new Error('Card creation failed, cannot proceed with subscription.');
    }

    const subscriptionParams: any = {
      idempotencyKey: crypto.randomUUID(),
      locationId: locationId as string, // Cast locationId
      planVariationId: planVariationIdToUse,
      customerId: customerId as string, // Cast customerId
      cardId: cardId, // Use the validated cardId
      timezone: 'America/Los_Angeles',
      startDate: startDate, // Use calculated start date
      phases: subscriptionPhases,
      source: { name: 'My Very Mulberry Subscription' },
      // ...(zoneEndDate && { canceledDate: zoneEndDate }),
    };

    // console.info(`${logPrefix} Creating subscription with params...`); // Less noise
    const response = await client.subscriptionsApi.createSubscription(subscriptionParams);

    if (!response.result?.subscription?.id) {
      console.error(
        `${logPrefix} Failed to create subscription. Status: ${response.statusCode}. Body:`,
        JSONBigInt.stringify(response)
      );
      await subscriptionRef.update({
        status: 'failed',
        error_message: 'Failed to create Square subscription',
        square_api_response: JSON.parse(JSONBigInt.stringify(response)),
        updated_at: Timestamp.now(),
      });
      throw new Error('Failed to create subscription');
    }
    const squareSubscriptionId = response.result.subscription.id;
    // console.info(`${logPrefix} Subscription created successfully: ${squareSubscriptionId}`); // Less noise

    // --- Final Firestore Update ---
    const firestoreUpdateData: Record<string, any> = {
      square_subscription_id: squareSubscriptionId,
      order_template_id: regularOrderTemplateId,
      status: 'active',
      updated_at: Timestamp.now(),
      square_subscription_response: JSON.parse(JSONBigInt.stringify(response.result)),
      error_message: null,
    };
    if (discountedOrderTemplateId) {
      firestoreUpdateData.discounted_order_template_id = discountedOrderTemplateId;
    }
    await subscriptionRef.update(firestoreUpdateData);
    // console.info(`${logPrefix} Firestore subscription record updated with Square IDs.`); // Less noise

    // --- Send Confirmation Email ---
    try {
      // --- Re-validate discount details for email ---
      let discountDetailsForEmail: ItemDiscountValidationResult | null = null;
      if (validatedData.discountId && validatedData.variationId) {
        try {
          discountDetailsForEmail = await validateDiscountService(
            '',
            validatedData.variationId,
            validatedData.discountId
          );
          if (!discountDetailsForEmail?.isValid) {
            console.warn(
              `[Create Sub API] Re-validation failed for discount ID: ${validatedData.discountId}. Proceeding without discount details in email.`
            );
            discountDetailsForEmail = null; // Reset on failure
          }
        } catch (revalError) {
          console.warn(
            `[Create Sub API] Error during discount re-validation: ${revalError}. Proceeding without discount details in email.`
          );
          discountDetailsForEmail = null; // Reset on error
        }
      }
      // --- End Re-validation ---

      // --- Record discount redemption if applicable ---
      if (validatedDiscountDetails && discountId) {
        try {
          const discountAmount = (discountDetailsForEmail?.discountAmount ?? 0) / 100;
          await recordDiscountRedemption(
            validatedDiscountDetails.codeName,
            discountAmount,
            email,
            customerId,
            undefined, // No order ID for subscriptions
            squareSubscriptionId
          );
        } catch (discountRecordError) {
          console.warn(
            `${logPrefix} Failed to record discount redemption:`,
            discountRecordError instanceof Error
              ? discountRecordError.message
              : String(discountRecordError)
          );
          // Non-blocking error - continue with the subscription process
        }
      }

      // Retrieve the correct order template details for the email (discounted if applied, otherwise regular)
      const templateIdForEmail = discountedOrderTemplateId ?? regularOrderTemplateId;
      const orderTemplateForEmailResponse = templateIdForEmail
        ? await client.ordersApi.retrieveOrder(templateIdForEmail).catch(e => {
            console.warn(
              `${logPrefix} Failed to retrieve order template ${templateIdForEmail} for email:`,
              (e as any).body || (e as Error).message
            );
            return null;
          })
        : null;

      if (orderTemplateForEmailResponse?.result?.order) {
        const orderDataForEmail = getSubscriptionDetailsFromOrder(
          orderTemplateForEmailResponse.result.order as SquareOrder
        );
        const deliveryAddress = addressLine2
          ? `${addressLine1}, ${addressLine2}, ${city}, ${state} ${zipCode}`
          : `${addressLine1}, ${city}, ${state} ${zipCode}`;

        let variationNameForEmail = 'Selected Pack';
        if (variationId) {
          try {
            const variationObj = await client.catalogApi.retrieveCatalogObject(variationId);
            variationNameForEmail =
              variationObj.result?.object?.itemVariationData?.name ?? variationNameForEmail;
          } catch {
            /* Ignore */
          }
        }

        if (isGift) {
          if (recipientEmail) {
            await sendGiftRecipientEmail({
              gifter_name: displayName,
              gifter_email: email,
              recipient_name: recipientDisplayName,
              recipient_email: recipientEmail,
              delivery_address: deliveryAddress,
              delivery_notes: deliveryNotes || '',
              gift_message: recipientGiftMessage || '',
              isOneTime,
            });
            await sendGiftGiverEmail({
              gifter_name: displayName,
              gifter_email: email,
              recipient_name: recipientDisplayName,
              recipient_email: recipientEmail,
              isOneTime,
            });
          }
        } else {
          await sendSubscriptionConfirmation({
            referralApplied: !!referralPolicyInformation,
            subscriptionId: squareSubscriptionId,
            customerEmail: email,
            customerName: displayName,
            customerPhone: phone as string,
            deliveryAddress,
            productName: `${productItemData?.name ?? 'Product'} - ${variationNameForEmail}`,
            totalAmount: orderDataForEmail.total,
            productPrice: orderDataForEmail.productPrice,
            deliveryFee: referralPolicyInformation
              ? orderDataForEmail.originalDeliveryFee
              : orderDataForEmail.deliveryFee,
            quantity: quantity,
            discountAmount: referralPolicyInformation
              ? orderDataForEmail.totalDiscount
              : discountDetailsForEmail?.discountAmount,
            firstCycleTotal: discountDetailsForEmail?.firstCycleTotalPreview,
            discountType: discountDetailsForEmail?.type,
            discountValue: discountDetailsForEmail?.value,
            discountCodeName: discountDetailsForEmail?.discountCodeName,
            targetType: discountDetailsForEmail?.targetType,
            originalProductPrice: discountDetailsForEmail?.originalProductPrice,
            originalDeliveryFeePrice: discountDetailsForEmail?.originalDeliveryFeePrice,
            originalTotalAmount: orderDataForEmail.originalTotalAmount,
            isOneTime,
          });
        }
        // console.info(`${logPrefix} Confirmation email sent successfully.`); // Less noise
      } else {
        console.error(
          `${logPrefix} Could not retrieve order details (Template ID: ${templateIdForEmail}) for confirmation email.`
        );
      }
    } catch (emailError: unknown) {
      // Use unknown
      console.error(
        `${logPrefix} Error sending confirmation email:`,
        emailError instanceof Error ? emailError.message : String(emailError)
      );
    }

    // --- Success Response ---
    return NextResponse.json(
      {
        subscriptionId: subscriptionRef.id,
        squareSubscriptionId: squareSubscriptionId,
      },
      { status: 200 }
    );
  } catch (error: unknown) {
    // Outer catch with unknown
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error during subscription creation';
    const errorBody = (error as any)?.body; // Attempt to get body for Square errors
    const statusCode = (error as any)?.statusCode || 500; // Attempt to get status code
    console.error(`${logPrefix} Error in POST handler:`, errorMessage);
    if (errorBody)
      console.error(`${logPrefix} Square Error Details:`, JSONBigInt.stringify(errorBody));
    if (subscriptionRef?.id) {
      await subscriptionRef
        .update({ status: 'failed', error_message: errorMessage, updated_at: Timestamp.now() })
        .catch(fsError => console.error('Failed to update Firestore on error:', fsError));
    }
    return NextResponse.json(
      { error: errorMessage || 'Failed to create subscription' },
      { status: statusCode }
    );
  }
}

const createDiscountCodeOrderTemplate = async (
  subscriptionRef: FirebaseFirestore.DocumentReference,
  baseLineItems: any[],
  productLineItemUid: string,
  deliveryLineItemUid: string,
  locationId: string,
  customerId: string,
  targetType: string,
  validatedDiscountDetails: Awaited<ReturnType<typeof fetchDiscountDetails>>,
  fulfillmentDetails: any,
  recipientGiftMessage: string | undefined,
  zoneId: string,
  orderFor: string,
  isGift: boolean,
  displayName: string,
  logPrefix: string
) => {
  const discountUid = `disc-${subscriptionRef.id}`;
  const discountedLineItems = baseLineItems.map(item => {
    const isTarget =
      (targetType === 'PRODUCT' && item.uid === productLineItemUid) ||
      (targetType === 'DELIVERY_FEE' && item.uid === deliveryLineItemUid);
    return { ...item, appliedDiscounts: isTarget ? [{ discountUid }] : undefined };
  });

  const discountedTemplateParams = {
    idempotencyKey: crypto.randomUUID(),
    order: {
      locationId,
      customerId: customerId as string,
      state: 'DRAFT',
      source: { name: 'My Very Mulberry Home Delivery' },
      lineItems: discountedLineItems,
      discounts: [
        {
          uid: discountUid,
          catalogObjectId: validatedDiscountDetails?.id,
          scope: 'LINE_ITEM',
        },
      ],
      fulfillments: [fulfillmentDetails],
      metadata: {
        ...(recipientGiftMessage && { gift_message: recipientGiftMessage }),
        zoneId,
        orderFor,
        ...(isGift && { giftBy: displayName }),
      },
    },
  };
  const discountedOrderTemplateResponse =
    await client.ordersApi.createOrder(discountedTemplateParams);
  if (!discountedOrderTemplateResponse.result?.order?.id) {
    console.error(
      `${logPrefix} Failed to create discounted order template.`,
      JSONBigInt.stringify(discountedOrderTemplateResponse)
    );
    throw new Error('Failed to create discounted order template');
  }

  return discountedOrderTemplateResponse.result.order.id;
};

const createReferralCodeOrderTemplate = async (
  baseLineItems: any[],
  locationId: string,
  customerId: string,
  fulfillmentDetails: any,
  recipientGiftMessage: string | undefined,
  zoneId: string,
  orderFor: string,
  isGift: boolean,
  displayName: string,
  logPrefix: string,
  referralPolicyInformation: DocumentData,
  referralCode: string,
  referralPolicyId: string
) => {
  const referralTemplateParams = {
    idempotencyKey: crypto.randomUUID(),
    order: {
      locationId,
      customerId: customerId as string,
      state: 'DRAFT',
      lineItems: baseLineItems,
      source: { name: 'My Very Mulberry Home Delivery' },
      discounts: [
        {
          type:
            referralPolicyInformation.referee_benefit_type === 'PERCENTAGE'
              ? 'FIXED_PERCENTAGE'
              : 'FIXED_AMOUNT',
          name: 'Referral discount',
          scope: 'ORDER',
          ...(referralPolicyInformation.referee_benefit_type === 'PERCENTAGE' && {
            percentage: String(referralPolicyInformation.referee_benefit_value),
          }),
          ...(referralPolicyInformation.referee_benefit_type === 'FIXED_AMOUNT' && {
            amountMoney: {
              amount: BigInt(referralPolicyInformation.referee_benefit_value * 100),
              currency: 'USD',
            },
          }),
        },
      ],
      fulfillments: [fulfillmentDetails],
      metadata: {
        ...(recipientGiftMessage && { gift_message: recipientGiftMessage }),
        zoneId,
        orderFor,
        ...(isGift && { giftBy: displayName }),
        ...(referralPolicyInformation && { referralCode, referralPolicyId }),
      },
    },
  };
  const referralOrderTemplateResponse = await client.ordersApi.createOrder(referralTemplateParams);
  if (!referralOrderTemplateResponse.result?.order?.id) {
    console.error(
      `${logPrefix} Failed to create referral order template.`,
      JSONBigInt.stringify(referralOrderTemplateResponse)
    );
    throw new Error('Failed to create referral order template');
  }

  return referralOrderTemplateResponse.result.order.id;
};
