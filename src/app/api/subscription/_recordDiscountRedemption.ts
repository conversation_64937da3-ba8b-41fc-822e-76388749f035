import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { DiscountRedemptions } from '@/src/types/DiscountCodes';

/**
 * Records a discount code redemption in Firestore
 *
 * @param discountCode The discount code that was used
 * @param squareCustomerId The Square customer ID who used the discount
 * @param orderId The order ID associated with this redemption
 * @param amountSaved The amount saved in dollars
 * @returns Promise<boolean> indicating success or failure
 */
export async function recordDiscountRedemption(
  discountCode: string,
  amountSaved: number,
  email: string,
  squareCustomerId?: string,
  orderId?: string,
  subscriptionId?: string
): Promise<boolean> {
  try {
    const discountRef = adminDb.collection(COLLECTIONS.DISCOUNT_CODES).doc(discountCode);
    const discountDoc = await discountRef.get();

    if (!discountDoc.exists || !discountDoc.data()?.active) {
      console.warn(
        `[Record Redemption] Attempted to record redemption for invalid code: ${discountCode}`
      );
      return false;
    }

    const redemptionData: DiscountRedemptions = {
      square_customer_id: squareCustomerId,
      redeemed_at: getPacificTimeNow(),
      email: email,
      amount_saved: amountSaved,
    };

    if (orderId) {
      redemptionData.order_id = orderId;
    } else {
      redemptionData.subscription_id = subscriptionId;
    }

    await discountRef.collection(COLLECTIONS.DISCOUNT_REDEMPTIONS).add(redemptionData);

    return true;
  } catch (error) {
    console.error(`[Record Redemption] Error recording redemption for ${discountCode}:`, error);
    return false;
  }
}
