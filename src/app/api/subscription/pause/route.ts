import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';
import { pauseSubscriptionSchema } from '@/src/schema/subscription.zod';

import { client, JSONBigInt } from '../../square/_squareConfig';

/**
 * Pauses a user's subscription in Square one cycle
 *
 * @route POST /api/subscription/pause
 * @param req The incoming request with subscriptionId in the request body
 * @returns Result of the pause operation
 */
export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const requestData = await req.json();

    // Validate request data
    const validationResult = pauseSubscriptionSchema.safeParse(requestData);

    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors[0]?.message || 'Invalid request data';
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    // Use validated data
    const { subscriptionId, firebase_user_id } = validationResult.data;

    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Verify the authenticated user is the same as the user whose subscription is being paused
    if (authResult.decodedToken.uid !== firebase_user_id) {
      return NextResponse.json({ error: 'Unauthorized access' }, { status: 401 });
    }

    // Call Square API to pause the subscription
    try {
      const response = await client.subscriptionsApi.pauseSubscription(subscriptionId, {
        pauseCycleDuration: BigInt(1),
        pauseReason: 'Skipping one billing cycle as requested by the customer.',
      });

      if (!response.result || !response.result.subscription) {
        throw new Error('Failed to pause subscription');
      }

      // Update Firestore with the new subscription response
      try {
        // Find the subscription document by square_subscription_id
        const subscriptionsRef = adminDb.collection(COLLECTIONS.SUBSCRIPTIONS);
        const querySnapshot = await subscriptionsRef
          .where('square_subscription_id', '==', subscriptionId)
          .get();

        if (!querySnapshot.empty) {
          const subscriptionDoc = querySnapshot.docs[0];
          await subscriptionDoc.ref.update({
            square_subscription_response: JSON.parse(JSONBigInt.stringify(response.result)),
            updated_at: Timestamp.now(),
          });
        }
      } catch (firestoreError) {
        console.error('Error updating Firestore:', firestoreError);
        // Continue with the response even if Firestore update fails
      }

      return NextResponse.json({
        success: true,
        message: 'Subscription paused successfully',
      });
    } catch (squareError: any) {
      // Check for specific Square API errors
      if (squareError.errors && squareError.errors.length > 0) {
        const error = squareError.errors[0];

        // Handle case where subscription already has a pending pause date
        if (error.detail && error.detail.includes('already has a pending pause date')) {
          return NextResponse.json(
            {
              error: 'This subscription is already scheduled to be paused',
              details: error.detail,
            },
            { status: 400 }
          );
        }

        // Return the original error
        return NextResponse.json(
          {
            error: 'Failed to pause subscription',
            details: error.detail,
            errors: squareError.errors,
          },
          { status: 400 }
        );
      }

      // Re-throw for the outer catch block
      throw squareError;
    }
  } catch (error) {
    console.error('Error pausing subscription:', error);
    return NextResponse.json(
      {
        error: 'Failed to pause subscription',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
