import jwt from 'jsonwebtoken';
import { NextRequest, NextResponse } from 'next/server';

import { ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function GET(req: NextRequest) {
  try {
    // App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const { searchParams } = new URL(req.url);
    const token = searchParams.get('token');
    const ticketId = searchParams.get('ticketId');

    if (!token || !ticketId) {
      return NextResponse.json({ error: 'Token and ticketId are required' }, { status: 400 });
    }

    try {
      // Verify token with your secret key
      const decoded = jwt.verify(token, process.env.NEXT_JWT_SECRET_KEY as string);

      // Check if token is expired (48 hours)
      const tokenData = decoded as { ticketId: string; iat: number; exp: number };
      const now = getPacificTimeNow().getTime();

      // JWT tokens have built-in expiration (exp) field
      // No need to manually calculate expiration time
      if (tokenData.exp && now > tokenData.exp * 1000) {
        return NextResponse.json({ error: 'Token expired' }, { status: 401 });
      }

      // Check if token is for the requested ticket
      if (tokenData.ticketId !== ticketId) {
        return NextResponse.json(
          { error: 'You are not authorized for this ticket' },
          { status: 401 }
        );
      }

      return NextResponse.json({ valid: true });
    } catch (_error) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
  } catch (error) {
    console.error('Error verifying token:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.GENERIC_ERROR }, { status: 500 });
  }
}
