import { Timestamp as ClientTimestamp } from 'firebase/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { FarmerMarket } from '@/src/types/FarmerMarket';

/**
 * Helper function to convert Admin Firestore Timestamp to client-compatible Timestamp
 * @param adminTimestamp The Admin Firestore timestamp to convert
 * @returns A client-compatible Timestamp object
 */
function convertTimestamp(adminTimestamp: any): ClientTimestamp | null {
  if (!adminTimestamp) return null;
  if (adminTimestamp.seconds !== undefined && adminTimestamp.nanoseconds !== undefined) {
    return new ClientTimestamp(adminTimestamp.seconds, adminTimestamp.nanoseconds);
  }
  return null;
}

/**
 * Converts all Timestamp fields in an object to client-compatible Timestamps
 * @param data The object containing potential Timestamp fields
 * @returns A new object with converted Timestamp fields
 */
function convertTimestampFields(data: any): any {
  if (!data) return data;

  const result = { ...data };
  for (const key in result) {
    if (result[key] && typeof result[key] === 'object') {
      if (result[key].seconds !== undefined && result[key].nanoseconds !== undefined) {
        result[key] = convertTimestamp(result[key]);
      } else if (Array.isArray(result[key])) {
        result[key] = result[key].map((item: any) =>
          typeof item === 'object' ? convertTimestampFields(item) : item
        );
      } else {
        result[key] = convertTimestampFields(result[key]);
      }
    }
  }
  return result;
}

export async function GET(req: NextRequest) {
  try {
    const isAppCheckTokenValid = await verifyAppCheckToken(req);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Query active and non-deleted farmer markets using Admin SDK
    const farmerMarketsQuery = adminDb
      .collection(COLLECTIONS.FARMER_MARKETS)
      .where('deleted_at', '==', null)
      .where('is_active', '==', true);

    const farmerMarketsSnapshot = await farmerMarketsQuery.get();

    if (farmerMarketsSnapshot.empty) {
      return NextResponse.json({
        message: SUCCESS_MESSAGES.GET_FARMER_MARKETS,
        data: [],
      });
    }

    // Transform farmer markets data with timestamp conversion
    const farmerMarkets = farmerMarketsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...convertTimestampFields(doc.data()),
    })) as FarmerMarket[];

    return NextResponse.json({
      message: SUCCESS_MESSAGES.GET_FARMER_MARKETS,
      data: farmerMarkets,
    });
  } catch (err: any) {
    console.error('Error fetching farmer markets:', err);
    return NextResponse.json(
      { error: err.message || ERROR_MESSAGES.FAILED_TO_FETCH_FARMER_MARKETS },
      { status: 500 }
    );
  }
}
