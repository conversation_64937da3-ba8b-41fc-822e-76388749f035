import { getStorage } from 'firebase-admin/storage';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { verifyAuthToken } from '@/src/middleware/auth';
import { updateProfileSchema } from '@/src/schema/profile.zod';

/**
 * Profile Update API Endpoint
 *
 * This endpoint allows users to update their profile information including personal details
 * and profile image. It handles form data with potential file uploads and updates the user
 * document in Firestore. Only authenticated users can update their own profiles, with an
 * exception for admin users who can update any profile.
 *
 * @route PUT /api/users/update-profile
 * @param {Object} req - The request object containing form data with user profile information
 * @returns {Object} JSON response with success status and updated user data
 * @throws Will return error responses for authentication failures, validation errors, or database issues
 */
export async function PUT(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    // Verify authentication token
    const authResult = await verifyAuthToken(req);
    if ('error' in authResult) {
      // Return error if authentication fails
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Get authenticated user's ID from the token for authorization checks
    const authenticatedUserId = authResult.decodedToken.uid;

    // Handle multipart form data
    const formData = await req.formData();

    // Extract relevant fields from the form data
    const rawFormData = {
      userId: formData.get('userId'), // ID of the user to update
      phone: formData.get('phone'), // Phone number
      firstName: formData.get('firstName'), // First name
      lastName: formData.get('lastName'), // Last name
      profileImage: formData.get('profileImage'), // Profile image file
      marketingConsent: formData.get('marketingConsent'), // Marketing consent flag
    };

    // Validate the form data against the schema
    const validationResult = updateProfileSchema.safeParse(rawFormData);

    if (!validationResult.success) {
      // Return first validation error if validation fails
      const errorMessage = validationResult.error.errors[0]?.message || 'Invalid form data';
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    // Extract validated data for use in the function
    const { userId, phone, firstName, lastName, profileImage, marketingConsent } =
      validationResult.data;

    const userRef = adminDb.collection(COLLECTIONS.USERS).doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      // Return error if user doesn't exist
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user already has a profile image
    const userData = userDoc.data();

    // Check if the authenticated user is the same as the user being updated
    // or if the authenticated user has admin role
    if (userData?.firebase_user_id !== authenticatedUserId) {
      // If not the same user, check if the authenticated user is an admin
      const adminSnapshot = await adminDb
        .collection(COLLECTIONS.USERS)
        .where('firebase_user_id', '==', authenticatedUserId)
        .where('role', '==', 'admin')
        .get();

      if (adminSnapshot.empty) {
        // Return error if not admin and not updating own profile
        return NextResponse.json({ error: 'Unauthorized user.' }, { status: 401 });
      }
    }

    const updates: any = {};

    if (phone) updates.phone = phone;
    if (firstName) updates.first_name = firstName;
    if (lastName) updates.last_name = lastName;
    if (marketingConsent !== null) updates.marketing_consent = marketingConsent === 'true';

    // Handle profile image upload
    if (profileImage) {
      // Get admin storage instance
      const adminStorage = getStorage();
      const bucket = adminStorage.bucket(process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET);

      // Create consistent file path regardless of extension
      const fileExtension = profileImage.name.split('.').pop();
      const imagePath = `profiles/${userId}.${fileExtension}`;

      if (userData?.profile_url) {
        try {
          // Extract the path from the existing profile URL
          const urlPath = userData.profile_url.split(
            `https://storage.cloud.google.com/${process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET}/`
          )[1];
          if (urlPath && urlPath !== imagePath) {
            // Delete the old file if it exists and has a different path
            await bucket
              .file(urlPath)
              .delete()
              .catch(() => {
                // Ignore errors if file doesn't exist
                console.error('Previous profile image not found or already deleted');
              });
          }
        } catch (err) {
          console.error('Error deleting previous profile image:', err);
          // Continue with upload even if delete fails
        }
      }

      const file = bucket.file(imagePath);

      // Convert File to buffer
      const arrayBuffer = await profileImage.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Upload to Firebase Storage with public access
      await file.save(buffer, {
        metadata: {
          contentType: profileImage.type,
          metadata: {
            uploadedBy: userId,
            uploadedAt: new Date().toISOString(),
          },
        },
        public: true,
      });

      // Get public URL for the image
      const publicUrl = `https://storage.cloud.google.com/${process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET}/${imagePath}`;

      // Add profile URL to updates
      updates.profile_url = publicUrl;
    }

    await userRef.update(updates);

    return NextResponse.json({
      success: true,
      data: { userId, ...updates },
    });
  } catch (error: any) {
    console.error('Profile update error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update user profile' },
      { status: 500 }
    );
  }
}
