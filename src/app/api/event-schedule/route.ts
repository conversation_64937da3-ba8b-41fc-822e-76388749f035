import { Timestamp as ClientTimestamp } from 'firebase/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/src/lib/constants';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';

/**
 * Helper function to convert Admin Firestore Timestamp to client-compatible Timestamp
 * @param adminTimestamp The Admin Firestore timestamp to convert
 * @returns A client-compatible Timestamp object
 */
function convertTimestamp(adminTimestamp: any): ClientTimestamp | null {
  if (!adminTimestamp) return null;
  if (adminTimestamp.seconds !== undefined && adminTimestamp.nanoseconds !== undefined) {
    return new ClientTimestamp(adminTimestamp.seconds, adminTimestamp.nanoseconds);
  }
  return null;
}

/**
 * Converts all Timestamp fields in an object to client-compatible Timestamps
 * @param data The object containing potential Timestamp fields
 * @returns A new object with converted Timestamp fields
 */
function convertTimestampFields(data: any): any {
  if (!data) return data;

  const result = { ...data };
  for (const key in result) {
    if (result[key] && typeof result[key] === 'object') {
      if (result[key].seconds !== undefined && result[key].nanoseconds !== undefined) {
        result[key] = convertTimestamp(result[key]);
      } else if (Array.isArray(result[key])) {
        result[key] = result[key].map((item: any) =>
          typeof item === 'object' ? convertTimestampFields(item) : item
        );
      } else {
        result[key] = convertTimestampFields(result[key]);
      }
    }
  }
  return result;
}

export async function GET(request: NextRequest) {
  try {
    //  App Check
    const isAppCheckTokenValid = await verifyAppCheckToken(request);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }
    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');
    const scheduleId = searchParams.get('scheduleId');

    if (!eventId || !scheduleId) {
      return NextResponse.json({ error: 'Event ID and Schedule ID are required' }, { status: 400 });
    }

    // Get event document reference using Admin SDK
    const eventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(eventId);
    const eventDoc = await eventRef.get();

    if (!eventDoc.exists) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Get schedule document reference using Admin SDK
    const scheduleRef = adminDb
      .collection(COLLECTIONS.EVENTS)
      .doc(eventId)
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(scheduleId);
    const scheduleDoc = await scheduleRef.get();

    if (!scheduleDoc.exists) {
      return NextResponse.json({ error: 'Schedule not found' }, { status: 404 });
    }

    // Convert any Timestamp fields to client-compatible Timestamps
    const eventData = convertTimestampFields(eventDoc.data());
    const scheduleData = convertTimestampFields(scheduleDoc.data());

    return NextResponse.json({
      message: SUCCESS_MESSAGES.GET_EVENT_SCHEDULE,
      data: {
        event: { id: eventDoc.id, ...eventData } as Events,
        schedule: { id: scheduleDoc.id, ...scheduleData } as EventSchedules,
      },
    });
  } catch (error) {
    console.error('Error fetching event schedule:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.FAILED_TO_FETCH_EVENTS }, { status: 500 });
  }
}
