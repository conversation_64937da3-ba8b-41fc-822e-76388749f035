import { Timestamp } from 'firebase-admin/firestore';
import { NextRequest, NextResponse } from 'next/server';

import { adminDb } from '@/src/app/api/_firebaseAdmin';
import { COLLECTIONS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { verifyAppCheckToken } from '@/src/middleware/appCheck';

export async function POST(req: NextRequest) {
  try {
    //  App Check with replay protection
    const isAppCheckTokenValid = await verifyAppCheckToken(req, true);
    if (isAppCheckTokenValid) {
      return NextResponse.json({ error: ERROR_MESSAGES.INVALID_REQUEST }, { status: 400 });
    }

    const { ticketId, passcode, selectedGuestIds } = await req.json();

    if (!ticketId || !passcode) {
      return NextResponse.json(
        { error: 'Please provide both ticket ID and check-in code' },
        { status: 400 }
      );
    }

    // Get main ticket details
    const ticketRef = adminDb.collection(COLLECTIONS.TICKETS).doc(ticketId);
    const ticketDoc = await ticketRef.get();

    if (!ticketDoc.exists) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    const ticketData = ticketDoc.data();

    // Check if main ticket is already checked in
    if (ticketData?.status === 'checked_in') {
      return NextResponse.json({ error: ERROR_MESSAGES.ALREADY_CHECKED_IN }, { status: 400 });
    }

    // Get event schedule to verify passcode
    const eventRef = adminDb.collection(COLLECTIONS.EVENTS).doc(ticketData?.event_id.id);
    const scheduleRef = eventRef
      .collection(COLLECTIONS.EVENT_SCHEDULES)
      .doc(ticketData?.event_schedule_id.id);
    const scheduleDoc = await scheduleRef.get();

    if (!scheduleDoc.exists) {
      return NextResponse.json({ error: 'Schedule not found' }, { status: 404 });
    }

    const scheduleData = scheduleDoc.data();

    // Check event date
    const currentDate = getPacificTimeNow();
    const eventDate = new Date(scheduleData?.event_date);

    // Set both dates to start of day for comparison
    currentDate.setHours(0, 0, 0, 0);
    eventDate.setHours(0, 0, 0, 0);

    if (currentDate < eventDate) {
      return NextResponse.json({ error: ERROR_MESSAGES.CHECK_IN_TOO_EARLY }, { status: 400 });
    }

    if (currentDate > eventDate) {
      return NextResponse.json({ error: 'You are trying to Check-In too late.' }, { status: 400 });
    }

    // Check if user is trying to check in too early (more than 30 min before start time)
    if (scheduleData?.start_time) {
      const currentTime = getPacificTimeNow();
      const eventDate = new Date(scheduleData.event_date);
      const [hours, minutes] = scheduleData.start_time.split(':').map(Number);
      const eventStartTime = new Date(eventDate);
      eventStartTime.setHours(hours, minutes, 0, 0);

      // Calculate time difference in minutes
      const timeDiffInMinutes = (eventStartTime.getTime() - currentTime.getTime()) / (1000 * 60);

      // If trying to check in more than 30 minutes before start time
      if (timeDiffInMinutes > 30) {
        return NextResponse.json(
          { error: 'Check-in is only available 30 minutes before the event starts' },
          { status: 400 }
        );
      }
    }

    // Verify passcode
    if (scheduleData?.check_in_passcode !== passcode) {
      return NextResponse.json(
        { error: 'Invalid check-in code. Please try again.' },
        { status: 400 }
      );
    }

    // Get all guest tickets for the order
    const orderRef = ticketData?.order_id;
    const ticketsQuery = adminDb.collection(COLLECTIONS.TICKETS).where('order_id', '==', orderRef);
    const ticketsSnapshot = await ticketsQuery.get();

    // Prepare batch write
    const batch = adminDb.batch();

    // Update main ticket
    batch.update(ticketRef, {
      status: 'checked_in',
      check_in: {
        method: 'passcode',
        timestamp: Timestamp.now(),
        checked_in_at: Timestamp.now(),
      },
    });

    // Update selected guest tickets
    if (selectedGuestIds && selectedGuestIds.length > 0) {
      ticketsSnapshot.forEach(doc => {
        const guestIndex = selectedGuestIds.indexOf(doc.id);
        if (guestIndex !== -1) {
          batch.update(doc.ref, {
            status: 'checked_in',
            check_in: {
              method: 'group',
              timestamp: Timestamp.now(),
              checked_in_at: Timestamp.now(),
              checked_in_with: ticketId,
            },
          });
        }
      });
    }

    // Commit all updates
    await batch.commit();

    return NextResponse.json({
      success: true,
      message: 'Check-in successful',
    });
  } catch (error: any) {
    console.error('Check-in error:', error);
    return NextResponse.json({ error: ERROR_MESSAGES.INVALID_CHECK_IN }, { status: 500 });
  }
}
