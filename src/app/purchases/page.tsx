'use client';

import { ShoppingBag, Ticket } from 'lucide-react';
import React, { useEffect, useMemo } from 'react';

import Spinner from '@/src/components/spinner';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/src/components/ui/tabs';
import withAuth from '@/src/features/auth/hoc/withAuth';
import { selectUser } from '@/src/features/auth/slices/authenticationSlice';
import { fetchUserOrders } from '@/src/features/u-pick/slices/guestsSlice';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';
import { Orders } from '@/src/types/Orders';

const PurchasesPage = () => {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const { orders, loading, error } = useAppSelector((state: RootState) => state.guests);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Fetch orders when user data is available
  useEffect(() => {
    if (user?.id) {
      dispatch(fetchUserOrders({ userId: user?.id, firebaseId: user?.firebase_user_id }));
    }
  }, [dispatch, user?.firebase_user_id, user?.id]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  // Separate orders by item_type
  const ticketOrders = useMemo(
    () => orders.filter(order => order.item_type === 'ticket'),
    [orders]
  );

  const productOrders = useMemo(
    () => orders.filter(order => order.item_type === 'product'),
    [orders]
  );
  /**
   * End Custom Methods
   */

  if (loading) {
    return <Spinner />;
  }

  const renderOrderCard = (order: Orders) => (
    <div
      key={order.id}
      className="border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow bg-white"
    >
      {/* Header with order ID and status */}
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold text-base">
          Order #{order.id ? order.id.slice(0, 8) : 'N/A'}
        </h3>
        <span
          className={`text-xs font-medium px-2.5 py-1 rounded-full ${
            order.status === 'pending'
              ? 'bg-green-100 text-green-800'
              : order.status === 'completed'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-mulberry/10 text-mulberry'
          }`}
        >
          {order.status}
        </span>
      </div>

      {/* Order date and total amount */}
      <div className="flex justify-between items-center mb-3">
        <div className="text-sm text-muted-foreground">
          {order.created_at &&
            new Date(order.created_at.seconds * 1000).toLocaleDateString(undefined, {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })}
        </div>
        <div className="text-base font-medium text-mulberry">
          ${order.total_amount?.toFixed(2) || '0.00'}
        </div>
      </div>

      {/* Order type and quantity */}
      <div className="flex gap-4 mb-3">
        {order.item_type && (
          <div className="text-sm flex items-center gap-1">
            {order.item_type === 'ticket' ? (
              <Ticket className="h-4 w-4" />
            ) : (
              <ShoppingBag className="h-4 w-4" />
            )}
            <span>{order.item_type === 'ticket' ? 'Event Ticket' : 'Product'}</span>
          </div>
        )}
        {order.quantity && (
          <div className="text-sm">
            Qty: <span className="font-medium">{order.quantity}</span>
          </div>
        )}
      </div>

      {/* Product details section */}
      {order.item_type === 'product' && Array.isArray(order.item) && order.item.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-100">
          <h4 className="font-medium text-sm mb-2 text-gray-700">Order Details</h4>
          <div className="space-y-3">
            {order.item.map((product, index) => (
              <div key={index} className="bg-gray-50 rounded-md p-3 text-sm">
                <div className="flex justify-between items-start mb-1">
                  <div className="font-medium">{product.name}</div>
                  {product.basePriceMoney && (
                    <div className="font-medium">
                      ${(Number(product.basePriceMoney.amount) / 100).toFixed(2)}
                    </div>
                  )}
                </div>
                <div className="flex justify-between text-gray-600">
                  {product.variationName && <div>{product.variationName}</div>}
                  <div>Qty: {product.quantity}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <>
      <div className="text-left space-y-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <h2 className="text-xl md:text-2xl font-bold text-start mb-4 ">My Purchases</h2>
        </div>

        {error && <div className="text-red-500">{error}</div>}

        {orders && orders.length > 0 ? (
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Orders ({orders.length})</TabsTrigger>
              <TabsTrigger value="tickets">
                <Ticket className="h-4 w-4 mr-1" />
                Tickets ({ticketOrders.length})
              </TabsTrigger>
              <TabsTrigger value="products">
                <ShoppingBag className="h-4 w-4 mr-1" />
                Products ({productOrders.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              {orders.map(renderOrderCard)}
            </TabsContent>

            <TabsContent value="tickets" className="space-y-4">
              {ticketOrders.length > 0 ? (
                ticketOrders.map(renderOrderCard)
              ) : (
                <p className="text-muted-foreground">You haven&apos;t purchased any tickets yet.</p>
              )}
            </TabsContent>

            <TabsContent value="products" className="space-y-4">
              {productOrders.length > 0 ? (
                productOrders.map(renderOrderCard)
              ) : (
                <p className="text-muted-foreground">
                  You haven&apos;t purchased any products yet.
                </p>
              )}
            </TabsContent>
          </Tabs>
        ) : (
          <p className="text-muted-foreground">
            You don&apos;t have any purchases yet. Once you make a purchase, it will appear here.
          </p>
        )}
      </div>
    </>
  );
};

export default withAuth(PurchasesPage);
