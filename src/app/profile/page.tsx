'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import { Edit2 } from 'lucide-react';
import React, { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { CustomPhoneInput } from '@/src/components/phone-input';
import ProfileImageModal from '@/src/components/ProfileImageModal';
import Spinner from '@/src/components/spinner';
import { Avatar, AvatarFallback, AvatarImage } from '@/src/components/ui/avatar';
import { Button } from '@/src/components/ui/button';
import { Checkbox } from '@/src/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/src/components/ui/form';
import { Input } from '@/src/components/ui/input';
import withAuth from '@/src/features/auth/hoc/withAuth';
import {
  updateUserProfile,
  setTempProfileImage,
} from '@/src/features/auth/slices/authenticationSlice';
import { VALIDATION_ERROR_MESSAGES } from '@/src/lib/constants';
import { useAppSelector, useAppDispatch, RootState } from '@/src/store';

// Profile form schema
const profileSchema = z.object({
  firstName: z
    .string()
    .min(2, VALIDATION_ERROR_MESSAGES.FIRST_NAME_MIN || 'First name must be at least 2 characters'),
  lastName: z
    .string()
    .min(2, VALIDATION_ERROR_MESSAGES.LAST_NAME_MIN || 'Last name must be at least 2 characters'),
  email: z
    .string()
    .email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID || 'Invalid email format')
    .optional(),
  phone: z
    .string()
    .optional()
    .refine(
      val => {
        if (!val || val === '') return true;
        try {
          const phoneNumber = parsePhoneNumberWithError(val);
          return phoneNumber.isValid();
        } catch {
          return false;
        }
      },
      { message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER || 'Invalid phone number' }
    ),
  marketingConsent: z.boolean().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

const ProfilePage = () => {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();
  const { user, loading } = useAppSelector((state: RootState) => state.authentication);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isProfileImageModalOpen, setIsProfileImageModalOpen] = useState(false);

  // Initialize form with React Hook Form
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      marketingConsent: false,
    },
  });
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Initialize form with user data
  useEffect(() => {
    if (user) {
      form.reset({
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        email: user.email || '',
        phone: user.phone || '',
        marketingConsent: user.marketing_consent || false,
      });
    }
  }, [user, form]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  // Updated to handle both file and preview
  const handleImageSelect = (file: File | null, preview: string | null) => {
    setSelectedFile(file);
    setPreviewUrl(preview);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit');
        return;
      }
      setSelectedFile(file);

      // Create preview URL and update Redux state
      const reader = new FileReader();
      reader.onloadend = () => {
        const previewUrl = reader.result as string;
        dispatch(setTempProfileImage(previewUrl));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleClearAll = () => {
    form.reset({
      firstName: '',
      lastName: '',
      email: user?.email || '', // Keep email since it's disabled
      phone: '',
    });
  };

  const onSubmit = async (data: ProfileFormValues) => {
    try {
      setIsSubmitting(true);
      if (user?.id) {
        // Phone number from CustomPhoneInput will already be in E.164 format (+1XXXXXXXXXX)
        await dispatch(
          updateUserProfile({
            userId: user.id,
            firstName: data.firstName,
            lastName: data.lastName,
            phone: data.phone || '',
            profileImage: selectedFile || undefined,
            marketingConsent: data.marketingConsent,
          })
        ).unwrap();

        // Clear selected file after successful upload
        if (selectedFile) {
          setSelectedFile(null);
          if (fileInputRef.current) fileInputRef.current.value = '';
        }

        toast.success('Profile updated successfully');
      }
    } catch (_error) {
      toast.error('Failed to update profile');
    } finally {
      setIsSubmitting(false);
    }
  };
  /**
   * End Custom Methods
   */

  // Show spinner when loading initial user data
  if (loading && !user) {
    return <Spinner />;
  }

  return (
    <>
      <div className="text-left">
        <div className="flex flex-col items-start mb-4">
          <h2 className="text-2xl font-semibold">My Profile</h2>
          <p className="text-muted-foreground">Manage your personal information and preferences.</p>
        </div>

        <div className="flex flex-col md:flex-row gap-8 items-start">
          <div className="flex flex-col items-center space-y-4 w-full lg:hidden">
            <div className="relative">
              <Avatar className="h-24 w-24 mb-2">
                <AvatarImage src={previewUrl || user?.profile_url} alt="User" />
                <AvatarFallback className="bg-mulberry text-white text-xl font-semibold">
                  {`${user?.first_name?.[0] || ''}${user?.last_name?.[0] || ''}`}
                </AvatarFallback>
              </Avatar>
              <button
                onClick={() => setIsProfileImageModalOpen(true)}
                className="absolute bottom-3 right-0 bg-white p-1.5 rounded-full shadow-md hover:bg-gray-100 transition-colors"
                aria-label="Edit profile picture"
              >
                <Edit2 size={14} className="text-mulberry" />
              </button>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
            />
          </div>
        </div>

        <div className="pt-3">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4 max-w-md">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="First Name" disabled={isSubmitting} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Last Name" disabled={isSubmitting} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Email"
                        disabled={true}
                        className="bg-gray-100"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <CustomPhoneInput
                        {...field}
                        value={field.value || ''}
                        disabled={isSubmitting}
                        className="w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="marketingConsent"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="font-normal cursor-pointer">
                        Send me information about Very Mulberry special offers and events
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <div className="flex justify-start gap-4 mb-6 mt-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClearAll}
                  disabled={isSubmitting}
                >
                  Clear All
                </Button>
                <Button
                  type="submit"
                  className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Saving...
                    </div>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>

      {/* Profile Image Modal */}
      <ProfileImageModal
        isOpen={isProfileImageModalOpen}
        onClose={() => setIsProfileImageModalOpen(false)}
        onImageSelect={handleImageSelect}
        currentPreview={previewUrl}
      />
    </>
  );
};

export default withAuth(ProfilePage);
