'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect } from 'react';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs';
import CombinedSchedule from '@/src/features/combined-schedule';
import FarmerMarket from '@/src/features/farmer-market';
import { fetchFarmerMarkets } from '@/src/features/farmer-market/slices/farmerMarketSlice';
// import HomeDelivery from '@/src/features/home-delivery';
import Schedule from '@/src/features/schedule';
// import { fetchAllDeliveryZones } from '@/src/features/subscription/slices/deliveryZoneSlice';
import { fetchEventSchedule } from '@/src/features/ticket-tailor/slice';
import { SCHEDULE_TABS_NAME, ROUTES, validTabs } from '@/src/lib/constants';
import { useAppDispatch } from '@/src/store';

// Create a client component that uses searchParams
function ScheduleContent() {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab') || SCHEDULE_TABS_NAME.ALL;

  // Use the tab param if valid, otherwise default to 'all'
  const tab = validTabs.includes(tabParam) ? tabParam : SCHEDULE_TABS_NAME.ALL;

  // Redirect to the correct tab if an invalid one was provided
  useEffect(() => {
    if (tabParam !== tab) {
      router.replace(`${ROUTES.SCHEDULE_TABS}${tab}`, { scroll: false });
    }
  }, [tabParam, tab, router]);

  useEffect(() => {
    dispatch(fetchEventSchedule());
    dispatch(fetchFarmerMarkets());
    // dispatch(fetchAllDeliveryZones());
  }, [dispatch]);

  const handleTabChange = (value: string) => {
    router.push(`${ROUTES.SCHEDULE_TABS}${value}`, { scroll: false });
  };

  return (
    <div className="max-w-5xl mx-auto ">
      <h3 className="text-center text-2xl font-bold  text-mulberry">Schedule</h3>
      <p className="text-center text-sm text-gray-600 mb-4">
        Stay updated with our U-Pick events, Farmers&apos; Markets locations.
      </p>
      <Tabs
        defaultValue={tab}
        className="w-full bg-muted rounded-t"
        onValueChange={handleTabChange}
      >
        <div className="">
          <TabsList className="inline-flex w-auto min-w-full sm:w-full px-1">
            <TabsTrigger
              value="all"
              className="data-[state=active]:bg-mulberry font-extrabold data-[state=active]:text-white data-[state=inactive]:text-mulberry flex-1 px-2 sm:px-4"
            >
              All
            </TabsTrigger>
            <TabsTrigger
              value="u-pick"
              className="data-[state=active]:bg-mulberry font-extrabold data-[state=active]:text-white data-[state=inactive]:text-mulberry flex-1 px-2 sm:px-4"
            >
              U-Pick
            </TabsTrigger>
            <TabsTrigger
              value="farmers-market"
              className="data-[state=active]:bg-mulberry font-extrabold data-[state=active]:text-white data-[state=inactive]:text-mulberry flex-1 px-2 sm:px-4 whitespace-nowrap"
            >
              Farmers&apos; Markets
            </TabsTrigger>
            {/* <TabsTrigger
              value="home-delivery"
              className="data-[state=active]:bg-mulberry data-[state=active]:text-white data-[state=inactive]:text-gray-800 flex-1 px-2 sm:px-4"
            >
              Home Delivery
            </TabsTrigger> */}
          </TabsList>
        </div>

        <div className=" bg-white overflow-y-auto">
          <TabsContent value="all">
            <CombinedSchedule />
          </TabsContent>

          <TabsContent value="u-pick">
            <Schedule />
          </TabsContent>

          <TabsContent value="farmers-market">
            <FarmerMarket />
          </TabsContent>

          {/* <TabsContent value="home-delivery">
            <HomeDelivery />
          </TabsContent> */}
        </div>
      </Tabs>
    </div>
  );
}

// Simple loading fallback component
function ScheduleLoading() {
  return (
    <div className="max-w-5xl mx-auto">
      <h3 className="text-center mb-5 mt-3 text-2xl font-bold text-mulberry">Schedule</h3>
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded mb-4"></div>
        <div className="h-64 bg-gray-100 rounded"></div>
      </div>
    </div>
  );
}

// Main page component that wraps the client component in Suspense
function SchedulePage() {
  return (
    <Suspense fallback={<ScheduleLoading />}>
      <ScheduleContent />
    </Suspense>
  );
}

export default SchedulePage;
