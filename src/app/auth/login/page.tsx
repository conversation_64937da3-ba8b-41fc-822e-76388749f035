'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

import ErrorAlert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import Login from '@/src/features/auth/components/Login';
import {
  selectIsAuthenticated,
  selectAuthLoading,
  selectAuthError,
  verifyMagicLink,
} from '@/src/features/auth/slices/authenticationSlice';
import { useAppDispatch, useAppSelector } from '@/src/store';

function LoginContent() {
  /**
   * Start Initials
   */
  // const [showModal, setShowModal] = useState(true);
  const [verificationAttempted, setVerificationAttempted] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();

  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);

  const email = searchParams.get('email');
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // TODO: Need to add the auth domain check for nextURL
  useEffect(() => {
    const verifyUser = async () => {
      if (!email || verificationAttempted) return;
      if (window.location.href.includes('apiKey=')) {
        setVerificationAttempted(true);
        // setShowModal(false);
        await dispatch(verifyMagicLink({ email }));
      }
    };

    verifyUser();
  }, [email, dispatch, verificationAttempted]);

  useEffect(() => {
    if (isAuthenticated) {
      // If authenticated, redirect to nextUrl
      const nextUrl = searchParams.get('nextUrl');
      const redirectPath = nextUrl ? decodeURIComponent(nextUrl) : '/';
      router.replace(redirectPath);
    }
  }, [isAuthenticated, searchParams, router]);

  // const handleClose = () => {
  //   // setShowModal(false);
  //   // Clear magic link state when closing
  //   dispatch(clearMagicLinkSent());

  //   // Get both URLs from search params
  //   const prevUrl = searchParams.get('prevUrl');
  //   const nextUrl = searchParams.get('nextUrl');

  //   // If prevUrl is the same as nextUrl, it means we're trying to access a protected route
  //   // In this case, redirect to home instead of the protected route
  //   const redirectPath = prevUrl === nextUrl ? '/' : prevUrl ? decodeURIComponent(prevUrl) : '/';

  //   router.replace(redirectPath);
  // };
  /**
   * End Lifecycle Methods
   */

  if (verificationAttempted) {
    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <Spinner />
            <h2 className="text-xl font-semibold text-gray-700">Verifying your login...</h2>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <ErrorAlert error={error} title="Verification Failed" />
            <p className="text-center text-gray-600 mt-4">Redirecting you to home page...</p>
          </div>
        </div>
      );
    }
  }

  return <Login />;
}

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <Spinner />
        </div>
      }
    >
      <LoginContent />
    </Suspense>
  );
}
