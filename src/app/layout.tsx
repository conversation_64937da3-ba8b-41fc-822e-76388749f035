import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import Script from 'next/script';

import './globals.css';
import LayoutWrapper from '@/src/components/LayoutWrapper';
import Sidebar from '@/src/components/Sidebar';
import { Toaster } from '@/src/components/ui/sonner';

import Header from '../components/Header';
import Providers from '../components/Providers';
import SidebarHeader from '../components/SidebarHeader';

/**
 * Configure Geist Sans font for general text
 * @see https://geist-font.vercel.app/
 */
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

/**
 * Configure Geist Mono font for monospace text
 * @see https://geist-font.vercel.app/
 */
const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

/**
 * Metadata configuration for the application
 * These values are used for SEO and browser tab information
 */
export const metadata: Metadata = {
  title: 'My Very Mulberry',
  description: 'My Very Mulberry',
  manifest: '/site.webmanifest',
  applicationName: 'My Very Mulberry',
  appleWebApp: {
    capable: true,
    title: 'My Very Mulberry',
    statusBarStyle: 'default',
    startupImage: '/splash/splash-screen.png',
  },
  formatDetection: {
    telephone: true,
  },
  icons: {
    icon: '/favicon/favicon.ico',
    shortcut: '/favicon/favicon.ico',
    apple: '/favicon/apple-touch-icon.png',
    other: [
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        url: '/favicon/favicon-16x16.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        url: '/favicon/favicon-32x32.png',
      },
    ],
  },
  themeColor: '#892b76',
};

// Add a separate viewport export
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
};

/**
 * Root Layout Component
 *
 * Provides the base structure for all pages in the application.
 * Includes:
 * - HTML document structure
 * - Font configuration
 * - Header component
 * - Main content area
 * - Redux and other providers
 *
 * @param {Object} props - Component properties
 * @param {React.ReactNode} props.children - Child components to render within the layout
 * @returns {JSX.Element} The root layout structure
 */
export default function RootLayout({ children }: { children: React.ReactNode }) {
  // Check if we're in debug mode
  const isDebugMode = process.env.NEXT_PUBLIC_APP_CHECK_DEBUG_MODE === 'true';

  return (
    <html lang="en">
      <head>
        <link rel="apple-touch-icon" href="/favicon/apple-touch-icon.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="theme-color" content="#892b76" />

        {/* Only load analytics scripts when not in debug mode */}
        {!isDebugMode && (
          <>
            {/* Google Tag Manager */}
            <Script id="google-tag-manager">
              {`
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-5R4B7FHJ');
              `}
            </Script>
            {/* Google Analytics */}
            <Script async src="https://www.googletagmanager.com/gtag/js?id=G-FRZVGHGGYY" />
            <Script id="google-analytics">
              {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', 'G-FRZVGHGGYY',{ debug_mode: true });
              `}
            </Script>
            {/* Microsoft Clarity */}
            <Script id="microsoft-clarity">
              {`
                (function(c,l,a,r,i,t,y){
                    c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                    t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                    y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "qyazlwb65q");
              `}
            </Script>
          </>
        )}
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        {/* Main container with minimum full viewport height */}
        <div className="min-h-screen flex flex-col">
          {/* Global navigation header */}
          <Providers>
            <Header />
            {/* Main content area with sidebar and content */}
            <div className="flex flex-col flex-1  overflow-hidden">
              <LayoutWrapper sidebar={<Sidebar />} sidebarHeader={<SidebarHeader />}>
                {children}
              </LayoutWrapper>
            </div>
          </Providers>
        </div>
        <Toaster richColors position="bottom-center" closeButton invert duration={10000} />

        {/* Only load feedback and chat widgets when not in debug mode */}
        {!isDebugMode && (
          <>
            <Script id="ybug-widget" strategy="afterInteractive">
              {`
                (function() {
                    window.ybug_settings = {
                      "id": "dy75c9a9vwbscpsznc4h",
                      "position": "left-bottom" // Position the button at the left bottom
                    };
                    var ybug = document.createElement('script'); ybug.type = 'text/javascript'; ybug.async = true;
                    ybug.src = 'https://widget.ybug.io/button/'+window.ybug_settings.id+'.js';
                    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ybug, s);
                })();
              `}
            </Script>
            <Script id="intercom-settings">
              {`window.intercomSettings = {
                api_base: "https://api-iam.intercom.io",
                app_id: "fhxsouj1",
                alignment: "right",
                vertical_padding: 50, // Increase this value to move the widget up
                horizontal_padding: 20
              };`}
            </Script>
            <Script id="intercom-script">{`// We pre-filled your app ID in the widget URL: 'https://widget.intercom.io/widget/fhxsouj1'
              (function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',w.intercomSettings);}else{var d=document;var i=function(){i.c(arguments);};i.q=[];i.c=function(args){i.q.push(args);};w.Intercom=i;var l=function(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/fhxsouj1';var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);};if(document.readyState==='complete'){l();}else if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})();`}</Script>
            <noscript>
              <iframe
                src="https://www.googletagmanager.com/ns.html?id=GTM-5R4B7FHJ"
                height="0"
                width="0"
                style={{ display: 'none', visibility: 'hidden' }}
              ></iframe>
            </noscript>
          </>
        )}
      </body>
    </html>
  );
}
