'use client';

import { RefreshCw, AlertTriangle, ExternalLink, Info, ArrowLeft } from 'lucide-react'; // Added ArrowLeft
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useRef, useState, useCallback } from 'react';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import {
  selectIsAuthenticated,
  selectAuthLoading,
} from '@/src/features/auth/slices/authenticationSlice';
import { loadClaimDetails } from '@/src/features/self-checkout/anonymous/client/apiService';
import ClaimView from '@/src/features/self-checkout/anonymous/components/ClaimView';
import { logClient } from '@/src/features/self-checkout/anonymous/lib/logging';
import {
  selectAnonymousFlowStatus,
  selectCurrentAttempt,
  selectAnonymousFlowError,
  setFlowStatus,
  setCurrentAttempt, // Added setCurrentAttempt
} from '@/src/features/self-checkout/anonymous/slices/anonymousFlowSlice';
import { LogLevel } from '@/src/lib/logging';
import { useAppDispatch, useAppSelector } from '@/src/store';

function ClaimPageContent() {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const attemptIdFromUrl = searchParams.get('attemptId');

  const flowStatusFromSlice = useAppSelector(selectAnonymousFlowStatus);
  const currentAttemptInState = useAppSelector(selectCurrentAttempt);
  const flowErrorFromSlice = useAppSelector(selectAnonymousFlowError);

  const isLoadingRef = useRef(false); // Tracks if loadClaimDetails for *this attemptId* is in progress
  const [isWaitingForProcessing, setIsWaitingForProcessing] = useState(false);
  const [isOrderUnpaid, setIsOrderUnpaid] = useState(false);
  const attemptIdForRetryRef = useRef<string | null>(null); // Stores attemptId if UI is showing 409-specific message

  const dispatchLoadClaimDetails = useCallback(
    async (attemptIdToLoad: string, isManualRetry: boolean = false) => {
      if (isLoadingRef.current && !isManualRetry) {
        logClient(
          LogLevel.DEBUG,
          'ClaimPageContent.dispatchLoadClaimDetails',
          'Skipping: Load already in progress for this attempt',
          { attemptId: attemptIdToLoad }
        );
        return;
      }

      isLoadingRef.current = true;
      logClient(
        LogLevel.DEBUG,
        'ClaimPageContent.dispatchLoadClaimDetails',
        'Resetting UI states (isOrderUnpaid, isWaitingForProcessing) before dispatch',
        { attemptIdToLoad }
      );
      setIsOrderUnpaid(false);
      setIsWaitingForProcessing(false);

      // Only set to 'loadingClaimDetails' if not already in that state for this specific attempt,
      // or if it's a manual retry.
      // This helps avoid unnecessary status flickering if called multiple times.
      if (
        isManualRetry ||
        flowStatusFromSlice !== 'loadingClaimDetails' ||
        currentAttemptInState?.attemptId !== attemptIdToLoad
      ) {
        dispatch(setFlowStatus('loadingClaimDetails'));
      }

      if (isManualRetry) {
        attemptIdForRetryRef.current = null; // Clear 409-specific UI trigger on manual retry
      }

      logClient(
        LogLevel.INFO,
        'ClaimPageContent.dispatchLoadClaimDetails',
        `Dispatching loadClaimDetails (${isManualRetry ? 'manual' : 'auto'})`,
        { attemptId: attemptIdToLoad }
      );

      try {
        const resultAction = await dispatch(loadClaimDetails(attemptIdToLoad));

        if (loadClaimDetails.fulfilled.match(resultAction)) {
          logClient(
            LogLevel.INFO,
            'ClaimPageContent.dispatchLoadClaimDetails',
            'loadClaimDetails fulfilled.',
            {
              attemptId: attemptIdToLoad,
              resultStep: resultAction.payload?.latestPayload?.step,
            }
          );
          attemptIdForRetryRef.current = null; // Clear any 409 specific UI state
        } else if (loadClaimDetails.rejected.match(resultAction)) {
          const errorPayload = resultAction.payload as
            | { status?: number; message?: string }
            | string;
          const status = typeof errorPayload === 'object' ? errorPayload.status : undefined;
          const message =
            typeof errorPayload === 'object'
              ? errorPayload.message
              : typeof errorPayload === 'string'
                ? errorPayload
                : 'An unknown error occurred while loading details.';

          logClient(
            LogLevel.WARN,
            'ClaimPageContent.dispatchLoadClaimDetails',
            'loadClaimDetails rejected.',
            {
              attemptId: attemptIdToLoad,
              errorStatus: status,
              errorMessage: message,
            }
          );

          if (status === 409) {
            attemptIdForRetryRef.current = attemptIdToLoad; // Set this to show specific UI
            if (message && message.toLowerCase().includes('order payment is not complete')) {
              logClient(
                LogLevel.INFO,
                'ClaimPageContent.dispatchLoadClaimDetails',
                'Setting isOrderUnpaid = true due to 409',
                { attemptIdToLoad }
              );
              setIsOrderUnpaid(true);
            } else {
              logClient(
                LogLevel.INFO,
                'ClaimPageContent.dispatchLoadClaimDetails',
                'Setting isWaitingForProcessing = true due to 409',
                { attemptIdToLoad }
              );
              setIsWaitingForProcessing(true);
            }
          } else {
            // For non-409 errors, the generic error UI will be shown due to `setError` in the thunk.
            // No need to set specific flags here.
            attemptIdForRetryRef.current = null;
          }
        }
      } catch (e) {
        logClient(
          LogLevel.ERROR,
          'ClaimPageContent.dispatchLoadClaimDetails',
          'Outer catch: Dispatch error during loadClaimDetails',
          {
            attemptId: attemptIdToLoad,
            error: (e as Error).message,
          }
        );
        attemptIdForRetryRef.current = null; // Clear 409 specific UI state
        // The thunk itself should dispatch setError for global error display.
      } finally {
        isLoadingRef.current = false;
      }
    },
    [dispatch, flowStatusFromSlice, currentAttemptInState?.attemptId] // Added currentAttemptInState to dep array
  );

  useEffect(() => {
    logClient(LogLevel.INFO, 'ClaimPageContent.useEffect[main]', 'Effect triggered', {
      attemptIdFromUrl,
      currentFlowStatus: flowStatusFromSlice,
      currentAttemptIdInState: currentAttemptInState?.attemptId,
      isLoadingRefCurrent: isLoadingRef.current,
      isWaitingForProcessing,
      isOrderUnpaid,
      attemptIdForRetryRefCurrent: attemptIdForRetryRef.current,
    });

    if (!attemptIdFromUrl) {
      logClient(
        LogLevel.ERROR,
        'ClaimPageContent.useEffect[main]',
        'No attemptId in URL, redirecting to /self-check.'
      );
      router.replace('/self-check');
      return;
    }

    // If the `currentAttemptInState` does not match `attemptIdFromUrl`,
    // or if it matches but we are in a state that suggests reloading (e.g. a general 'error' state not tied to a 409 retry for *this* attempt)
    // then we should try to load.
    // The `isLoadingRef.current` ensures we don't dispatch if already loading *this specific attempt*.
    if (!isLoadingRef.current) {
      // If `currentAttemptInState` is for a different attempt, or null, clear the 409-specific UI states.
      // This ensures that if we navigate from one claim page to another, or from self-check to claim,
      // we don't incorrectly show a 409 message related to a previous attempt.
      if (currentAttemptInState?.attemptId !== attemptIdFromUrl) {
        setIsOrderUnpaid(false);
        setIsWaitingForProcessing(false);
        attemptIdForRetryRef.current = null;
        // If currentAttempt is not for this URL, it's stale, clear it.
        // loadClaimDetails will populate it.
        // Dispatching null here ensures the initial loading spinner shows correctly if previous attempt data was present.
        dispatch(setCurrentAttempt(null));
      }

      // Condition to dispatch:
      // 1. Not currently loading this attempt.
      // 2. EITHER:
      //    a. currentAttempt in state doesn't match the URL's attemptId (stale data or fresh load).
      //    b. currentAttempt matches, BUT the flowStatus is 'error' AND it's not a 409-specific wait for *this* attempt
      //       (meaning a general error occurred, and we should try reloading this attempt's details).
      //    c. currentAttempt matches, BUT flowStatus is 'idle' or some other non-final state, worth a refresh.
      // The `loadClaimDetails` thunk itself is idempotent for already processed steps (2 & 3) by checking Dexie.
      const shouldDispatch =
        currentAttemptInState?.attemptId !== attemptIdFromUrl ||
        (currentAttemptInState?.attemptId === attemptIdFromUrl &&
          flowStatusFromSlice === 'error' &&
          attemptIdForRetryRef.current !== attemptIdFromUrl) ||
        (currentAttemptInState?.attemptId === attemptIdFromUrl &&
          ![
            'readyToClaim',
            'claimed',
            'verifying',
            'verified',
            'loadingClaimDetails',
            'claimDetailsRetryWait',
          ].includes(flowStatusFromSlice) &&
          attemptIdForRetryRef.current !== attemptIdFromUrl);

      if (shouldDispatch) {
        logClient(
          LogLevel.DEBUG,
          'ClaimPageContent.useEffect[main]',
          'Conditions met for dispatchLoadClaimDetails',
          {
            attemptIdFromUrl,
            currentAttemptIdInState: currentAttemptInState?.attemptId,
            flowStatusFromSlice,
          }
        );
        dispatchLoadClaimDetails(attemptIdFromUrl);
      } else {
        logClient(
          LogLevel.DEBUG,
          'ClaimPageContent.useEffect[main]',
          'Skipping dispatch: Data for this attempt seems current or a 409-specific wait is active for this attempt.',
          {
            attemptIdFromUrl,
            currentAttemptIdInState: currentAttemptInState?.attemptId,
            flowStatusFromSlice,
            attemptIdForRetryRefCurrent: attemptIdForRetryRef.current,
          }
        );
      }
    } else {
      logClient(
        LogLevel.DEBUG,
        'ClaimPageContent.useEffect[main]',
        'Skipping dispatch: isLoadingRef.current is true for this attempt.',
        { attemptIdFromUrl }
      );
    }
  }, [
    attemptIdFromUrl, // Key dependency
    // flowStatusFromSlice, // dispatchLoadClaimDetails depends on it, so keep it if that causes re-runs
    // currentAttemptInState?.attemptId, // dispatchLoadClaimDetails depends on it
    // isWaitingForProcessing, // These flags are for UI, not for triggering load itself.
    // isOrderUnpaid,          // ^
    router,
    dispatchLoadClaimDetails, // dispatchLoadClaimDetails itself will change if its deps change
    dispatch, // Added dispatch
  ]);

  const handleManualRetry = () => {
    if (attemptIdFromUrl) {
      logClient(LogLevel.INFO, 'ClaimPageContent.handleManualRetry', 'Manual retry triggered.', {
        attemptId: attemptIdFromUrl,
      });
      // Resetting these flags before manual retry ensures the UI doesn't get stuck on old 409 message if retry is successful
      setIsOrderUnpaid(false);
      setIsWaitingForProcessing(false);
      attemptIdForRetryRef.current = null;
      dispatchLoadClaimDetails(attemptIdFromUrl, true);
    }
  };

  if (!attemptIdFromUrl) {
    // This should ideally be caught by the useEffect redirecting, but as a safeguard for render.
    return (
      <div className="text-center p-4 text-red-500">
        Missing attempt information. Redirecting...
      </div>
    );
  }

  // Show initial loading spinner IF:
  // - `flowStatus` is `loadingClaimDetails` AND
  // - `isLoadingRef.current` is true (meaning the load is for *this* attemptId) AND
  // - We are not in a specific 409 wait state (isOrderUnpaid or isWaitingForProcessing for this attemptId) AND
  // - `currentAttemptInState` is null or for a different attemptId (meaning we don't have data for *this* attemptId yet).
  const showInitialLoadingSpinner =
    flowStatusFromSlice === 'loadingClaimDetails' &&
    isLoadingRef.current &&
    !isOrderUnpaid &&
    !isWaitingForProcessing &&
    (!currentAttemptInState || currentAttemptInState.attemptId !== attemptIdFromUrl);

  if (showInitialLoadingSpinner) {
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-3 bg-white shadow-lg rounded-lg min-h-[200px]">
        <Spinner />
        <p className="text-lg text-mulberry animate-pulse">Loading claim details...</p>
      </div>
    );
  }

  // UI for 409: Order Unpaid
  if (isOrderUnpaid && attemptIdForRetryRef.current === attemptIdFromUrl) {
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-4 bg-white shadow-lg rounded-lg min-h-[200px] border-l-4 border-red-500">
        <div className="text-red-500">
          <AlertTriangle className="w-12 h-12 mx-auto" />
        </div>
        <p className="text-xl font-semibold text-gray-800 text-center">Payment Not Completed</p>
        <p className="text-sm text-gray-600 text-center px-4">
          It appears the payment for this purchase was not successfully completed with Square.
        </p>
        <Button
          onClick={() => router.push('/self-check')}
          className="mt-3 bg-mulberry hover:bg-mulberryHover text-white px-6 py-2 text-base"
        >
          <ExternalLink className="mr-2 h-4 w-4" /> Start New Purchase
        </Button>
        <Button
          onClick={handleManualRetry}
          variant="outline"
          className="mt-2 text-sm"
          disabled={isLoadingRef.current}
        >
          <RefreshCw className="mr-2 h-3 w-3" /> Check Status Again
        </Button>
      </div>
    );
  }

  // UI for 409: Order Still Processing
  if (isWaitingForProcessing && attemptIdForRetryRef.current === attemptIdFromUrl) {
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-4 bg-white shadow-lg rounded-lg min-h-[200px] border-l-4 border-yellow-400">
        <div className="text-yellow-500">
          <Info className="w-12 h-12 mx-auto" />
        </div>
        <p className="text-xl font-semibold text-gray-800 text-center">Order Still Processing</p>
        <p className="text-sm text-gray-600 text-center px-4">
          {flowErrorFromSlice && attemptIdForRetryRef.current === attemptIdFromUrl
            ? flowErrorFromSlice // Show specific message from API if available for this 409
            : 'Your order is being processed.'}{' '}
          This can take a few moments.
        </p>
        <Button
          onClick={handleManualRetry}
          className="mt-3 bg-mulberry hover:bg-mulberryHover text-white px-6 py-2 text-base"
          disabled={isLoadingRef.current}
        >
          <RefreshCw className="mr-2 h-4 w-4" /> Check Status Again
        </Button>
      </div>
    );
  }

  // UI for General Error (when `currentAttemptInState` matches `attemptIdFromUrl` or is null and not loading)
  // AND not in a specific 409 wait state for *this* attempt.
  if (
    flowStatusFromSlice === 'error' &&
    attemptIdForRetryRef.current !== attemptIdFromUrl && // Not a 409 wait for *this* specific attempt
    (currentAttemptInState?.attemptId === attemptIdFromUrl ||
      (!currentAttemptInState && !isLoadingRef.current))
  ) {
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-3 bg-white shadow-lg rounded-lg min-h-[200px] border-l-4 border-red-500">
        <div className="text-red-500">
          <AlertTriangle className="w-12 h-12 mx-auto" />
        </div>
        <p className="text-lg font-medium text-red-700 text-center">
          {flowErrorFromSlice || 'An unexpected error occurred. Please try again.'}
        </p>
        <Button
          onClick={handleManualRetry}
          className="mt-2 bg-mulberry hover:bg-mulberryHover text-white"
          disabled={isLoadingRef.current}
        >
          <RefreshCw className="mr-2 h-4 w-4" /> Try Again
        </Button>
      </div>
    );
  }

  // If we have currentAttempt data for the specific attemptId in the URL, and we're not in an error state for it (or a 409 wait)
  // then render ClaimView.
  // This also covers 'readyToClaim', 'claimed', 'verifying', 'verified'.
  if (
    currentAttemptInState?.attemptId === attemptIdFromUrl &&
    flowStatusFromSlice !== 'error' // If it's an error, it should have been caught above
  ) {
    return <ClaimView attemptId={attemptIdFromUrl} />;
  }

  // Fallback: If none of the above conditions are met, it implies we are still waiting for data
  // for this specific attemptId, or there's an inconsistent state.
  // This also catches the case where `currentAttemptInState` is null and `flowStatusFromSlice` is not `loadingClaimDetails`
  // (e.g., after `setCurrentAttempt(null)` in `useEffect[main]` before `loadClaimDetails` updates it).
  logClient(
    LogLevel.DEBUG,
    'ClaimPageContent',
    'Fallback rendering: Awaiting data or state update for this attempt.',
    {
      attemptIdFromUrl,
      currentAttemptIdInState: currentAttemptInState?.attemptId,
      flowStatusFromSlice,
      isWaitingForProcessing,
      isOrderUnpaid,
      flowErrorFromSlice,
      isLoadingRefCurrent: isLoadingRef.current,
    }
  );
  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-3 bg-white shadow-lg rounded-lg min-h-[200px]">
      <Spinner />
      <p className="text-lg text-mulberry">Loading your purchase data...</p>
    </div>
  );
}

export default function ClaimPage() {
  const router = useRouter();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isAuthLoading = useAppSelector(selectAuthLoading);

  useEffect(() => {
    if (!isAuthLoading && isAuthenticated) {
      logClient(LogLevel.WARN, 'ClaimPage', 'User is authenticated, redirecting to /');
      router.replace('/');
    }
  }, [isAuthLoading, isAuthenticated, router]);

  if (isAuthLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner />
      </div>
    );
  }

  if (isAuthenticated && !isAuthLoading) {
    // This state is brief as the useEffect above should trigger a redirect.
    return (
      <div className="flex justify-center items-center min-h-screen">
        <p>Redirecting...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-lg">
      <div className="mb-4">
        <Button variant="outline" size="sm" onClick={() => router.push('/self-check')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Checkout
        </Button>
      </div>
      <h1 className="text-3xl font-bold text-center mb-6 text-mulberry">Claim Your Purchase</h1>
      <Suspense
        fallback={
          <div className="flex flex-col items-center justify-center p-6 space-y-3 bg-white shadow-lg rounded-lg min-h-[200px]">
            <Spinner />
            <p className="text-lg text-mulberry animate-pulse">Loading details...</p>
          </div>
        }
      >
        <ClaimPageContent />
      </Suspense>
    </div>
  );
}
