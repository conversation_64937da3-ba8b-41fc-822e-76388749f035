'use client';

import React, { useEffect, useMemo } from 'react';

import { Skeleton } from '@/src/components/ui/skeleton';

import withAuth from '../features/auth/hoc/withAuth';
import SubscriptionsCard from '../features/subscription/components/SubscriptionsCard';
import { fetchUserSubscription } from '../features/subscription/slices/userSubscriptionSlice';
// import SendGiftCard from '../features/u-pick/components/SendGiftCard';
import UpcomingEventCard from '../features/u-pick/components/UpcomingEventCard';
import { fetchUserTickets } from '../features/u-pick/slices/guestsSlice';
import { TICKET_TYPES } from '../lib/constants';
import { getPacificTimeNow } from '../lib/timezone';
import { getTicketType } from '../lib/utils';
import { useAppDispatch, useAppSelector, RootState } from '../store';

const Home = () => {
  /**
   * Start Initials
   */
  const { loading: guestsLoading } = useAppSelector((state: RootState) => state.guests);
  const { subscriptions, loading: subscriptionLoading } = useAppSelector(
    (state: RootState) => state.userSubscription
  );

  const dispatch = useAppDispatch();
  const {
    user,
    isAuthenticated,
    loading: authLoading,
  } = useAppSelector((state: RootState) => state.authentication);

  const activeTickets = useAppSelector((state: RootState) => state.guests.activeTickets);
  const upcomingOrder = useMemo(() => {
    if (!activeTickets || activeTickets.length === 0) return null;

    const today = getPacificTimeNow();
    today.setHours(0, 0, 0, 0);

    // Sort orders by event date (soonest upcoming first)
    const sortedOrders = [...activeTickets].sort((orderGroupA, orderGroupB) => {
      const orderIdA = Object.keys(orderGroupA)[0];
      const orderIdB = Object.keys(orderGroupB)[0];

      // Get the first ticket from each order
      const ticketA = orderGroupA[orderIdA]?.[0];
      const ticketB = orderGroupB[orderIdB]?.[0];

      // Get event dates from event_details.start_date.date or schedule.event_date
      const dateA = ticketA?.event_details?.start_date?.date
        ? new Date(ticketA.event_details.start_date.date)
        : ticketA?.schedule?.event_date
          ? new Date(ticketA.schedule.event_date)
          : new Date(9999, 0); // Far future date ensures tickets without dates are sorted last

      const dateB = ticketB?.event_details?.start_date?.date
        ? new Date(ticketB.event_details.start_date.date)
        : ticketB?.schedule?.event_date
          ? new Date(ticketB.schedule.event_date)
          : new Date(9999, 0); // Far future date ensures tickets without dates are sorted last

      // Sort by date (ascending)
      return dateA.getTime() - dateB.getTime();
    });

    // Filter for future events and find the first valid order
    for (const orderGroup of sortedOrders) {
      const orderId = Object.keys(orderGroup)[0];
      const tickets = orderGroup[orderId];

      // Check if any ticket in this order is a valid upcoming event
      const hasValidUpcomingTicket = tickets.some(ticket => {
        // Skip add-ons, parking passes, etc.
        const type = getTicketType(ticket.ticket_type || '');
        if (type === TICKET_TYPES.CLAM_SHELL || type === TICKET_TYPES.PARKING) return false;

        // Check if event is in the future or today
        const eventDate = ticket.event_details?.start_date?.date || ticket.schedule?.event_date;
        if (!eventDate) return false;

        const parsedDate = new Date(eventDate);
        return parsedDate >= today;
      });

      // If this order has a valid upcoming ticket, return the entire order
      if (hasValidUpcomingTicket) {
        return { [orderId]: tickets };
      }
    }

    return null;
  }, [activeTickets]);

  useEffect(() => {
    if (activeTickets && activeTickets.length > 0) {
    }
  }, [activeTickets, upcomingOrder]);

  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    // Only fetch tickets and subscription if authenticated
    const fetchData = async () => {
      if (isAuthenticated && user?.id) {
        // Fetch both resources in parallel
        const promises = [];

        // Add ticket fetch promise
        promises.push(dispatch(fetchUserTickets({ userId: user.id })));

        // Add subscription fetch promise if email exists
        if (user.email) {
          promises.push(dispatch(fetchUserSubscription(user.email)));
        }

        // Wait for all promises to resolve
        await Promise.all(promises);
      }
    };

    fetchData();
  }, [dispatch, user, isAuthenticated]);
  /**
   * End Lifecycle Methods
   */
  return (
    <div className="w-full ml-0 mr-auto mb-6 lg:w-[60%]">
      <h1 className="text-xl md:text-2xl font-bold text-start mb-5">Home</h1>
      <h2 className="text-lg mb-5 font-bold">Prepare for Your U-Pick Visit</h2>
      <p className="text-md mb-5">
        Bookmark this page for easy access on the day of your visit. <br /> Skip the email
        search—just tap <strong>“Manage”</strong> next to your reservation to view your tickets and
        event details instantly.
      </p>
      {/* Event card with independent loading state */}
      {guestsLoading || authLoading ? (
        <div className="border rounded-lg shadow-sm overflow-hidden">
          <div className="p-4">
            <Skeleton className="h-6 w-[30%] mb-3 bg-mulberryLight/50" />
            <div className="space-y-3 mb-4">
              <Skeleton className="h-5 w-[50%] bg-mulberryLight/50" />
              <Skeleton className="h-4 w-[30%] bg-mulberryLight/50" />
            </div>
            <div className="mt-3">
              <Skeleton className="h-8 w-[15%] bg-mulberryLight/50" />
            </div>
          </div>
        </div>
      ) : (
        <>
          {upcomingOrder && (
            <div className="border rounded-lg shadow-sm overflow-hidden">
              <UpcomingEventCard isHomePage={true} order={upcomingOrder} />
            </div>
          )}
        </>
      )}

      {/* <SendGiftCard /> */}

      {/* Subscription card with independent loading state */}
      {subscriptionLoading || authLoading ? (
        <div className="mt-4 border rounded-lg shadow-sm overflow-hidden">
          <div className="p-4 pb-2">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-[120px] bg-mulberryLight/50" />
              <Skeleton className="h-5 w-[80px] bg-mulberryLight/50" />
            </div>
          </div>
          <div className="px-6 pb-4">
            <div className="space-y-3 mb-4 mt-2">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-[40%] bg-mulberryLight/50" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-[5%] bg-mulberryLight/50" />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-4">
          <SubscriptionsCard
            subscriptions={subscriptions
              ?.filter(sub => {
                // First try to get ACTIVE subscriptions
                const activeSubscriptions = subscriptions?.filter(
                  s =>
                    s.status === 'ACTIVE' &&
                    s.chargedThroughDate &&
                    (new Date(s.chargedThroughDate) > getPacificTimeNow() ||
                      new Date(s.chargedThroughDate).toDateString() ===
                        getPacificTimeNow().toDateString())
                );

                // If there are active subscriptions, only show those
                if (activeSubscriptions && activeSubscriptions.length > 0) {
                  return (
                    sub.status === 'ACTIVE' &&
                    sub.chargedThroughDate &&
                    (new Date(sub.chargedThroughDate) > getPacificTimeNow() ||
                      new Date(sub.chargedThroughDate).toDateString() ===
                        getPacificTimeNow().toDateString())
                  );
                }

                // Otherwise, show PENDING subscriptions
                return sub.status === 'PENDING' && sub.startDate;
              })
              .sort((a, b) => {
                // For ACTIVE subscriptions, sort by chargedThroughDate
                if (a.status === 'ACTIVE' && b.status === 'ACTIVE') {
                  const dateComparison =
                    (a.chargedThroughDate ? new Date(a.chargedThroughDate).getTime() : 0) -
                    (b.chargedThroughDate ? new Date(b.chargedThroughDate).getTime() : 0);

                  if (dateComparison === 0 && a.createdAt && b.createdAt) {
                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
                  }

                  return dateComparison;
                }

                // For PENDING subscriptions, sort by startDate
                return (
                  (a.startDate ? new Date(a.startDate).getTime() : 0) -
                  (b.startDate ? new Date(b.startDate).getTime() : 0)
                );
              })
              .slice(0, 1)}
            isHomePage={true}
          />
        </div>
      )}
    </div>
  );
};

export default withAuth(Home);
