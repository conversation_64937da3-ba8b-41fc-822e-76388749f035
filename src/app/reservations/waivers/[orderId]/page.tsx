'use client';

import { ChevronLeft, Check, User, CircleAlert, Mail, Users as UsersIcon } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import React, { useMemo } from 'react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/src/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/src/components/ui/card';
import { sendWaiverNotification } from '@/src/features/ticket-tailor/slice';
import { ROUTES, TICKET_TYPES } from '@/src/lib/constants';
import { cn, formatTimestamp, getTicketType } from '@/src/lib/utils';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';
import { Tickets } from '@/src/types/Tickets';

const WaiverPage = () => {
  /**
   * Start Initials
   */
  const { activeTickets } = useAppSelector((state: RootState) => state.guests);
  const { user } = useAppSelector((state: RootState) => state.authentication);
  const params = useParams();
  const router = useRouter();
  const orderId = params.orderId as string;
  const dispatch = useAppDispatch();
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // TODO Commented for now for getting thow to reservation issue.
  // useEffect(() => {
  //   if (activeTickets.length === 0) {
  //     router.back();
  //   }
  // }, [activeTickets]);

  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const orderData = useMemo(() => {
    if (!activeTickets || !orderId) return null;
    const orderObject = activeTickets.find(item => orderId in item);
    return orderObject ? orderObject[orderId] : null;
  }, [activeTickets, orderId]);

  const organizer = useMemo(() => {
    if (!orderData || !orderData.length || !user?.id) return null;

    const matchingTicket = orderData.find(ticket => {
      const ticketType = getTicketType(ticket.ticket_type || '');
      return (
        ticketType !== TICKET_TYPES.CLAM_SHELL &&
        ticketType !== TICKET_TYPES.PARKING &&
        ticket.user_details?.id === user.id &&
        ticket.user_details?.first_name?.toLowerCase() === user?.first_name?.toLowerCase() &&
        ticket.user_details?.last_name?.toLowerCase() === user?.last_name?.toLowerCase() &&
        ticket.user_details?.email?.toLowerCase() === user?.email?.toLowerCase()
      );
    });

    return {
      user,
      hasValidWaiver: matchingTicket?.waiver || false,
      waiver: matchingTicket?.waiver || null,
      hasTicket: !!matchingTicket, // Add this property to track if organizer has a ticket
    };
  }, [activeTickets]);

  // Direct calculation of adult guest tickets
  const adultGuestTickets = useMemo(() => {
    if (!orderData) return [];
    return orderData.filter(ticket => {
      const ticketType = getTicketType(ticket.ticket_type || '');
      return (
        ticketType !== TICKET_TYPES.CHILD &&
        ticketType !== TICKET_TYPES.PARKING &&
        ticketType !== TICKET_TYPES.CLAM_SHELL &&
        ticket.user_details?.id !== organizer?.user.id
      );
    });
  }, [orderData, organizer?.user.id]);

  // Direct calculation of child tickets
  const childTickets = useMemo(() => {
    if (!orderData) return [];
    return orderData.filter(ticket => {
      const ticketType = getTicketType(ticket.ticket_type || '');
      return ticketType === TICKET_TYPES.CHILD;
    });
  }, [orderData]);

  // Add this helper function to check if all children have waivers
  const allChildrenHaveWaivers = useMemo(() => {
    if (childTickets.length === 0) return true;
    return childTickets.every(ticket => ticket.hasValidWaiver);
  }, [childTickets]);

  const navigateToSignWaiver = () => {
    router.push(`${ROUTES.SIGN_WAIVER}?tt_order_id=${orderId}`);
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleEmailSend = async (ticket: Tickets) => {
    const payload = {
      fn: ticket?.user_details?.first_name || '',
      ln: ticket?.user_details?.last_name || '',
      em: ticket?.user_details?.email || '',
      ag:
        getTicketType(ticket?.ticket_type || '') === TICKET_TYPES.CHILD
          ? TICKET_TYPES.CHILD
          : getTicketType(ticket?.ticket_type || '') === 'senior'
            ? 'senior'
            : TICKET_TYPES.ADULT,
    };

    // Convert to base64url
    const base64 = Buffer.from(JSON.stringify(payload)).toString('base64');
    // Convert to base64url format by replacing characters
    const token = base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    if (!ticket?.user_details?.email) {
      toast.error('User email is required');
      return;
    }
    // Implement your email sending logic here
    dispatch(sendWaiverNotification({ email: ticket?.user_details?.email, token: token }))
      .unwrap()
      .then(data => {
        if (data.success) {
          toast.success('Waiver notification sent successfully!');
        } else {
          toast.error('Failed to send waiver notification.');
        }
      });
  };

  /**
   * End Custom Methods
   */

  // Check if we have valid data
  const hasValidData = useMemo(() => {
    return !!orderData && !!organizer;
  }, [orderData, organizer]);

  if (!hasValidData) {
    return (
      <div className="w-full max-w-5xl p-2 sm:p-4">
        <div className="flex items-center mb-4 sm:mb-6 space-x-2 sm:space-x-3">
          <Button
            variant="outline"
            size="sm"
            className="mr-2 sm:mr-4 text-mulberry hover:bg-mulberryLight flex items-center gap-1"
            onClick={handleGoBack}
          >
            <ChevronLeft className="h-4 sm:h-5 w-4 sm:w-5" />
            <span className="text-xs sm:text-sm">Back</span>
          </Button>

          <h2 className="text-lg sm:text-2xl font-semibold text-mulberry">Farm Waivers</h2>
        </div>

        <Card className=" p-3 sm:p-6">
          <CardContent className="flex flex-col items-center justify-center py-4 sm:py-8">
            <CircleAlert className="h-10 w-10 sm:h-12 sm:w-12 text-mulberry mb-3 sm:mb-4" />
            <h2 className="text-lg sm:text-xl font-semibold mb-2 text-center">
              No Waiver Information Available
            </h2>
            <p className="text-sm sm:text-base text-muted-foreground mb-4 text-center px-2 sm:px-0">
              The waiver information for this order could not be loaded. Please try viewing your
              reservations again.
            </p>
            <Button
              className="bg-mulberry hover:bg-mulberryHover text-white text-xs sm:text-sm px-3 sm:px-4 py-1 sm:py-2"
              onClick={() => router.push('/reservations')}
            >
              View Reservations
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full ">
      <div className="flex items-center mb-4 sm:mb-4 space-x-2 sm:space-x-3">
        <Button
          variant="outline"
          size="sm"
          className="mr-4 text-mulberry hover:bg-mulberryLight flex items-center gap-1"
          onClick={handleGoBack}
        >
          <ChevronLeft className="h-5 w-5" />
          <span>Back</span>
        </Button>

        <h2 className="text-xl sm:text-2xl font-semibold text-mulberry">Farm Waivers</h2>
      </div>

      <Card className="shadow-md mb-4 sm:mb-6 overflow-hidden">
        {/* Organizer Section */}
        <CardHeader className="bg-mulberryLightest border-b py-3 px-4 sm:pb-4">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 sm:h-5 sm:w-5 text-mulberry" />
            <CardTitle className="text-lg sm:text-xl font-bold">Organizer</CardTitle>
          </div>
        </CardHeader>

        <CardContent className="p-4 sm:p-6">
          <div
            className={cn(
              'flex items-center justify-between p-2 sm:p-3 rounded-md text-sm sm:text-base',
              // Make card green if:
              // 1. Organizer has valid waiver, OR
              // 2. Organizer has no ticket AND (no children OR all children have waivers)
              organizer?.hasValidWaiver || (!organizer?.hasTicket && allChildrenHaveWaivers)
                ? 'bg-green-50 border border-green-200'
                : 'bg-mulberryLightest border border-mulberryLight'
            )}
          >
            <div className="flex items-center">
              <div>
                <p
                  className={cn(
                    'font-medium flex items-center',
                    // Also update text color to match
                    organizer?.hasValidWaiver || (!organizer?.hasTicket && allChildrenHaveWaivers)
                      ? 'text-green-700'
                      : 'text-mulberry'
                  )}
                >
                  {/* Update icon to show check when conditions are met */}
                  {organizer?.hasValidWaiver ||
                  (!organizer?.hasTicket && allChildrenHaveWaivers) ? (
                    <Check className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 text-green-600" />
                  ) : (
                    <CircleAlert className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 text-mulberry" />
                  )}
                  {organizer?.user.first_name || ''} {organizer?.user.last_name || ''}
                  {!organizer?.hasTicket && childTickets.length === 0 && (
                    <span className="ml-2 text-xs">
                      (You are not attending the event. No need to sign waiver.)
                    </span>
                  )}
                </p>
                {organizer?.user.email && (
                  <p className="text-xs sm:text-sm text-gray-500 mt-1 ml-5 sm:ml-7 flex items-center">
                    <Mail className="h-3 w-3 mr-1" />
                    {organizer.user.email}
                  </p>
                )}
                {organizer?.hasValidWaiver && organizer?.waiver && (
                  <div className="flex flex-col text-xs sm:text-sm text-green-600 mt-1 ml-5 sm:ml-7">
                    <span>Signed {formatTimestamp(organizer.waiver.signed_at)}</span>
                  </div>
                )}
              </div>
            </div>
            {/* Show sign waiver button if:
                1. Organizer has a ticket but no waiver, OR
                2. There are children without waivers */}
            {((!organizer?.hasValidWaiver && organizer?.hasTicket) ||
              childTickets.some(ticket => !ticket.hasValidWaiver)) && (
              <Button
                size="sm"
                className="bg-mulberry hover:bg-mulberryHover text-white text-xs sm:text-sm"
                onClick={navigateToSignWaiver}
              >
                Sign Waiver
              </Button>
            )}
          </div>
        </CardContent>

        {/* Only render CardContent when there are child tickets */}
        {childTickets.length > 0 && (
          <CardContent className="p-4 sm:p-6 space-y-6 ml-4 sm:space-y-8">
            <div>
              <div className="flex items-center gap-2 mb-3 sm:mb-4">
                <UsersIcon className="h-4 w-4 sm:h-5 sm:w-5 text-mulberry" />
                <h3 className="text-sm sm:text-md font-semibold">
                  Children in your custody during event
                </h3>
              </div>
              <ul className="space-y-2 sm:space-y-3">
                {childTickets.map((childTicket, index) => (
                  <li
                    key={index}
                    className={cn(
                      'flex items-center justify-between p-2 sm:p-3 rounded-md text-sm sm:text-base',
                      childTicket.hasValidWaiver
                        ? 'bg-green-50 border border-green-200'
                        : 'bg-mulberryLightest border border-mulberryLight'
                    )}
                  >
                    <div className="flex items-center">
                      <div>
                        <p
                          className={cn(
                            'font-medium flex items-center',
                            childTicket.hasValidWaiver ? 'text-green-700' : 'text-mulberry'
                          )}
                        >
                          {childTicket.hasValidWaiver ? (
                            <Check className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 text-green-600" />
                          ) : (
                            <CircleAlert className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 text-mulberry" />
                          )}
                          {childTicket.user_details?.first_name}{' '}
                          {childTicket.user_details?.last_name}
                        </p>
                        {childTicket.user_details?.email && (
                          <p className="text-xs sm:text-sm text-gray-500 mt-1 ml-5 sm:ml-7 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {childTicket.user_details.email}
                          </p>
                        )}
                        {childTicket.hasValidWaiver && childTicket.waiver && (
                          <div className="flex flex-col text-xs sm:text-sm text-green-600 mt-1 ml-5 sm:ml-7">
                            <span>Signed {formatTimestamp(childTicket.waiver.signed_at)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Only show Guests card if there are adult guests */}
      {adultGuestTickets.length > 0 && (
        <Card className="shadow-md overflow-hidden">
          {/* Guests Section */}
          <CardHeader className="bg-mulberryLightest border-b py-3 px-4 sm:pb-4">
            <div className="flex items-center gap-2">
              <UsersIcon className="h-4 w-4 sm:h-5 sm:w-5 text-mulberry" />
              <CardTitle className="text-lg sm:text-xl font-bold">Guests</CardTitle>
            </div>
          </CardHeader>

          <CardContent className="p-4 sm:p-6">
            <ul className="space-y-3 sm:space-y-4">
              {adultGuestTickets.map((adultTicket, index) => (
                <li
                  key={index}
                  className={cn(
                    'flex items-center justify-between p-2 sm:p-3 rounded-md',
                    adultTicket.hasValidWaiver
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-mulberryLightest border border-mulberryLight'
                  )}
                >
                  <div className="flex items-center">
                    <div>
                      <p
                        className={cn(
                          'font-medium flex items-center text-sm sm:text-base',
                          adultTicket.hasValidWaiver ? 'text-green-700' : 'text-mulberry'
                        )}
                      >
                        {adultTicket.hasValidWaiver ? (
                          <Check className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 text-green-600" />
                        ) : (
                          <CircleAlert className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 text-mulberry" />
                        )}
                        {adultTicket.user_details?.first_name} {adultTicket.user_details?.last_name}
                      </p>
                      {adultTicket.user_details?.email && (
                        <p className="text-xs sm:text-sm text-gray-500 mt-1 ml-5 sm:ml-7 flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {adultTicket.user_details.email}
                        </p>
                      )}
                      {adultTicket.hasValidWaiver && adultTicket.waiver && (
                        <div className="flex flex-col text-xs sm:text-sm text-green-600 mt-1 ml-5 sm:ml-7">
                          <span>Signed {formatTimestamp(adultTicket.waiver.signed_at)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  {!adultTicket.hasValidWaiver && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-mulberry text-mulberry hover:bg-mulberryLight text-xs sm:text-sm"
                      onClick={() => handleEmailSend(adultTicket)}
                    >
                      Send Reminder
                    </Button>
                  )}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default WaiverPage;
