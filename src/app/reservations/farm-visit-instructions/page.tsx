'use client';

import { <PERSON>ert<PERSON>ircle, Check, ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Suspense } from 'react';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';

const importantGuidelines = [
  'Each guest must have a ticket and signed waiver to enter the farm.',
  '<PERSON> <PERSON> is responsible for signing the waiver on behalf any child, minor and elder in their group who cannot sign their own waiver online',
  'Sampling a few Mulberries while picking is allowed, We’re sure you’ll want to take home plenty more to enjoy after your U-Pick experience.',
  'NOT PERMITTED Inside the Orchard: Dogs, Bags, Backpacks, Containers, Push Carts, Pull Carts, Wagons, Large Strollers, Large Water Jugs, and Food.',
  'Do not pick leaves, climb trees, or damage branches. Tree damage incurs a $500 penalty.',
  'Picnicking inside the orchard is strictly prohibited.',
  'Ladder and step stool use is strictly at your own risk. Avoid leaning or overreaching.',
  'Children must be supervised at all times. Parents and guardians are responsible for their children’s actions.',
  'While we love dogs, they are not allowed due to food safety reasons. Service animals are welcome in the common area, (not in orchards) but must be kept on leash at all times.',
  'Help us keep the farm clean by refraining from throwing fruit or littering. Clamshells must be closed before checkout. (squished mulberries will not last long.)',
  'Preferred payment options: credit card and Apple Pay. Cash is accepted with a $1 extra fee per clamshell to cover operational expenses. any inquiries, our friendly staff is here to assist you.',
  'In case of adverse weather or fruit shortages, Habitera Farms reserves the right to cancel scheduled events. We will provide raincheck and advance announcements in such cases.',
  'By entering the farm, you consent to being photographed or filmed. These photos may be used in our marketing and social media. If you wish to opt out, <NAME_EMAIL> with the image details.',
];

const whatToExpect = [
  'Discover over 10000 trees loaded with juicy and nutritious mulberries, ready for you to pick and pack.',
  'Find easy parking and ample space to enjoy your time on the farm. VIP paid parking is also available for –',
  'Mulberry pickers are available at check-in for convenient picking, and must be returned at check-out.',
  'Use our provided clamshells to pick and purchase as many sweet mulberries as you desire.',
  'Portable toilet facilities, handwashing stations and paper towels are provided.',
  'Expect a lot of walking in sunny, bright & hot conditions—dress comfortably and stay hydrated.',
];

const FarmVisitInstructionsContent = () => {
  const router = useRouter();

  const handleGoBack = () => {
    router.back();
  };

  return (
    <div className="w-full  mx-auto">
      <div className="flex items-center mb-6">
        <Button
          variant="outline"
          size="sm"
          className="mr-4 text-mulberry hover:bg-mulberryLightest flex items-center gap-1"
          onClick={handleGoBack}
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 className="text-xl md:text-2xl font-bold">Farm Visitor Guidelines and Instructions</h1>
      </div>

      <Card className="mb-6 overflow-hidden">
        <CardContent className="p-4 sm:p-6">
          <div className="grid gap-4 sm:gap-6">
            {/* Important Guidelines Section */}
            <div>
              <h2 className="text-lg font-semibold mb-3 text-mulberry">Important Guidelines</h2>
              <div className="space-y-2">
                {importantGuidelines.map((guideline, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <AlertCircle size={16} className="text-mulberry mt-0.5 shrink-0" />
                    <span>{guideline}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          {/* What to Expect Section */}
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-3 text-mulberry">What to Expect</h2>
            <div className="space-y-2">
              {whatToExpect.map((expectation, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <Check size={16} className="text-mulberry mt-0.5 shrink-0" />
                  <span>{expectation}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default function FarmVisitInstructionsPage() {
  return (
    <Suspense fallback={<Spinner />}>
      <FarmVisitInstructionsContent />
    </Suspense>
  );
}
