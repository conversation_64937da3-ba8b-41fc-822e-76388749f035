'use client';

import { Download, FileSignature } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import withAuth from '@/src/features/auth/hoc/withAuth';
import { selectUser } from '@/src/features/auth/slices/authenticationSlice';
import { fetchUserWaivers, fetchWaiverPdfUrl } from '@/src/features/u-pick/slices/userWaiversSlice';
import { ROUTES } from '@/src/lib/constants';
import { formatWaiverDate, generateWaiverToken } from '@/src/lib/utils';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';
import { Users } from '@/src/types/Users';

const WaiversPage = () => {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const {
    userWaivers,
    childWaivers,
    loading: waiverLoading,
  } = useAppSelector((state: RootState) => state.userWaivers);
  const { loading: authLoading } = useAppSelector((state: RootState) => state.authentication);
  const router = useRouter();

  // Combine loading states to ensure we show spinner during initial load
  const loading = authLoading || waiverLoading;
  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  // Add function to navigate to sign waiver page
  const navigateToSignWaiver = () => {
    if (user) {
      const token = generateWaiverToken({
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email || '',
      } as Users);
      router.push(`${ROUTES.SIGN_WAIVER}?t=${token}&nextUrl=${ROUTES.SIGN_WAIVER}`);
    } else {
      router.push(ROUTES.SIGN_WAIVER);
    }
  };

  const handleDownloadPdf = async (pdfPath: string) => {
    try {
      // Use Redux to fetch the download URL from API
      const downloadUrl = await dispatch(fetchWaiverPdfUrl(pdfPath)).unwrap();
      const url = downloadUrl.url;

      // Open the PDF in a new tab
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error('Failed to open the waiver PDF');
    }
  };
  /**
   * End Custom Methods
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    if (user?.email) {
      dispatch(fetchUserWaivers(user.email));
    }
  }, [dispatch, user?.email]);
  /**
   * End Lifecycle Methods
   */

  if (loading) {
    return <Spinner />;
  }

  return (
    <div className="space-y-6 px-2 mb-6 sm:px-4 md:px-0">
      <div className="flex items-center justify-between">
        <div className="flex flex-col items-start mb-4">
          <h2 className="text-xl sm:text-2xl font-semibold">My Waivers</h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            View and manage your signed liability waivers. These waivers are required each season
            when you visit our farm for a U-Pick Experience. Each adult over 18 needs their own
            waiver. If you organize a U-Pick event that includes minors under the age of 18, you can
            take care of a waiver for yourself and for any minors in your custody for an event with
            just one signature.
          </p>
        </div>
      </div>

      {/* User Waivers */}
      <div className="bg-white rounded-lg shadow p-2 sm:p-4">
        <h3 className="text-base sm:text-lg font-medium mb-2 sm:mb-4">My Personal Waivers</h3>
        {userWaivers && userWaivers.length > 0 ? (
          <div className="overflow-x-auto -mx-2 sm:mx-0">
            <table className="w-full border-collapse text-sm sm:text-base">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-2 sm:px-4 py-2 text-left">Name</th>
                  <th className="px-2 sm:px-4 py-2 text-left hidden sm:table-cell">Signed On</th>
                  <th className="px-2 sm:px-4 py-2 text-left hidden md:table-cell">Expires On</th>
                  <th className="px-2 sm:px-4 py-2 text-left hidden sm:table-cell">Version</th>
                  <th className="px-2 sm:px-4 py-2 text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {userWaivers.map(waiver => (
                  <tr key={waiver.id} className="border-t hover:bg-gray-50">
                    <td className="px-2 sm:px-4 py-2 sm:py-3">
                      {waiver.first_name} {waiver.last_name}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 hidden sm:table-cell">
                      {formatWaiverDate(waiver.signed_at)}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 hidden md:table-cell">
                      {formatWaiverDate(waiver.valid_until)}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 hidden sm:table-cell">
                      v{waiver.waiver_version}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 text-right">
                      {waiver.waiver_pdf_path && (
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={!waiver.waiver_pdf_path}
                          onClick={() =>
                            waiver.waiver_pdf_path && handleDownloadPdf(waiver.waiver_pdf_path)
                          }
                        >
                          <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-0 sm:mr-1" />{' '}
                          <span className="hidden sm:inline">Download</span>
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="py-6">
            <p className="text-gray-500 mb-3">You haven&apos;t signed a waiver yet.</p>
            <p className="text-mulberry font-medium mb-4">
              Sign your waiver now to ensure quick entry to the farm!
            </p>
            <Button
              onClick={navigateToSignWaiver}
              className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white"
            >
              <FileSignature className="h-4 w-4 mr-2" />
              Sign Waiver Now
            </Button>
          </div>
        )}
      </div>

      {/* Child Waivers */}
      {childWaivers && childWaivers.length > 0 && (
        <div className="bg-white rounded-lg shadow p-2 sm:p-4">
          <h3 className="text-base sm:text-lg font-medium mb-2 sm:mb-4">
            Waivers Signed for Children
          </h3>
          <div className="overflow-x-auto -mx-2 sm:mx-0">
            <table className="w-full border-collapse text-sm sm:text-base">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-2 sm:px-4 py-2 text-left">Child Name</th>
                  <th className="px-2 sm:px-4 py-2 text-left hidden sm:table-cell">Signed On</th>
                  <th className="px-2 sm:px-4 py-2 text-left hidden md:table-cell">Expires On</th>
                  <th className="px-2 sm:px-4 py-2 text-left hidden sm:table-cell">Version</th>
                  <th className="px-2 sm:px-4 py-2 text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {childWaivers.map(waiver => (
                  <tr key={waiver.id} className="border-t hover:bg-gray-50">
                    <td className="px-2 sm:px-4 py-2 sm:py-3">
                      {waiver.first_name} {waiver.last_name}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 hidden sm:table-cell">
                      {waiver.signed_at ? formatWaiverDate(waiver.signed_at) : 'N/A'}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 hidden md:table-cell">
                      {waiver.valid_until ? formatWaiverDate(waiver.valid_until) : 'N/A'}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 hidden sm:table-cell">
                      v{waiver.waiver_version}
                    </td>
                    <td className="px-2 sm:px-4 py-2 sm:py-3 text-right">
                      <div className="flex justify-end gap-1 sm:gap-2">
                        {waiver.waiver_pdf_path && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              waiver.waiver_pdf_path && handleDownloadPdf(waiver.waiver_pdf_path)
                            }
                          >
                            <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-0 sm:mr-1" />{' '}
                            <span className="hidden sm:inline">Download</span>
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default withAuth(WaiversPage);
