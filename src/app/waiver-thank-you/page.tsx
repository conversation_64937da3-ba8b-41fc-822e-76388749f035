'use client';

import { CheckCircleIcon } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { fetchUserTickets } from '@/src/features/u-pick/slices/guestsSlice';
import { BUTTON_TEXTS, ROUTES } from '@/src/lib/constants';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

// Separate the main content into a client component
const WaiverThankYouContent = () => {
  /**
   * Start Initials
   */
  const router = useRouter();
  const searchParams = useSearchParams();
  const nextUrl = searchParams.get('nextUrl');
  const user = useAppSelector((state: RootState) => state.authentication.user);

  const dispatch = useAppDispatch();
  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  const handleContinue = () => {
    if (nextUrl) {
      router.push(nextUrl);
    } else {
      if (user && user.id) {
        dispatch(
          fetchUserTickets({
            userId: user.id,
          })
        );
      }
      router.push(ROUTES.RESERVATIONS);
    }
  };
  /**
   * End Custom Methods
   */

  return (
    <div className="max-w-2xl mx-auto p-6 text-center space-y-6">
      <div className="flex justify-center">
        <CheckCircleIcon className="h-16 w-16 text-green-500" />
      </div>

      <h1 className="text-2xl font-bold">Thank You for Signing the Waiver!</h1>

      <p className="text-muted-foreground">
        Your waiver has been successfully submitted. You can now view your reservations and get
        ready for your visit.
      </p>

      <Button
        onClick={handleContinue}
        className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive"
      >
        {nextUrl ? BUTTON_TEXTS.CONTINUE : BUTTON_TEXTS.VIEW_RESERVATIONS}
      </Button>
    </div>
  );
};

// Main page component
export default function WaiverThankYouPage() {
  return (
    <Suspense fallback={<Spinner />}>
      <WaiverThankYouContent />
    </Suspense>
  );
}
