'use client';

import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

import Error<PERSON>lert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Input } from '@/src/components/ui/input';
import withAdminAuth from '@/src/features/auth/hoc/withAdminAuth';
import {
  selectAuthLoading,
  selectAuthError,
  impersonateUser,
} from '@/src/features/auth/slices/authenticationSlice';
import { useAppDispatch, useAppSelector } from '@/src/store';

const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const router = useRouter();
  const dispatch = useAppDispatch();

  const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await dispatch(
      impersonateUser({
        email,
      })
    );
  };

  const handleBackToHome = () => {
    router.replace('/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-6">Admin Login</h1>

        {error && <ErrorAlert error={error} />}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              User Email
            </label>
            <Input
              id="email"
              type="email"
              required
              placeholder="<EMAIL>"
              value={email}
              onChange={e => setEmail(e.target.value)}
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">Only administrators can access this area.</p>
          </div>

          <Button
            type="submit"
            className="w-full bg-mulberry hover:bg-mulberryHover"
            disabled={isLoading}
          >
            See User Profile
          </Button>

          <Button
            type="button"
            variant="outline"
            className="w-full mt-2"
            onClick={handleBackToHome}
          >
            Back to Home
          </Button>
        </form>
      </div>
    </div>
  );
};

export default withAdminAuth(AdminLoginPage);
