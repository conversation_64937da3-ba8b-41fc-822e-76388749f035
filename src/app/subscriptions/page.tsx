'use client';

import React, { useEffect } from 'react';
import { toast } from 'sonner';

import Spinner from '@/src/components/spinner';
import withAuth from '@/src/features/auth/hoc/withAuth';
import SubscriptionsCard from '@/src/features/subscription/components/SubscriptionsCard';
import { fetchUserSubscription } from '@/src/features/subscription/slices/userSubscriptionSlice';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

const SubscriptionsPage = () => {
  const dispatch = useAppDispatch();
  const user = useAppSelector((state: RootState) => state.authentication.user);
  const { subscriptions, loading, error } = useAppSelector(
    (state: RootState) => state.userSubscription
  );

  useEffect(() => {
    if (user?.email) {
      dispatch(fetchUserSubscription(user.email));
    }
  }, [dispatch, user]);

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  return (
    <>
      <div className="text-left space-y-6 lg:w-[60%] mb-6">
        <div className="flex items-center gap-3 mb-4">
          <h2 className="text-xl md:text-2xl font-bold text-start mb-4">My Subscriptions</h2>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        ) : (
          <SubscriptionsCard
            subscriptions={[...subscriptions].sort((a, b) => {
              const dateA = a.startDate ? new Date(a.startDate) : new Date(0);
              const dateB = b.startDate ? new Date(b.startDate) : new Date(0);
              return dateB.getTime() - dateA.getTime();
            })}
          />
        )}
      </div>
    </>
  );
};

export default withAuth(SubscriptionsPage);
