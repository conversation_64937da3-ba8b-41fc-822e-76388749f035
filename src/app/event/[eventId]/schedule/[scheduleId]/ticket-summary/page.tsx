'use client';

import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import ErrorAlert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import EventCard from '@/src/features/u-pick/guests/EventCard';
import {
  fetchEventAndSchedule,
  fetchPaymentProcessLink,
  getTicketStocks,
} from '@/src/features/u-pick/slices/guestsSlice';
import PaymentDetailsCard from '@/src/features/u-pick/tickets/PaymentDetailsCard';
import TicketDetailsCard from '@/src/features/u-pick/tickets/TicketDetailsCard';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

const TicketSummary = () => {
  /**
   * Start Initials
   */
  const router = useRouter();
  const params = useParams();
  const eventId = params.eventId as string;
  const scheduleId = params.scheduleId as string;

  const dispatch = useAppDispatch();

  const { event, schedule, guests, loading, error, paymentProcessLink, booker } = useAppSelector(
    (state: RootState) => state.guests
  );

  const [loadingPay, setLoadingPay] = useState(false);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    if (eventId && scheduleId) {
      dispatch(fetchEventAndSchedule({ eventId, scheduleId }));
    }
  }, [dispatch, eventId, scheduleId]);

  useEffect(() => {
    if (!paymentProcessLink) return;
    setLoadingPay(true);
    router.push(paymentProcessLink.paymentLink?.url || '');
  }, [paymentProcessLink, router]);

  useEffect(() => {
    if (!event || !schedule || !guests || !booker) {
      router.back();
      return;
    }
  }, [event, schedule, guests, booker, router]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const handlePayAndBook = async () => {
    if (!event || !schedule || !guests || !booker) {
      router.back();
      return;
    }

    try {
      // Check available ticket stock
      const stockResult = await dispatch(
        getTicketStocks({
          event_id: eventId,
          schedule_id: scheduleId,
        })
      ).unwrap();

      // Compare available tickets with number of guests
      if (stockResult < guests.length) {
        toast.error(
          `${stockResult === 1 ? 'Sorry, only 1 ticket is' : `Sorry, only ${stockResult} tickets are`} available in the selected slot. Try again with a different slot.`,
          {
            duration: 3000,
            id: 'stock-error',
          }
        );
        return;
      }

      // Proceed with payment if stock is available
      dispatch(fetchPaymentProcessLink({ event, schedule, guests, booker }));
    } catch (_error) {
      toast.error('Failed to verify ticket availability. Please try again.', {
        duration: 3000,
        id: 'stock-check-error',
      });
    }
  };
  /**
   * End Custom Methods
   */

  // Show loading spinner while data is being fetched
  if (loading || loadingPay) return <Spinner />;
  // Show error message if data fetch failed
  if (error) return <ErrorAlert error={error} title="Error" />;

  return (
    <div>
      {/* Display event and schedule information */}
      {event && schedule && <EventCard event={event} schedule={schedule} />}
      {/* Display ticket details */}
      <TicketDetailsCard />
      {/* Display payment details if guests and schedule are available */}
      {guests && schedule && (
        <PaymentDetailsCard
          handlePayAndBook={handlePayAndBook}
          schedule={schedule}
          guests={guests}
          showButton={true}
        />
      )}
    </div>
  );
};

export default TicketSummary;
