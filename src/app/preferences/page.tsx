'use client';

import { Settings } from 'lucide-react';
import React from 'react';

import withAuth from '@/src/features/auth/hoc/withAuth';

const PreferencesPage = () => {
  return (
    <>
      <div className="text-left space-y-6">
        <div className="flex items-center gap-3 mb-4">
          <Settings className="h-8 w-8 text-mulberry" />
          <h2 className="text-xl font-semibold">Preferences Page Coming Soon</h2>
        </div>

        <p className="text-muted-foreground">
          We&apos;re working on building your communication preferences features. Check back soon to
          manage your notification settings and preferences!
        </p>
      </div>
    </>
  );
};

export default withAuth(PreferencesPage);
