'use client';

import { useRouter } from 'next/navigation';
import { useEffect, Suspense } from 'react';

import Spinner from '@/src/components/spinner';
import {
  selectIsAuthenticated,
  selectAuthLoading,
} from '@/src/features/auth/slices/authenticationSlice';
import AnonymousFlowController from '@/src/features/self-checkout/anonymous/components/AnonymousFlowController';
import { useAppSelector } from '@/src/store';

export default function FarmCheckoutPage() {
  const router = useRouter();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isAuthLoading = useAppSelector(selectAuthLoading);

  useEffect(() => {
    if (!isAuthLoading && isAuthenticated) {
      console.warn('FarmCheckoutPage: User is authenticated, redirecting to /');
      router.replace('/');
    }
  }, [isAuthLoading, isAuthenticated, router]);

  // 1. Show spinner while auth is loading
  if (isAuthLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner />
      </div>
    );
  }

  // 2. If authenticated (and not loading), this part of the effect will have run
  //    and redirected.
  if (isAuthenticated) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner />
      </div>
    );
  }

  // 3. If not loading and not authenticated, render the controller
  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <h1 className="text-3xl font-bold text-center mb-6 text-mulberry">Self-Checkout</h1>
      <Suspense fallback={<Spinner />}>
        <AnonymousFlowController />
      </Suspense>
    </div>
  );
}
