import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { Timestamp } from 'firebase/firestore';

import { apiPost } from '@/src/lib/apiClient';
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';

interface UserReferral {
  created_at: Timestamp;
  updated_at: Timestamp;
  total_points_earned: number;
  referral_code: string;
  referrer_email: string;
}

interface ReferralState {
  // Loading state for async operations
  loading: boolean;
  // Error message if any operation fails
  error: string | null;
  userReferral: UserReferral | null;
}

const initialState: ReferralState = {
  error: null,
  loading: false,
  userReferral: null,
};

export const getOrCreateUserReferralCode = createAsyncThunk(
  'referralSlice/getOrCreateUserReferralCode',
  async ({ email }: { email: string }, { rejectWithValue }) => {
    try {
      const response = await apiPost(API_ENDPOINTS.GET_OR_CREATE_REFERRAL_CODE, {
        email,
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_GET_CODE);
      }

      const data = await response.json();
      return data.data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_GET_CODE);
    }
  }
);

const referralSlice = createSlice({
  name: 'referral',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(getOrCreateUserReferralCode.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getOrCreateUserReferralCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getOrCreateUserReferralCode.fulfilled, (state, action) => {
        state.userReferral = action.payload || null;
        state.loading = false;
      });
  },
});

export default referralSlice.reducer;
