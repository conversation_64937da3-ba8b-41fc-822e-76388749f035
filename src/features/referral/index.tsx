'use client';

import {
  Co<PERSON>,
  Facebook,
  Gift,
  Instagram,
  Mail,
  MessageCircle,
  Share2,
  TrendingUp,
} from 'lucide-react';
import React, { useEffect } from 'react';
import { toast } from 'sonner';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Input } from '@/src/components/ui/input';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

import { getOrCreateUserReferralCode } from './slices/referralSlice';

export default function MyReferral() {
  const { user } = useAppSelector((state: RootState) => state.authentication);
  const { error, loading, userReferral } = useAppSelector(
    (state: RootState) => state.referralSlice
  );

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (user && user.email) {
      dispatch(getOrCreateUserReferralCode({ email: user?.email || '' }));
    }
  }, [dispatch, user]);

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${type} copied to clipboard`);
  };

  const shareViaEmail = () => {
    const subject = 'Get discount your first Very Mulberry order!';
    const body = `Hey! Get discount your first Very Mulberry order with my referral link: ${process.env.NEXT_PUBLIC_BASE_URL}/subscription?referral=${userReferral?.referral_code} Fresh, organic mulberries delivered to your door!`;
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  const shareViaWhatsApp = () => {
    const message = `Hey! Get discount your first Very Mulberry order with my referral link: ${process.env.NEXT_PUBLIC_BASE_URL}/subscription?referral=${userReferral?.referral_code} Fresh, organic mulberries delivered to your door!`;
    window.open(`https://wa.me/?text=${encodeURIComponent(message)}`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <h2 className="text-xl font-bold text-red-600">Error</h2>
        <p className="mt-2">{error || 'Unable to get the referral code :)'}</p>
        <Button
          onClick={() => dispatch(getOrCreateUserReferralCode({ email: user?.email || '' }))}
          className="mt-4 bg-mulberry hover:bg-mulberryHover"
        >
          Try Again
        </Button>
      </div>
    );
  }
  return (
    <>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        {/* Hero Section */}
        <div className="text-left mb-8">
          <h1 className="text-3xl font-bold text-mulberry mb-4">Share the Mulberry Love</h1>
          <p className="text-lg text-gray-600 max-w-2xl">
            Invite friends or family to discover fresh, organic mulberries and earn points for every
            successful referral
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Points Stats - Takes full width on mobile, 1 column on desktop */}
          <div className="lg:col-span-1">
            <div className="space-y-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg font-medium flex items-center justify-between">
                    <span>Total Points</span>
                    <Gift className="h-6 w-6 text-mulberry" />
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-4xl font-bold mb-2">450</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg font-medium flex items-center justify-between">
                    <span>Referrals</span>
                    <TrendingUp className="h-5 w-5 text-mulberry" />
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-4xl font-bold mb-2">10</div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Referral Sharing - Takes 2 columns on desktop */}
          <div className="lg:col-span-2">
            <div className="space-y-8">
              {/* Your Referral Link Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-gray-800">
                    <Share2 className="h-5 w-5 text-mulberry" />
                    <span>Your Referral Link</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="flex space-x-2">
                      <Input
                        value={`${process.env.NEXT_PUBLIC_BASE_URL}/subscription?referral=${userReferral?.referral_code}`}
                        readOnly
                        className="bg-gray-50/50 border-gray-200"
                      />
                      <Button
                        onClick={() =>
                          copyToClipboard(
                            `${process.env.NEXT_PUBLIC_BASE_URL}/subscription?referral=${userReferral?.referral_code}`,
                            'Referral link'
                          )
                        }
                        className="bg-mulberry hover:bg-mulberryHover"
                        size="sm"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>

                    <p className="text-sm text-gray-600 bg-purple-50 p-4 rounded-lg border border-purple-100">
                      💡 Share this link with friends and family. When they purchase using your
                      link, you&apos;ll both receive rewards!
                    </p>

                    {/* Share Buttons */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <Button
                        onClick={shareViaEmail}
                        variant="outline"
                        className="flex flex-col space-y-2 h-auto py-4 hover:bg-blue-50 hover:border-blue-300 border-gray-200"
                      >
                        <Mail className="h-6 w-6 text-blue-600" />
                        <span className="text-sm font-medium">Email</span>
                      </Button>

                      <Button
                        onClick={shareViaWhatsApp}
                        variant="outline"
                        className="flex flex-col space-y-2 h-auto py-4 hover:bg-green-50 hover:border-green-300 border-gray-200"
                      >
                        <MessageCircle className="h-6 w-6 text-green-600" />
                        <span className="text-sm font-medium">WhatsApp</span>
                      </Button>

                      <Button
                        variant="outline"
                        className="flex flex-col space-y-2 h-auto py-4 hover:bg-blue-50 hover:border-blue-300 border-gray-200"
                      >
                        <Facebook className="h-6 w-6 text-blue-600" />
                        <span className="text-sm font-medium">Facebook</span>
                      </Button>

                      <Button
                        variant="outline"
                        className="flex flex-col space-y-2 h-auto py-4 hover:bg-sky-50 hover:border-sky-300 border-gray-200"
                      >
                        <Instagram className="h-6 w-6 text-sky-600" />
                        <span className="text-sm font-medium">Twitter</span>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <Card className="bg-gradient-to-r from-mulberry to-mulberryHover text-white">
          <CardContent className="p-8">
            <div className="text-center space-y-8">
              <h3 className="text-3xl font-bold">How It Works</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="bg-white/20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                    <span className="text-3xl font-bold">1</span>
                  </div>
                  <h4 className="font-semibold mb-3 text-lg">Share Your Link</h4>
                  <p className="text-sm opacity-90 leading-relaxed">
                    Share your referral link with friends and family through social media, email, or
                    messaging
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-white/20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                    <span className="text-3xl font-bold">2</span>
                  </div>
                  <h4 className="font-semibold mb-3 text-lg">They Get Discount</h4>
                  <p className="text-sm opacity-90 leading-relaxed">
                    Your friends and family get discount on their first order of fresh, organic
                    mulberries
                  </p>
                </div>
                <div className="text-center">
                  <div className="bg-white/20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                    <span className="text-3xl font-bold">3</span>
                  </div>
                  <h4 className="font-semibold mb-3 text-lg">You Earn Points</h4>
                  <p className="text-sm opacity-90 leading-relaxed">
                    Earn points for each successful referral and redeem for rewards
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
