import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { RootState } from '@/src/store';

import { AnonymousAttemptDexieRecord } from '../client/dexieService';
import { CatalogItem } from '../types';

export type FlowStatus =
  | 'idle'
  | 'initializing'
  | 'loadingInitData'
  | 'readyToBuy'
  | 'creatingPayment'
  | 'loadingClaimDetails'
  | 'readyToClaim'
  | 'claiming'
  | 'claimed'
  | 'verifying'
  | 'verified'
  | 'loadingClaimList'
  | 'claimListReady'
  | 'claimDetailsRetryWait'
  | 'error';

interface AnonymousFlowState {
  status: FlowStatus;
  error: string | null;
  claimAttemptToken: string | null;
  catalog: CatalogItem[] | null;
  claimListAttempts: AnonymousAttemptDexieRecord[];
  currentAttempt: AnonymousAttemptDexieRecord | null;
  claimDetailsRetryInfo: {
    attemptId: string | null;
    retryAllowedAt: number | null;
  } | null;
  lastAttemptEmail: string | null; // New field for pre-population
}

const initialState: AnonymousFlowState = {
  status: 'idle',
  error: null,
  claimAttemptToken: null,
  catalog: null,
  claimListAttempts: [],
  currentAttempt: null,
  claimDetailsRetryInfo: null,
  lastAttemptEmail: null, // Initialize new field
};

const anonymousFlowSlice = createSlice({
  name: 'anonymousFlow',
  initialState,
  reducers: {
    setFlowStatus: (state, action: PayloadAction<FlowStatus>) => {
      state.status = action.payload;
      if (action.payload !== 'error' && action.payload !== 'claimDetailsRetryWait') {
        state.error = null;
      }
      // If transitioning away from a retryWait state, clear the retry info.
      // Also clear if setting to a non-retryWait state directly.
      if (state.status !== 'claimDetailsRetryWait' && action.payload !== 'claimDetailsRetryWait') {
        state.claimDetailsRetryInfo = null;
      }
      if (['idle', 'initializing', 'readyToBuy', 'claimListReady'].includes(action.payload)) {
        state.currentAttempt = null;
      }
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.status = 'error';
      state.error = action.payload;
    },
    setClaimAttemptToken: (state, action: PayloadAction<string | null>) => {
      state.claimAttemptToken = action.payload;
    },
    setCatalog: (state, action: PayloadAction<CatalogItem[] | null>) => {
      state.catalog = action.payload;
    },
    setClaimListAttempts: (state, action: PayloadAction<AnonymousAttemptDexieRecord[]>) => {
      state.claimListAttempts = action.payload;
    },
    setCurrentAttempt: (state, action: PayloadAction<AnonymousAttemptDexieRecord | null>) => {
      state.currentAttempt = action.payload;
    },
    setClaimDetailsRetry: (
      state,
      action: PayloadAction<{ attemptId: string; retryAfterMs: number }>
    ) => {
      state.status = 'claimDetailsRetryWait';
      state.claimDetailsRetryInfo = {
        attemptId: action.payload.attemptId,
        retryAllowedAt: Date.now() + action.payload.retryAfterMs,
      };
      state.error = 'Order processing. Please wait.';
    },
    clearClaimDetailsRetry: state => {
      state.claimDetailsRetryInfo = null;
      if (state.status === 'claimDetailsRetryWait') {
        state.status = 'claimListReady';
        state.error = null;
      }
    },
    updateAttemptInList: (state, action: PayloadAction<AnonymousAttemptDexieRecord>) => {
      const index = state.claimListAttempts.findIndex(
        attempt => attempt.attemptId === action.payload.attemptId
      );
      if (index !== -1) {
        state.claimListAttempts[index] = action.payload;
      }
      if (state.currentAttempt?.attemptId === action.payload.attemptId) {
        state.currentAttempt = action.payload;
      }
    },
    // New action for setting the last attempt email
    setLastAttemptEmail: (state, action: PayloadAction<string | null>) => {
      state.lastAttemptEmail = action.payload;
    },
    resetAnonymousFlow: () => initialState,
  },
});

export const {
  setFlowStatus,
  setError,
  setClaimAttemptToken,
  setCatalog,
  setClaimListAttempts,
  setCurrentAttempt,
  setClaimDetailsRetry,
  clearClaimDetailsRetry,
  updateAttemptInList,
  setLastAttemptEmail, // Export new action
  resetAnonymousFlow,
} = anonymousFlowSlice.actions;

// Selectors
export const selectAnonymousFlowStatus = (state: RootState) => state.anonymousFlow.status;
export const selectAnonymousFlowError = (state: RootState) => state.anonymousFlow.error;
export const selectClaimAttemptToken = (state: RootState) => state.anonymousFlow.claimAttemptToken;
export const selectCatalog = (state: RootState) => state.anonymousFlow.catalog;
export const selectClaimListAttempts = (state: RootState) => state.anonymousFlow.claimListAttempts;
export const selectCurrentAttempt = (state: RootState) => state.anonymousFlow.currentAttempt;
export const selectClaimDetailsRetryInfo = (state: RootState) =>
  state.anonymousFlow.claimDetailsRetryInfo;
// New selector for the last attempt email
export const selectLastAttemptEmail = (state: RootState) => state.anonymousFlow.lastAttemptEmail;

export default anonymousFlowSlice.reducer;
