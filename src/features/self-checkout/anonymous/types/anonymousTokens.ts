import { SelectedItem } from '.';

// For X-Claim-Attempt-Token header
export interface ClaimAttemptTokenPayload {
  claimAttemptId: string; // UUID
  createdAt: number; // Timestamp (ms)
}

// Base structure common to all attempt token steps
interface BaseAttemptTokenPayload {
  attemptId: string; // UUID generated for this specific attempt
  claimAttemptId: string; // From the persistent token
  email: string;
  createdAt: number; // Timestamp (ms) of initial token generation
  updatedAt: number; // Timestamp (ms) of the *last update* to this token
  selectedItems: SelectedItem[]; // Will use the updated SelectedItem type (name, imageUrl, tokentype)
  order_id: string; // Square Order ID, included from the start
  step: 1 | 2 | 3;
  status: 'pending_payment' | 'ready_to_claim' | 'claimed';
  locationId: string; // Added: The Square Location ID used for this attempt
}

// Step 1: After payment intent creation
export interface Step1TokenPayload extends BaseAttemptTokenPayload {
  step: 1;
  status: 'pending_payment';
  // selectedItems will implicitly be the richer version from BaseAttemptTokenPayload
  // locationId is inherited from BaseAttemptTokenPayload
}

// Step 2: After successful payment verification
export interface Step2TokenPayload extends BaseAttemptTokenPayload {
  step: 2;
  status: 'ready_to_claim';
  orderTotal: number; // NEW: Actual total from Square order, in cents
  // locationId is inherited from BaseAttemptTokenPayload
}

// Step 3: After successful claim/burn
export interface Step3TokenPayload extends BaseAttemptTokenPayload {
  step: 3;
  status: 'claimed';
  burnOrderId: string; // Specific to Step 3
  orderTotal: number; // NEW: Carried over from Step 2, actual total from Square order, in cents
  // locationId is inherited from BaseAttemptTokenPayload
}

// Union type for runtime validation or general handling
export type AnonymousAttemptTokenPayload =
  | Step1TokenPayload
  | Step2TokenPayload
  | Step3TokenPayload;

// Helper type guard to check if a payload is Step1
export function isStep1Payload(
  payload: AnonymousAttemptTokenPayload | null | undefined
): payload is Step1TokenPayload {
  return !!payload && payload.step === 1 && payload.status === 'pending_payment';
}

// Helper type guard to check if a payload is Step2
export function isStep2Payload(
  payload: AnonymousAttemptTokenPayload | null | undefined
): payload is Step2TokenPayload {
  return !!payload && payload.step === 2 && payload.status === 'ready_to_claim';
}

// Helper type guard to check if a payload is Step3
export function isStep3Payload(
  payload: AnonymousAttemptTokenPayload | null | undefined
): payload is Step3TokenPayload {
  return !!payload && payload.step === 3 && payload.status === 'claimed';
}
