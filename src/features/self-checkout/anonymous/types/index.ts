import { Customer, Order, CustomAttribute as SquareSdkCustomAttribute } from 'square';

/**
 * Represents an item structure used when initially requesting a purchase.
 * Sent from client to backend for payment intent creation.
 */
export interface PurchaseRequestItem {
  variationId: string;
  quantity: number;
}

/**
 * Represents a selected item for purchase, enriched with details.
 * This is the structure stored in the JWT and Dexie.
 * @property {string} variationId - The Square Catalog Item Variation ID.
 * @property {number} quantity - The quantity of the item selected.
 * @property {string} name - The display name of the item.
 * @property {string} imageUrl - The URL for the product image.
 * @property {string} tokentype - Single character representing the type of item for tokenization.
 */
export interface SelectedItem {
  variationId: string;
  quantity: number;
  name: string;
  imageUrl: string;
  tokentype: string; // Single character
}

/**
 * Represents a catalog item available for purchase.
 * @property {string} variationId - The Square Catalog Item Variation ID.
 * @property {string} name - The display name of the item.
 * @property {number} price - The price of the item in cents.
 * @property {number} limit - The maximum quantity allowed per purchase attempt.
 * @property {string} [image] - Optional URL for the product image.
 * @property {string} tokentype - Single character representing the type of item for tokenization.
 */
export interface CatalogItem {
  variationId: string;
  name: string;
  price: number; // In cents
  limit: number; // Max quantity per purchase
  image?: string; // Optional image URL
  tokentype: string; // Single character, e.g., 'C'
}

// Use the CustomAttribute type directly from the Square SDK
export type AppSquareCustomAttribute = SquareSdkCustomAttribute;

/**
 * Application-specific type combining a base Square Customer with potentially fetched custom attributes.
 */
export interface AppCustomerWithAttributes {
  customer: Customer;
  clamshellTokenBalance?: AppSquareCustomAttribute | null;
}

/**
 * Application-specific type combining a base Square Order with potentially fetched custom attributes.
 */
export interface AppOrderWithAttributes {
  order: Order;
  flow?: AppSquareCustomAttribute | null;
  attemptId?: AppSquareCustomAttribute | null;
  mintStatus?: AppSquareCustomAttribute | null;
  orderMintedTokens?: AppSquareCustomAttribute | null;
  orderBurnedTokens?: AppSquareCustomAttribute | null;
  original_attemptId?: AppSquareCustomAttribute | null;
  burnDate?: AppSquareCustomAttribute | null;
}
