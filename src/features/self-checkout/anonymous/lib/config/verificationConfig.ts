export interface TimeSensitiveStyle {
  thresholdMinutes: number; // Upper bound for this style (e.g., up to X minutes AFTER claim)
  backgroundColor: string; // Tailwind class for the VERIFIED certificate
  borderColor?: string; // Tailwind class
  textColor?: string; // Tailwind class
  label: string; // Label for the VERIFIED state, e.g., "Verified (Recent)"
}

export interface VerificationConfig {
  // Styling for the VERIFIED certificate, based on time elapsed since claim
  // Order these from shortest duration to longest.
  verifiedTimeSensitiveStyles: TimeSensitiveStyle[];

  // How many taps by attendant to trigger the actual verification API call
  tapsToInitiateVerification: number;
  // Max time (ms) between taps for verification to be considered a sequence
  verificationTapWindowMs: number;

  // UX for displaying claim timestamp (easter egg)
  tapsToDisplayTimestamp: {
    count: number;
    windowSeconds: number;
  };

  // Backend: Server-side verification window for the /verify-tap endpoint
  serverVerificationWindowHours: number;

  // JWT Expiry for Step 3 (Claimed) Token
  step3TokenSettings: {
    enableJwtExpiry: boolean;
    jwtExpirySeconds?: number; // e.g., 12 * 60 * 60 for 12 hours. Required if enableJwtExpiry is true.
  };
}

export const verificationConfig: VerificationConfig = {
  verifiedTimeSensitiveStyles: [
    {
      thresholdMinutes: 30,
      backgroundColor: 'bg-green-500', // Tailwind class
      textColor: 'text-white',
      label: 'Verified: Claimed Recently',
    },
    {
      thresholdMinutes: 60,
      backgroundColor: 'bg-yellow-400', // Tailwind class (using 400 for better text contrast potentially)
      textColor: 'text-black',
      label: 'Verified: Claimed Within Hour (Caution)',
    },
    {
      thresholdMinutes: 120,
      backgroundColor: 'bg-orange-500', // Tailwind class
      textColor: 'text-white',
      label: 'Verified: Claimed 1-2 Hours Ago (Verify Carefully)',
    },
    {
      thresholdMinutes: 540, // 9 hours
      backgroundColor: 'bg-red-500', // Tailwind class
      textColor: 'text-white',
      label: 'Verified: Claimed Over 2 Hours Ago (High Scrutiny)',
    },
    {
      thresholdMinutes: Infinity, // Fallback for very old claims
      backgroundColor: 'bg-gray-600', // Tailwind class
      textColor: 'text-white',
      label: 'Verified: Claimed Long Ago (Verify Window May Be Expired)',
    },
  ].sort((a, b) => a.thresholdMinutes - b.thresholdMinutes), // Ensure sorted

  tapsToInitiateVerification: 3,
  verificationTapWindowMs: 500, // 500ms window for multi-tap verification

  tapsToDisplayTimestamp: {
    count: 7,
    windowSeconds: 3,
  },

  serverVerificationWindowHours: 12, // For application-level check in Zod schema

  step3TokenSettings: {
    enableJwtExpiry: true,
    jwtExpirySeconds: 12 * 60 * 60, // 12 hours JWT expiry
  },
};

// Helper function to get the style for the VERIFIED certificate
export function getStyleForVerifiedCertificate(
  minutesElapsedSinceClaim: number
): TimeSensitiveStyle {
  for (const style of verificationConfig.verifiedTimeSensitiveStyles) {
    if (minutesElapsedSinceClaim <= style.thresholdMinutes) {
      return style;
    }
  }
  // Should not be reached if Infinity is the last threshold, but as a fallback:
  return verificationConfig.verifiedTimeSensitiveStyles[
    verificationConfig.verifiedTimeSensitiveStyles.length - 1
  ];
}
