import 'server-only';
import { logServer, LogLevel } from '@/src/lib/logging';
// AppSquareCustomAttribute is imported from App types, e.g.
// import { AppSquareCustomAttribute } from '@/src/features/self-checkout/types';

// New Token Format: S<TokenID>:S<TokenID>:...:AccountStatus
// S = U (Unfrozen) or F (Frozen)
// TokenID = Typically a short unique ID (e.g., 7-char NanoID)
// AccountStatus = U (Unfrozen) or F (Frozen), as the very last character after the last colon.
// Empty unfrozen account: ":U"
// Empty frozen account: ":F"
// Account with one unfrozen token, account unfrozen: "UTkn1aBc:U"

export interface ParsedToken {
  id: string;
  status: 'U' | 'F';
  type: string; // Single character token type
}

export interface ParsedCustomerBalance {
  tokens: ParsedToken[];
  accountStatus: ('U' | 'F') | null; // Null if account status part is missing/invalid from a non-empty string
  isValid: boolean; // True if the string was parsed without structural errors according to expected format
  rawString: string | null | undefined; // Store the original string for logging or debugging
}

/**
 * Parses the customer's clamshellTokenBalance string.
 * Expected format: "S<TokenID(7)><TokenType(1)>:S<TokenID(7)><TokenType(1)>:...:AccountStatus" or ":AccountStatus" for an empty token list.
 * S can be 'U' or 'F'. TokenID is the part after 'U'/'F'. AccountStatus is 'U' or 'F'.
 */
export function parseCustomerBalance(
  balanceString: string | unknown | null | undefined
): ParsedCustomerBalance {
  const result: ParsedCustomerBalance = {
    tokens: [],
    accountStatus: null,
    isValid: false, // Default to false, set to true only on successful parse of a valid format
    rawString: typeof balanceString === 'string' ? balanceString : null,
  };

  if (typeof balanceString !== 'string') {
    logServer(
      LogLevel.DEBUG,
      'parseCustomerBalance',
      'Input is not a string (null, undefined, or other type).',
      { inputType: typeof balanceString }
    );
    return result; // isValid remains false
  }

  if (balanceString.trim() === '') {
    logServer(LogLevel.DEBUG, 'parseCustomerBalance', 'Input string is empty or whitespace.', {
      input: balanceString,
    });
    return result; // isValid remains false, an empty string isn't ":U" or ":F"
  }

  const parts = balanceString.split(':');
  if (parts.length === 0) {
    // This case should ideally not be hit if balanceString is not empty,
    // but as a safeguard.
    logServer(
      LogLevel.WARN,
      'parseCustomerBalance',
      'Splitting the balance string resulted in zero parts (unexpected).',
      { balanceString }
    );
    return result; // isValid remains false
  }

  // The last part is expected to be the account status.
  const accountStatusPart = parts.pop(); // Removes and returns the last element.

  if (accountStatusPart === 'U' || accountStatusPart === 'F') {
    result.accountStatus = accountStatusPart;
  } else {
    logServer(
      LogLevel.WARN,
      'parseCustomerBalance',
      'The last segment of the balance string is not a valid account status ("U" or "F").',
      { balanceString, extractedLastPart: accountStatusPart }
    );
    return result; // isValid remains false because a valid account status is mandatory.
  }

  // At this point, 'parts' contains only the token segments, or it's an array with a single empty string
  // if the original balanceString was just ":U" or ":F".

  // Scenario 1: Original string was ":U" or ":F" (empty token list, valid account status)
  // In this case, 'parts' will be `['']` because `":U".split(':')` is `['', 'U']`, and 'U' was popped.
  if (parts.length === 1 && parts[0] === '') {
    result.isValid = true; // This is a valid format for an empty token list.
    logServer(
      LogLevel.DEBUG,
      'parseCustomerBalance',
      'Parsed an empty token list with a valid account status.',
      { balanceString }
    );
    return result;
  }

  // Scenario 2: Original string had token segments.
  // If parts is empty now AND it wasn't `['']`, it means original string was just "U" or "F" (invalid).
  if (parts.length === 0 && balanceString.length === 1) {
    logServer(
      LogLevel.WARN,
      'parseCustomerBalance',
      'Original string was just a single character account status without preceding colon (invalid format).',
      { balanceString }
    );
    return result; // isValid remains false.
  }

  // Process each remaining part as a token segment "S<TokenID>"
  for (const tokenPart of parts) {
    if (tokenPart.length < 1) {
      // Handles cases like "UToken1::U" (empty part between colons) or "UToken1::FToken2:U"
      // For 9-char tokens, min length is U + 7 ID + 1 Type = 9 chars
      logServer(
        LogLevel.WARN,
        'parseCustomerBalance',
        'Encountered an empty token segment or segment too short.',
        { balanceString, problemPart: tokenPart }
      );
      result.isValid = false; // Mark as invalid and stop processing further.
      return result;
    }
    if (
      tokenPart.length === 9 && // Must be S + 7 char ID + 1 char Type = 9 chars
      (tokenPart.startsWith('U') || tokenPart.startsWith('F'))
    ) {
      const status = tokenPart[0] as 'U' | 'F';
      const id = tokenPart.substring(1, 8); // Next 7 chars are ID
      const type = tokenPart.substring(8, 9); // Last char is type

      if (id.length === 7 && type.length === 1) {
        result.tokens.push({ id, status, type });
      } else {
        logServer(
          LogLevel.WARN,
          'parseCustomerBalance',
          'Malformed token part: ID or Type length incorrect after status prefix.',
          { balanceString, problemPart: tokenPart }
        );
        result.isValid = false; // Mark as invalid and stop processing.
        return result;
      }
    } else {
      logServer(
        LogLevel.WARN,
        'parseCustomerBalance',
        'Malformed token part: Does not start with "U" or "F", or is too short.',
        { balanceString, problemPart: tokenPart, expectedLength: 9 }
      );
      result.isValid = false; // Mark as invalid and stop processing.
      return result;
    }
  }

  // If we've successfully processed all parts and have a valid account status
  result.isValid = true;
  logServer(
    LogLevel.DEBUG,
    'parseCustomerBalance',
    'Successfully parsed customer balance string.',
    {
      balanceString,
      parsedTokensCount: result.tokens.length,
      parsedAccountStatus: result.accountStatus,
    }
  );
  return result;
}

/**
 * Counts all tokens in a parsed balance, regardless of their status.
 * Assumes parseCustomerBalance was called first and its result is passed.
 */
export function countTokensInParsedBalance(parsedBalance: ParsedCustomerBalance): number {
  if (!parsedBalance.isValid) {
    // Depending on strictness, you might throw an error or return 0.
    // Returning 0 might be safer if the goal is just to check a limit
    // and an invalid string implies no countable tokens.
    logServer(
      LogLevel.WARN,
      'countTokensInParsedBalance',
      'Attempted to count tokens from an invalidly parsed balance string. Returning 0.',
      { rawString: parsedBalance.rawString }
    );
    return 0;
  }
  return parsedBalance.tokens.length;
}

/**
 * Verifies if tokens can be burned from the customer's balance.
 * Checks account status and individual status of tokens to be burned.
 * @param parsedCustomerBalance - The result from parseCustomerBalance.
 * @param plainTokenIdsToBurn - Array of plain token IDs (e.g., ["Tkn1aBc", "Tkn2dEf"]) to burn.
 *                              These IDs do NOT include the 'U'/'F' prefix.
 */
export function verifyTokensForBurning(
  parsedCustomerBalance: ParsedCustomerBalance,
  plainTokenIdsToBurn: string[]
): { canBurn: boolean; reason?: string } {
  if (!parsedCustomerBalance.isValid) {
    return {
      canBurn: false,
      reason: 'Customer balance data is malformed or could not be parsed.',
    };
  }

  if (parsedCustomerBalance.accountStatus !== 'U') {
    return {
      canBurn: false,
      reason: `Account is not in an unfrozen state (Account Status: ${parsedCustomerBalance.accountStatus}).`,
    };
  }

  if (!plainTokenIdsToBurn || plainTokenIdsToBurn.length === 0) {
    // This check might be redundant if the calling service ensures this.
    return { canBurn: false, reason: 'No token IDs were specified for burning.' };
  }

  const customerTokensMap = new Map(
    parsedCustomerBalance.tokens.map(token => [token.id, token.status])
  );

  for (const plainId of plainTokenIdsToBurn) {
    if (!customerTokensMap.has(plainId)) {
      return {
        canBurn: false,
        reason: `Token with ID '${plainId}' not found in customer's balance.`,
      };
    }
    const tokenStatusInBalance = customerTokensMap.get(plainId);
    if (tokenStatusInBalance === 'F') {
      return {
        canBurn: false,
        reason: `Token with ID '${plainId}' is individually frozen (Status: F) in customer's balance.`,
      };
    }
    // If status is 'U', it's eligible (so far).
  }

  logServer(
    LogLevel.DEBUG,
    'verifyTokensForBurning',
    'All tokens specified for burning are present and unfrozen, and account is unfrozen.',
    {
      tokenIdsToBurn: plainTokenIdsToBurn,
      accountStatus: parsedCustomerBalance.accountStatus,
    }
  );
  return { canBurn: true };
}

/**
 * Reconstructs the full customer balance string from an array of ParsedToken objects and an account status.
 * @param tokens - Array of ParsedToken objects (each with id and status).
 * Each token in `tokens` is now `ParsedToken { id: string(7), status: 'U'|'F', type: string(1) }`.
 * @param accountStatus - The account status ('U' or 'F').
 * @returns The formatted balance string, e.g., "UTkn1AbC:FTkn2DeX:U" or ":U" if tokens array is empty.
 */
export function reconstructBalanceString(tokens: ParsedToken[], accountStatus: 'U' | 'F'): string {
  const tokenSegments = tokens.map(token => `${token.status}${token.id}${token.type}`); // Add type
  if (tokenSegments.length === 0) {
    return `:${accountStatus}`; // Format for empty token list, e.g., ":U"
  }
  return `${tokenSegments.join(':')}:${accountStatus}`;
}

/**
 * Removes specified tokens (by their plain IDs) from a given array of ParsedToken objects.
 * @param currentTokens - The array of ParsedToken objects representing the current state.
 * @param plainTokenIdsToRemove - An array of plain token IDs (without 'U'/'F' prefix) to remove.
 * @returns A new array of ParsedToken objects with the specified tokens removed.
 */
export function removeTokensFromParsedBalance(
  currentTokens: ParsedToken[],
  plainTokenIdsToRemove: string[]
): ParsedToken[] {
  const idsToRemoveSet = new Set(plainTokenIdsToRemove);
  return currentTokens.filter(token => !idsToRemoveSet.has(token.id));
}

/**
 * Parses the order's token attribute string (e.g., from orderMintedTokens or orderBurnedTokens).
 * Expected format: "S<TokenID(7)><TokenType(1)>:S<TokenID(7)><TokenType(1)>:..." (NO account status suffix).
 * S can be 'U' or 'F'.
 * An empty string or null/undefined is considered a valid (empty) list of tokens for an order attribute.
 */
export function parseOrderTokenAttribute(attributeString: string | unknown | null | undefined): {
  tokens: ParsedToken[];
  isValid: boolean;
} {
  const result: { tokens: ParsedToken[]; isValid: boolean } = {
    tokens: [],
    isValid: false, // Default to false
  };

  // Handle null, undefined, or empty string as a valid empty list of tokens for order attributes
  if (
    attributeString === null ||
    attributeString === undefined ||
    (typeof attributeString === 'string' && attributeString.trim() === '')
  ) {
    result.isValid = true;
    logServer(
      LogLevel.DEBUG,
      'parseOrderTokenAttribute',
      'Input is null, undefined, or empty string; parsed as valid empty token list.',
      { input: attributeString }
    );
    return result;
  }

  if (typeof attributeString !== 'string') {
    logServer(
      LogLevel.WARN,
      'parseOrderTokenAttribute',
      'Input is not a string and not empty/null/undefined.',
      { inputType: typeof attributeString }
    );
    return result; // isValid remains false
  }

  const parts = attributeString.split(':');
  // If splitting an empty string or a string like ":" results in parts like ['']
  // parts could be [''] if attributeString was empty, but that's caught above.
  // if attributeString is just ":", parts will be ['', '']. This is invalid.

  if (parts.length === 0) {
    // This should not happen if attributeString is a non-empty string.
    logServer(
      LogLevel.WARN,
      'parseOrderTokenAttribute',
      'Splitting the attribute string resulted in zero parts.',
      { attributeString }
    );
    return result;
  }

  // If the attributeString was just ":", parts = ["", ""]. This is invalid.
  // Or if it was "UToken1::UToken2", one part will be empty.
  if (parts.some(p => p === '')) {
    if (parts.length === 1 && parts[0] === '') {
      // This means the original string was empty or only whitespace, handled above.
      // This block would only be hit if the above check for empty/null/undefined was removed.
      // For safety, let's assume it's an error here if not caught earlier.
      logServer(
        LogLevel.WARN,
        'parseOrderTokenAttribute',
        'Attribute string resulted in a single empty part after split, implies malformed input or was already handled.',
        { attributeString }
      );
    } else {
      logServer(
        LogLevel.WARN,
        'parseOrderTokenAttribute',
        'Attribute string contains empty segments (e.g., double colons "::") or trailing/leading colons.',
        { attributeString }
      );
    }
    return result; // isValid remains false
  }

  for (const tokenPart of parts) {
    // Each tokenPart must be S<TokenID><TokenType>
    // Length should be 1 (status) + 7 (ID) + 1 (type) = 9
    if (tokenPart.length === 9 && (tokenPart.startsWith('U') || tokenPart.startsWith('F'))) {
      const status = tokenPart[0] as 'U' | 'F';
      const id = tokenPart.substring(1, 8); // 7 chars for ID
      const type = tokenPart.substring(8, 9); // 1 char for Type

      if (id.length === 7 && type.length === 1) {
        result.tokens.push({ id, status, type });
      } else {
        logServer(
          LogLevel.WARN,
          'parseOrderTokenAttribute',
          'Malformed token part in order attribute: ID or Type length incorrect.',
          { problemPart: tokenPart, attributeString }
        );
        result.isValid = false; // Mark as invalid and stop.
        return result;
      }
    } else {
      // Handles parts that are not 9 characters long or don't start with U/F
      logServer(
        LogLevel.WARN,
        'parseOrderTokenAttribute',
        'Malformed token part in order attribute: Does not start with "U"/"F" or is too short.',
        { problemPart: tokenPart, attributeString }
      );
      result.isValid = false; // Mark as invalid and stop.
      return result;
    }
  }

  // If all parts processed successfully
  result.isValid = true;
  logServer(
    LogLevel.DEBUG,
    'parseOrderTokenAttribute',
    'Successfully parsed order token attribute string.',
    {
      attributeString,
      parsedTokensCount: result.tokens.length,
    }
  );
  return result;
}
