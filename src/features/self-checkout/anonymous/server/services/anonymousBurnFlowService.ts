import 'server-only';
import { ApiError } from 'square';

import { logServer, LogLevel } from '@/src/lib/logging';

import {
  parseOrderTokenAttribute,
  parseCustomerBalance,
  verifyTokensForBurning,
  removeTokensFromParsedBalance,
  reconstructBalanceString,
} from '../../lib/squareAttributeUtils';

import {
  retrieveOrder, // To get purchase order details
  retrieveCustomer, // To get customer balance and version
  updateCustomerBalance, // Crucial for atomic update with optimistic locking
  createBurnOrder, // To record the burn
  completeSquareOrder, // To finalize the burn record
  upsertOrderAttribute, // For setting post-burn attributes
  updateOrderState, // For cancelling DRAFT order on failure
} from './anonymousSquareService'; // Adjusted imports

// Env Vars for attribute keys
const ATTR_DEF_KEY_BURNED_TOKENS =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_BURNED_TOKENS || 'orderburnedtokens';
const ATTR_DEF_KEY_BURN_DATE =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_BURN_DATE || 'burnedtimestamp';

interface BurnResult {
  success: boolean;
  burnOrderId?: string;
  error?: string;
  isAlreadyClaimed?: boolean;
}

/**
 * Executes the anonymous burn flow.
 *
 * @param purchaseOrderId - The ID of the original order containing the tokens to be burned.
 * @param attemptId - The attemptId from the JWT, associated with the original purchase.
 * @param locationIdFromToken - The location ID from the JWT, to be used for creating the burn order.
 */
export async function executeAnonymousBurn(
  purchaseOrderId: string,
  attemptId: string, // This is the original purchase attemptId
  locationIdFromToken: string // Added: Location ID for the burn order
): Promise<BurnResult> {
  logServer(LogLevel.INFO, 'executeAnonymousBurn', 'Starting anonymous burn flow', {
    purchaseOrderId,
    originalPurchaseAttemptId: attemptId,
    locationIdForBurnOrder: locationIdFromToken,
  });

  let customerId: string;
  let plainTokenIdsToBurn: string[] = [];
  let tokenStringsForBurnOrderAttribute: string[] = [];
  let draftBurnOrderData:
    | import('@/src/features/self-checkout/anonymous/types').AppOrderWithAttributes
    | null = null;

  try {
    // 1. Fetch Purchase Order Details
    logServer(LogLevel.DEBUG, 'executeAnonymousBurn', 'Step 1: Retrieving purchase order', {
      purchaseOrderId,
    });
    const purchaseOrderData = await retrieveOrder(purchaseOrderId);
    if (!purchaseOrderData?.order) throw new Error(`Purchase order ${purchaseOrderId} not found.`);
    const purchaseOrder = purchaseOrderData.order;
    customerId = purchaseOrder.customerId!;
    if (!customerId) throw new Error('Customer ID missing from purchase order.');

    // Validate that the locationId in the token matches the purchase order's locationId.
    // This is a sanity check, as the purchase order should have been created with this locationId.
    if (purchaseOrder.locationId !== locationIdFromToken) {
      logServer(
        LogLevel.ERROR,
        'executeAnonymousBurn',
        'Location ID mismatch between token and purchase order.',
        {
          tokenLocationId: locationIdFromToken,
          purchaseOrderLocationId: purchaseOrder.locationId,
          purchaseOrderId,
        }
      );
      throw new Error('Location mismatch during burn process. Integrity check failed.');
    }

    const mintedTokensValueFromOrder = purchaseOrderData.orderMintedTokens?.value;
    const parsedOrderTokens = parseOrderTokenAttribute(mintedTokensValueFromOrder);
    if (!parsedOrderTokens.isValid || parsedOrderTokens.tokens.length === 0) {
      throw new Error('Invalid or no tokens found on purchase order.');
    }
    plainTokenIdsToBurn = parsedOrderTokens.tokens.map(t => t.id);
    tokenStringsForBurnOrderAttribute = parsedOrderTokens.tokens.map(
      t => `${t.status}${t.id}${t.type}`
    ); // Ensure 9-char format
    logServer(LogLevel.DEBUG, 'executeAnonymousBurn', 'Purchase order details extracted', {
      customerId,
      tokensToBurnCount: plainTokenIdsToBurn.length,
    });

    // 2. Validate Customer Account State & Token Availability
    logServer(
      LogLevel.DEBUG,
      'executeAnonymousBurn',
      `Step 2: Retrieving customer ${customerId} for validation.`
    );
    const customerData = await retrieveCustomer(customerId);
    if (!customerData?.customer) throw new Error(`Customer ${customerId} not found.`);

    const rawBalanceAttribute = customerData.clamshellTokenBalance;

    if (!rawBalanceAttribute || typeof rawBalanceAttribute.value !== 'string') {
      throw new Error('Customer token balance attribute is missing or invalid (critical).');
    }
    const currentBalanceString = rawBalanceAttribute.value;
    const parsedCustomerBalance = parseCustomerBalance(currentBalanceString);

    if (!parsedCustomerBalance.isValid)
      throw new Error(`Customer ${customerId} has a malformed balance: '${currentBalanceString}'.`);
    if (parsedCustomerBalance.accountStatus === 'F')
      return { success: false, error: 'Account is frozen.' };
    if (parsedCustomerBalance.accountStatus !== 'U')
      return { success: false, error: 'Account not in a valid state.' };

    const verificationResult = verifyTokensForBurning(parsedCustomerBalance, plainTokenIdsToBurn);
    if (!verificationResult.canBurn) {
      if (verificationResult.reason?.includes('not found')) {
        return {
          success: false,
          error: 'Items already claimed (tokens not found).',
          isAlreadyClaimed: true,
        };
      }
      return { success: false, error: verificationResult.reason || 'Tokens cannot be claimed.' };
    }
    logServer(LogLevel.INFO, 'executeAnonymousBurn', 'Customer and token validation passed.');

    // 3. Create DRAFT Burn Order & Set Initial Attributes
    logServer(LogLevel.DEBUG, 'executeAnonymousBurn', 'Step 3: Creating DRAFT burn order.', {
      originalPurchaseAttemptId: attemptId,
      locationIdForBurnOrder: locationIdFromToken,
    });
    draftBurnOrderData = await createBurnOrder(
      customerId,
      [], // tokenStringsForBurnOrderAttribute is not used for *initial* attributes here
      'anonymous_burn',
      attemptId,
      false,
      locationIdFromToken // Pass the locationId from the token
    );
    if (!draftBurnOrderData?.order?.id) {
      throw new Error('Failed to create DRAFT burn order record.');
    }
    const burnOrderId = draftBurnOrderData.order.id;
    const currentBurnOrderVersion = draftBurnOrderData.order.version;
    if (currentBurnOrderVersion === undefined) {
      throw new Error('DRAFT Burn order created without a version.');
    }
    logServer(LogLevel.INFO, 'executeAnonymousBurn', 'DRAFT burn order created successfully.', {
      burnOrderId,
      version: currentBurnOrderVersion,
      locationId: draftBurnOrderData.order.locationId,
    });

    // 4. Update Customer Balance
    logServer(
      LogLevel.DEBUG,
      'executeAnonymousBurn',
      `Step 4: Updating customer balance for ${customerId}.`
    );
    const remainingTokensArray = removeTokensFromParsedBalance(
      parsedCustomerBalance.tokens,
      plainTokenIdsToBurn
    );
    const newBalanceString = reconstructBalanceString(remainingTokensArray, 'U'); // 'U' is the account status

    try {
      const updatedCustomerData = await updateCustomerBalance(customerId, newBalanceString);
      if (!updatedCustomerData) {
        throw new Error('Failed to update customer balance after retries (returned null).');
      }
      logServer(LogLevel.INFO, 'executeAnonymousBurn', 'Customer balance updated successfully.', {
        customerId,
        newBalance: newBalanceString,
      });
    } catch (balanceUpdateError: any) {
      logServer(
        LogLevel.ERROR,
        'executeAnonymousBurn',
        'Failed to update customer balance. Attempting to cancel DRAFT burn order.',
        { customerId, burnOrderId, error: balanceUpdateError.message }
      );
      if (burnOrderId && currentBurnOrderVersion !== undefined) {
        try {
          // updateOrderState will fetch the order's actual locationId
          await updateOrderState(burnOrderId, 'CANCELED');
          logServer(
            LogLevel.INFO,
            'executeAnonymousBurn',
            'Successfully CANCELED DRAFT burn order due to balance update failure.',
            { burnOrderId }
          );
        } catch (cancelError: any) {
          logServer(
            LogLevel.ERROR,
            'executeAnonymousBurn',
            'CRITICAL: Failed to CANCEL DRAFT burn order after balance update failure.',
            { burnOrderId, cancelError: cancelError.message }
          );
        }
      }
      throw balanceUpdateError;
    }

    // 5. Set Post-Burn Attributes on Burn Order
    logServer(
      LogLevel.DEBUG,
      'executeAnonymousBurn',
      `Step 5: Setting post-burn attributes on order ${burnOrderId}.`
    );
    try {
      await upsertOrderAttribute(
        burnOrderId,
        ATTR_DEF_KEY_BURNED_TOKENS!,
        tokenStringsForBurnOrderAttribute.join(':')
      );

      await upsertOrderAttribute(burnOrderId, ATTR_DEF_KEY_BURN_DATE!, new Date().toISOString());

      logServer(LogLevel.INFO, 'executeAnonymousBurn', 'Post-burn attributes set successfully.', {
        burnOrderId,
      });
    } catch (postBurnAttrError: any) {
      logServer(
        LogLevel.ERROR,
        'executeAnonymousBurn',
        'CRITICAL: Balance updated, but failed setting post-burn attributes. Burn order completion will be attempted.',
        { burnOrderId, error: postBurnAttrError.message }
      );
    }

    // 6. Complete Burn Order
    logServer(
      LogLevel.DEBUG,
      'executeAnonymousBurn',
      `Step 6: Completing burn order ${burnOrderId}.`
    );
    const completedOrderData = await completeSquareOrder(burnOrderId);
    if (!completedOrderData?.order) {
      logServer(
        LogLevel.ERROR,
        'executeAnonymousBurn',
        'CRITICAL: Balance updated, attributes (maybe) set, but FAILED to complete burn order.',
        { burnOrderId }
      );
      return {
        success: true,
        burnOrderId,
        error: 'Burn record finalization pending. Ref: ' + burnOrderId,
      };
    }
    logServer(LogLevel.INFO, 'executeAnonymousBurn', 'Burn order COMPLETED successfully.', {
      burnOrderId,
      finalVersion: completedOrderData.order.version,
    });

    // 7. Return Success
    return { success: true, burnOrderId };
  } catch (error: any) {
    logServer(LogLevel.ERROR, 'executeAnonymousBurn', 'Anonymous burn flow failed at some stage.', {
      purchaseOrderId,
      originalPurchaseAttemptId: attemptId,
      locationIdFromToken,
      draftBurnOrderId: draftBurnOrderData?.order?.id,
      error: (error as ApiError)?.errors || error.message,
      stack: error.stack,
    });

    if (
      error.message?.includes('already claimed') ||
      error.message?.includes('Items already claimed')
    ) {
      return { success: false, error: error.message, isAlreadyClaimed: true };
    }
    return {
      success: false,
      error: error.message || 'The token claim process encountered an unexpected error.',
      burnOrderId: draftBurnOrderData?.order?.id,
    };
  }
}
