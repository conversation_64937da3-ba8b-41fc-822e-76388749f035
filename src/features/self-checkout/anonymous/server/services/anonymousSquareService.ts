import 'server-only';
import {
  ApiError,
  ApiResponse,
  CatalogObject,
  CreateCustomerRequest,
  CreateOrderRequest,
  Customer,
  Location,
  Order,
  SearchCatalogItemsRequest,
  OrderLineItem,
  SearchCustomersRequest,
  UpdateOrderRequest,
  UpsertCustomerCustomAttributeRequest,
  UpsertOrderCustomAttributeRequest,
  RetrieveCustomerCustomAttributeResponse,
  RetrieveCustomerResponse,
  RetrieveOrderCustomAttributeResponse,
  PaymentLink,
  SearchCatalogItemsResponse,
  // Needed for explicit type assertion later
  CatalogItemVariation,
  CatalogCustomAttributeValue,
} from 'square';
import { v4 as uuidv4 } from 'uuid';

import { logServer, LogLevel } from '@/src/lib/logging';

import {
  AppSquareCustomAttribute,
  AppCustomerWithAttributes,
  AppOrderWithAttributes,
} from '../../types';
import { client as squareClient } from '../_squareClient';

// --- Configuration ---
const DEFAULT_SQUARE_LOCATION_ID = process.env.SELF_CHECKOUT_SQUARE_LOCATION_ID;

const ATTR_DEF_KEY_CLAMSHELL_BALANCE =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_CLAMSHELL_BALANCE || 'clamshelltokenbalance';
const ATTR_DEF_KEY_FLOW = process.env.SELF_CHECKOUT_ATTR_DEF_KEY_FLOW || 'flowcheckouttype';
const ATTR_DEF_KEY_ATTEMPT_ID =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_ATTEMPT_ID || 'attempttokenid';
const ATTR_DEF_KEY_MINT_STATUS =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_MINT_STATUS || 'tokenmintstatus';
const ATTR_DEF_KEY_ORDER_MINTED_TOKENS =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_ORDER_MINTED_TOKENS || 'ordermintedtokens';
const ATTR_DEF_KEY_BURNED_TOKENS =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_BURNED_TOKENS || 'orderburnedtokens';
const ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID || 'originalpurchaseattemptid';
const ATTR_DEF_KEY_BURN_DATE =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_BURN_DATE || 'burnedtimestamp';

const requiredEnvVarKeys = {
  DEFAULT_SQUARE_LOCATION_ID,
  ATTR_DEF_KEY_CLAMSHELL_BALANCE,
  ATTR_DEF_KEY_FLOW,
  ATTR_DEF_KEY_ATTEMPT_ID,
  ATTR_DEF_KEY_MINT_STATUS,
  ATTR_DEF_KEY_ORDER_MINTED_TOKENS,
  ATTR_DEF_KEY_BURNED_TOKENS,
  ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID,
  ATTR_DEF_KEY_BURN_DATE,
};

for (const [key, value] of Object.entries(requiredEnvVarKeys)) {
  if (!value) {
    const errorMessage = `Server configuration error: Missing environment variable for Square key: ${key}`;
    logServer(LogLevel.ERROR, 'anonymousSquareService.config', errorMessage);
    throw new Error(errorMessage);
  }
}

if (!squareClient) {
  const clientErrorMessage =
    'Square client not initialized due to missing configuration. Anonymous checkout cannot function.';
  logServer(LogLevel.ERROR, 'anonymousSquareService.config', clientErrorMessage);
  throw new Error(clientErrorMessage);
}

const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 250;

async function upsertCustomerAttribute(
  customerId: string,
  attributeKey: string,
  value: unknown,
  version?: number
): Promise<AppSquareCustomAttribute | null> {
  if (!squareClient) throw new Error('Square client not initialized for upsertCustomerAttribute.');
  if (!attributeKey) throw new Error(`Attribute key is required for upsertCustomerAttribute.`);

  const request: UpsertCustomerCustomAttributeRequest = {
    customAttribute: {
      key: attributeKey,
      value: value,
      ...(version !== undefined && { version: version }),
    },
    idempotencyKey: uuidv4(),
  };

  try {
    const response = await squareClient.customerCustomAttributesApi.upsertCustomerCustomAttribute(
      customerId,
      attributeKey,
      request
    );
    return response.result.customAttribute ?? null;
  } catch (error) {
    logServer(LogLevel.ERROR, 'upsertCustomerAttribute', 'Failed to upsert customer attribute.', {
      customerId,
      attributeKey,
      valueAttempted: value,
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
    });
    throw error;
  }
}

export async function upsertOrderAttribute(
  orderId: string,
  attributeKey: string,
  value: unknown,
  version?: number
): Promise<AppSquareCustomAttribute | null> {
  if (!squareClient) throw new Error('Square client not initialized for upsertOrderAttribute.');
  if (!attributeKey) throw new Error(`Attribute key is required for upsertOrderAttribute.`);

  const request: UpsertOrderCustomAttributeRequest = {
    customAttribute: {
      key: attributeKey,
      value: value,
      ...(version !== undefined && { version: version }),
    },
    idempotencyKey: uuidv4(),
  };

  try {
    const response = await squareClient.orderCustomAttributesApi.upsertOrderCustomAttribute(
      orderId,
      attributeKey,
      request
    );
    return response.result.customAttribute ?? null;
  } catch (error) {
    logServer(LogLevel.ERROR, 'upsertOrderAttribute', 'Failed to upsert order attribute.', {
      orderId,
      attributeKey,
      valueAttempted: value,
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
    });
    throw error;
  }
}

async function retrieveCustomerAttribute(
  customerId: string,
  attributeKey: string
): Promise<AppSquareCustomAttribute | null> {
  if (!squareClient)
    throw new Error('Square client not initialized for retrieveCustomerAttribute.');
  if (!attributeKey) {
    logServer(
      LogLevel.WARN,
      'retrieveCustomerAttribute',
      'Missing attribute key, cannot retrieve.'
    );
    return null;
  }

  try {
    const response: ApiResponse<RetrieveCustomerCustomAttributeResponse> =
      await squareClient.customerCustomAttributesApi.retrieveCustomerCustomAttribute(
        customerId,
        attributeKey,
        false
      );
    return response.result.customAttribute ?? null;
  } catch (error) {
    const apiError = error as ApiError;
    if (apiError?.statusCode === 404) {
      logServer(LogLevel.DEBUG, 'retrieveCustomerAttribute', 'Attribute not found (404).', {
        customerId,
        attributeKey,
      });
      return null;
    }
    logServer(
      LogLevel.ERROR,
      'retrieveCustomerAttribute',
      'Failed to retrieve customer attribute.',
      {
        customerId,
        attributeKey,
        errorDetails: apiError?.errors || (error as Error).message,
      }
    );
    throw error;
  }
}

async function retrieveOrderAttribute(
  orderId: string,
  attributeKey: string
): Promise<AppSquareCustomAttribute | null> {
  if (!squareClient) throw new Error('Square client not initialized for retrieveOrderAttribute.');
  if (!attributeKey) {
    logServer(LogLevel.WARN, 'retrieveOrderAttribute', 'Missing attribute key, cannot retrieve.');
    return null;
  }

  try {
    const response: ApiResponse<RetrieveOrderCustomAttributeResponse> =
      await squareClient.orderCustomAttributesApi.retrieveOrderCustomAttribute(
        orderId,
        attributeKey,
        undefined,
        false
      );
    return response.result.customAttribute ?? null;
  } catch (error) {
    const apiError = error as ApiError;
    if (apiError?.statusCode === 404) {
      logServer(LogLevel.DEBUG, 'retrieveOrderAttribute', 'Attribute not found (404).', {
        orderId,
        attributeKey,
      });
      return null;
    }
    logServer(LogLevel.ERROR, 'retrieveOrderAttribute', 'Failed to retrieve order attribute.', {
      orderId,
      attributeKey,
      errorDetails: apiError?.errors || (error as Error).message,
    });
    throw error;
  }
}

export async function listSquareLocations(): Promise<Location[] | null> {
  if (!squareClient) {
    logServer(LogLevel.ERROR, 'listSquareLocations', 'Square client not initialized.');
    return null;
  }
  try {
    logServer(LogLevel.DEBUG, 'listSquareLocations', 'Fetching locations from Square.');
    const response = await squareClient.locationsApi.listLocations();
    const locations = response.result.locations;
    if (locations && locations.length > 0) {
      logServer(
        LogLevel.INFO,
        'listSquareLocations',
        `Successfully fetched ${locations.length} locations.`
      );
      return locations;
    }
    logServer(LogLevel.WARN, 'listSquareLocations', 'No locations found for the merchant.');
    return [];
  } catch (error) {
    logServer(LogLevel.ERROR, 'listSquareLocations', 'Failed to list Square locations.', {
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
    });
    return null;
  }
}

/**
 * Searches for catalog items.
 * Assumes that if variations are returned nested under parent items in the search,
 * their customAttributeValues are sufficiently populated.
 * @param categoryId The ID of the Square Category to search within.
 * @param locationId The Square Location ID for which items should be enabled.
 * @returns Promise resolving to an array of CatalogObject (Item Variations with potentially populated custom attributes) or null.
 */
export async function searchCatalogItemsByLocationAndCategory(
  categoryId: string,
  locationId: string
): Promise<CatalogObject[] | null> {
  const functionName = 'searchCatalogItemsByLocationAndCategory_Optimized';
  if (!squareClient) {
    logServer(LogLevel.ERROR, functionName, 'Square client not initialized.');
    return null;
  }
  if (!categoryId || !locationId) {
    logServer(LogLevel.ERROR, functionName, 'Category ID and Location ID are required.');
    return null;
  }

  logServer(LogLevel.INFO, functionName, 'Starting optimized catalog search', {
    categoryId,
    locationId,
  });

  const request: SearchCatalogItemsRequest = {
    categoryIds: [categoryId],
    enabledLocationIds: [locationId],
    limit: 100,
  };
  logServer(LogLevel.DEBUG, functionName, 'SearchCatalogItemsRequest prepared.', { request });

  const itemVariationsFromSearch: CatalogObject[] = [];

  try {
    const response: ApiResponse<SearchCatalogItemsResponse> =
      await squareClient.catalogApi.searchCatalogItems(request);
    logServer(LogLevel.DEBUG, functionName, 'SearchCatalogItems API call successful.', {
      hasItems: !!response.result.items,
      itemCount: response.result.items?.length,
      hasMatchedVariationIds: !!response.result.matchedVariationIds,
      matchedVariationIdCount: response.result.matchedVariationIds?.length,
    });

    const parentItems = response.result.items;

    if (parentItems && parentItems.length > 0) {
      logServer(
        LogLevel.INFO,
        functionName,
        `Processing ${parentItems.length} parent items from search result.`
      );
      for (const item of parentItems) {
        if (item.type === 'ITEM' && item.itemData?.variations) {
          logServer(
            LogLevel.DEBUG,
            functionName,
            `Processing item '${item.itemData.name}' (ID: ${item.id}) with ${item.itemData.variations.length} variations.`
          );
          for (const shallowVariation of item.itemData.variations) {
            if (shallowVariation.id) {
              // Construct a CatalogObject that represents this variation,
              // including its itemVariationData and importantly, its customAttributeValues if present.
              // The 'shallowVariation' itself is of type CatalogItemVariation.
              // We need to package it into a CatalogObject structure for consistency with retrieveCatalogObject.
              // The crucial part is whether `shallowVariation.customAttributeValues` is populated by the search.
              // Based on the provided cURL example, it IS.

              // Create a CatalogObject of type ITEM_VARIATION from the shallowVariation data
              const variationAsCatalogObject: CatalogObject = {
                type: 'ITEM_VARIATION', // This is the type we expect for a variation
                id: shallowVariation.id,
                updatedAt: shallowVariation.updatedAt,
                version: shallowVariation.version,
                isDeleted: shallowVariation.isDeleted,
                presentAtAllLocations: shallowVariation.presentAtAllLocations,
                presentAtLocationIds: shallowVariation.presentAtLocationIds,
                absentAtLocationIds: shallowVariation.absentAtLocationIds,
                // The actual variation data
                itemVariationData: shallowVariation as CatalogItemVariation, // Cast, as it is itemVariationData
                // If custom attributes are directly on the shallowVariation from search (as per example)
                customAttributeValues: (shallowVariation as any).customAttributeValues as
                  | Record<string, CatalogCustomAttributeValue>
                  | null
                  | undefined,
                // Include parent item data for context if needed by initializationService
                itemData: item.itemData, // shallowly include parent item data
              };

              logServer(
                LogLevel.DEBUG,
                functionName,
                `Extracted variation ${variationAsCatalogObject.id} from parent item ${item.id}.`,
                {
                  hasCustomAttributesDirectly: !!variationAsCatalogObject.customAttributeValues,
                  // customAttributesKeys: variationAsCatalogObject.customAttributeValues ? Object.keys(variationAsCatalogObject.customAttributeValues) : 'N/A'
                }
              );
              itemVariationsFromSearch.push(variationAsCatalogObject);
            } else {
              logServer(
                LogLevel.WARN,
                functionName,
                `Shallow variation from item ${item.id} missing an ID. Skipping.`
              );
            }
          }
        } else {
          logServer(
            LogLevel.DEBUG,
            functionName,
            `Skipping item ID ${item.id} as it's not type ITEM or has no variations. Type: ${item.type}`
          );
        }
      }
    } else {
      logServer(LogLevel.INFO, functionName, 'No parent items found in search response.');
      // It's possible `matched_variation_ids` could still have items if the parent items weren't returned
      // but their variations matched. However, the cURL example shows items being returned.
      // If `items` is empty, and `matched_variation_ids` is also empty, then there's nothing.
      if (
        !response.result.matchedVariationIds ||
        response.result.matchedVariationIds.length === 0
      ) {
        return [];
      }
    }

    // If we primarily relied on iterating parent items, `itemVariationsFromSearch` is populated.
    // If `matched_variation_ids` was the source AND it had IDs not covered by iterating parent items (unlikely based on API behavior),
    // one might consider an additional step to fetch those. But the cURL example suggests iterating parent items' variations is sufficient if they are returned.
    // For this optimization, we assume the nested variations in `items` are the source of truth.

    logServer(
      LogLevel.INFO,
      functionName,
      `Optimization complete. Returning ${itemVariationsFromSearch.length} item variations directly from search results.`
    );
    return itemVariationsFromSearch;
  } catch (error) {
    logServer(
      LogLevel.ERROR,
      functionName,
      'SearchCatalogItems API call failed OR error during subsequent processing.',
      {
        categoryId,
        locationId,
        errorDetails: (error as ApiError)?.errors || (error as Error).message,
        stack: (error as Error).stack,
      }
    );
    return null;
  }
}

export async function retrieveCatalogObject(
  objectId: string,
  includeRelatedObjects: boolean = false
): Promise<CatalogObject | null> {
  const functionName = 'anonymousSquareService.retrieveCatalogObject';
  if (!squareClient) throw new Error('Square client not initialized for retrieveCatalogObject.');
  logServer(LogLevel.DEBUG, functionName, `Retrieving catalog object ${objectId}`, {
    includeRelatedObjects,
  });
  try {
    const response = await squareClient.catalogApi.retrieveCatalogObject(
      objectId,
      includeRelatedObjects
    );
    const retrievedObject = response.result.object ?? null;
    if (retrievedObject) {
      logServer(
        LogLevel.DEBUG,
        functionName,
        `Successfully retrieved catalog object ${objectId}. Type: ${retrievedObject.type}`
      );
    } else {
      logServer(
        LogLevel.WARN,
        functionName,
        `Catalog object ${objectId} not found by API (null response).`
      );
    }
    return retrievedObject;
  } catch (error) {
    logServer(LogLevel.ERROR, functionName, `Failed to retrieve catalog object ${objectId}.`, {
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
      stack: (error as Error).stack,
    });
    if (error instanceof ApiError && error.statusCode === 404) {
      logServer(LogLevel.WARN, functionName, `Catalog object ${objectId} not found (404).`);
      return null;
    }
    throw error;
  }
}

// ... (rest of the file remains unchanged: findCustomerByEmail, retrieveCustomer, createCustomer, updateCustomerBalance, createPurchaseOrder, createBurnOrder, retrieveOrder, updateOrderState, completeSquareOrder, findCompletedOrderByCustomAttribute, createSquareCheckoutLink) ...
// Ensure that the unchanged parts are included below if this file is to be fully replaced.
// For brevity in this response, I'm omitting them as they were not part of this specific optimization task.
// Assume they follow here.
export async function findCustomerByEmail(email: string): Promise<Customer | null> {
  if (!squareClient) throw new Error('Square client not initialized for findCustomerByEmail.');
  const request: SearchCustomersRequest = {
    query: {
      filter: { emailAddress: { exact: email } },
      sort: { field: 'CREATED_AT', order: 'DESC' },
    },
    limit: BigInt(1),
  };
  try {
    const response = await squareClient.customersApi.searchCustomers(request);
    return response.result.customers?.[0] ?? null;
  } catch (error) {
    logServer(
      LogLevel.ERROR,
      'anonymousSquareService.findCustomerByEmail',
      'Error searching for customer by email.',
      { email, errorDetails: (error as ApiError)?.errors || (error as Error).message }
    );
    return null;
  }
}

export async function retrieveCustomer(
  customerId: string
): Promise<AppCustomerWithAttributes | null> {
  if (!squareClient) throw new Error('Square client not initialized for retrieveCustomer.');
  if (!ATTR_DEF_KEY_CLAMSHELL_BALANCE) {
    const configErrorMsg = 'Configuration error: Clamshell Balance Attribute Key is missing.';
    logServer(LogLevel.ERROR, 'retrieveCustomer', configErrorMsg);
    throw new Error(configErrorMsg);
  }
  try {
    const customerResponse: ApiResponse<RetrieveCustomerResponse> =
      await squareClient.customersApi.retrieveCustomer(customerId);
    const customer = customerResponse.result.customer;
    if (!customer) {
      logServer(LogLevel.WARN, 'retrieveCustomer', `Customer with ID ${customerId} not found.`, {
        customerId,
      });
      return null;
    }

    let balanceAttribute: AppSquareCustomAttribute | null = null;
    try {
      balanceAttribute = await retrieveCustomerAttribute(
        customerId,
        ATTR_DEF_KEY_CLAMSHELL_BALANCE
      );
    } catch (attrError: any) {
      logServer(
        LogLevel.WARN,
        'retrieveCustomer',
        'Failed to retrieve clamshellTokenBalance attribute during customer retrieval. Proceeding without it.',
        {
          customerId,
          attributeKey: ATTR_DEF_KEY_CLAMSHELL_BALANCE,
          error: attrError.message,
        }
      );
    }
    return { customer, clamshellTokenBalance: balanceAttribute };
  } catch (error) {
    logServer(LogLevel.ERROR, 'retrieveCustomer', 'Error retrieving customer.', {
      customerId,
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
    });
    return null;
  }
}

export async function createCustomer(
  email: string,
  referenceId?: string
): Promise<AppCustomerWithAttributes | null> {
  if (!squareClient) throw new Error('Square client not initialized for createCustomer.');
  if (!ATTR_DEF_KEY_CLAMSHELL_BALANCE) {
    const configErrorMsg =
      'Configuration error: Clamshell Balance Attribute Key is missing for createCustomer.';
    logServer(LogLevel.ERROR, 'createCustomer', configErrorMsg);
    throw new Error(configErrorMsg);
  }
  const request: CreateCustomerRequest = {
    idempotencyKey: uuidv4(),
    emailAddress: email,
    ...(referenceId && { referenceId: referenceId }),
  };
  try {
    const response = await squareClient.customersApi.createCustomer(request);
    const customer = response.result.customer;
    if (!customer?.id) {
      logServer(
        LogLevel.ERROR,
        'createCustomer',
        'Failed to create customer or customer ID is missing in response.',
        { email }
      );
      throw new Error('Customer creation failed: No customer ID returned.');
    }

    let balanceAttribute: AppSquareCustomAttribute | null = null;
    try {
      balanceAttribute = await upsertCustomerAttribute(
        customer.id,
        ATTR_DEF_KEY_CLAMSHELL_BALANCE,
        ':U'
      );
      if (!balanceAttribute) {
        logServer(
          LogLevel.WARN,
          'createCustomer',
          'Failed to initialize clamshellTokenBalance attribute for new customer, though customer was created.',
          { customerId: customer.id }
        );
      }
    } catch (attrError: any) {
      logServer(
        LogLevel.WARN,
        'createCustomer',
        'Error initializing clamshellTokenBalance attribute for new customer.',
        {
          customerId: customer.id,
          error: attrError.message,
        }
      );
    }
    return { customer, clamshellTokenBalance: balanceAttribute };
  } catch (error) {
    logServer(LogLevel.ERROR, 'createCustomer', 'Error creating customer.', {
      email,
      referenceId,
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
    });
    return null;
  }
}

export async function updateCustomerBalance(
  customerId: string,
  newBalanceString: string
): Promise<AppCustomerWithAttributes | null> {
  if (!squareClient) throw new Error('Square client not initialized for updateCustomerBalance.');
  if (!ATTR_DEF_KEY_CLAMSHELL_BALANCE) {
    const configErrorMsg =
      'Configuration error: Clamshell Balance Attribute Key is missing for updateCustomerBalance.';
    logServer(LogLevel.ERROR, 'updateCustomerBalance', configErrorMsg);
    throw new Error(configErrorMsg);
  }

  let lastKnownAttributeVersion: number | undefined;

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      const currentAttribute = await retrieveCustomerAttribute(
        customerId,
        ATTR_DEF_KEY_CLAMSHELL_BALANCE
      );
      lastKnownAttributeVersion = currentAttribute?.version;

      logServer(
        LogLevel.DEBUG,
        'updateCustomerBalance',
        `Attempt ${attempt}: Upserting balance for customer ${customerId}.`,
        {
          newBalanceString,
          version: lastKnownAttributeVersion,
        }
      );

      const upsertedAttribute = await upsertCustomerAttribute(
        customerId,
        ATTR_DEF_KEY_CLAMSHELL_BALANCE,
        newBalanceString,
        lastKnownAttributeVersion
      );

      if (upsertedAttribute) {
        logServer(
          LogLevel.INFO,
          'updateCustomerBalance',
          `Successfully upserted balance for customer ${customerId} on attempt ${attempt}.`,
          { newBalanceString }
        );
        const updatedCustomerData = await retrieveCustomer(customerId);
        if (!updatedCustomerData) {
          logServer(
            LogLevel.ERROR,
            'updateCustomerBalance',
            `Failed to retrieve customer ${customerId} data *after* successful balance attribute upsert.`,
            { customerId }
          );
          throw new Error('Failed to confirm customer data post-balance update.');
        }
        return updatedCustomerData;
      } else {
        logServer(
          LogLevel.ERROR,
          'updateCustomerBalance',
          `Upsert attempt ${attempt} for customer ${customerId} returned null, indicating unexpected failure.`,
          { newBalanceString }
        );
        throw new Error(`Balance update attempt ${attempt} failed unexpectedly.`);
      }
    } catch (error: any) {
      const apiError = error as ApiError;
      const isVersionMismatch = apiError.errors?.some(
        (e: any) => e.code === 'VERSION_MISMATCH' || e.detail?.includes('version_mismatch')
      );

      if (isVersionMismatch && attempt < MAX_RETRIES) {
        logServer(
          LogLevel.WARN,
          'updateCustomerBalance',
          `Version mismatch on attempt ${attempt} for customer ${customerId}. Retrying...`,
          { newBalanceString }
        );
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS * attempt));
      } else {
        logServer(
          LogLevel.ERROR,
          'updateCustomerBalance',
          `Failed to update customer balance for ${customerId} after ${attempt} attempts.`,
          {
            newBalanceString,
            finalError: apiError.errors || error.message,
          }
        );
        throw error;
      }
    }
  }
  logServer(
    LogLevel.ERROR,
    'updateCustomerBalance',
    `Failed to update balance for customer ${customerId} after all ${MAX_RETRIES} retries due to persistent version conflicts.`
  );
  return null;
}

export async function createPurchaseOrder(
  customerId: string,
  selectedItems: OrderLineItem[],
  attemptIdForOrderAttribute: string
): Promise<AppOrderWithAttributes | null> {
  logServer(
    LogLevel.WARN,
    'createPurchaseOrder',
    'This function may be deprecated or not used by anonymous flow. Review its usage.'
  );
  if (!squareClient || !DEFAULT_SQUARE_LOCATION_ID)
    throw new Error('Square Client or Default Location ID is missing for createPurchaseOrder.');
  if (!ATTR_DEF_KEY_FLOW || !ATTR_DEF_KEY_ATTEMPT_ID || !ATTR_DEF_KEY_MINT_STATUS) {
    logServer(
      LogLevel.ERROR,
      'createPurchaseOrder',
      'One or more required Attribute Definition Keys are missing in environment variables.'
    );
    throw new Error('Server configuration error for purchase order attributes.');
  }

  const request: CreateOrderRequest = {
    idempotencyKey: uuidv4(),
    order: {
      locationId: DEFAULT_SQUARE_LOCATION_ID,
      customerId: customerId,
      lineItems: selectedItems,
      state: 'DRAFT',
      source: { name: 'Anonymous Self Checkout Mint' },
    },
  };
  try {
    const response = await squareClient.ordersApi.createOrder(request);
    const order = response.result.order;
    if (!order?.id) {
      logServer(
        LogLevel.ERROR,
        'createPurchaseOrder',
        'Order creation via API failed or returned no order ID.'
      );
      throw new Error('Order creation failed (no ID returned from Square).');
    }

    const [flowAttr, attemptIdAttr, mintStatusAttr] = await Promise.all([
      upsertOrderAttribute(order.id, ATTR_DEF_KEY_FLOW, 'anonymous'),
      upsertOrderAttribute(order.id, ATTR_DEF_KEY_ATTEMPT_ID, attemptIdForOrderAttribute),
      upsertOrderAttribute(order.id, ATTR_DEF_KEY_MINT_STATUS, 'mintable'),
    ]);

    if (!flowAttr || !attemptIdAttr || !mintStatusAttr) {
      logServer(
        LogLevel.WARN,
        'createPurchaseOrder',
        'Failed to set one or more crucial attributes on the purchase order.',
        { orderId: order.id }
      );
    }
    return {
      order,
      flow: flowAttr,
      attemptId: attemptIdAttr,
      mintStatus: mintStatusAttr,
      orderMintedTokens: null,
    };
  } catch (error) {
    logServer(
      LogLevel.ERROR,
      'createPurchaseOrder',
      'Error creating purchase order or its attributes.',
      {
        customerId,
        attemptIdForOrderAttribute,
        errorDetails: (error as ApiError)?.errors || (error as Error).message,
      }
    );
    return null;
  }
}

export async function createBurnOrder(
  customerId: string,
  _tokenStringsForAttribute: string[],
  flow: string,
  originalPurchaseAttemptId: string,
  createAsCompleted: boolean = false,
  locationIdForBurnOrder: string
): Promise<AppOrderWithAttributes | null> {
  if (!squareClient) throw new Error('Square Client missing for createBurnOrder.');
  if (!locationIdForBurnOrder) throw new Error('Location ID is required for createBurnOrder.');

  if (!ATTR_DEF_KEY_FLOW || !ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID) {
    logServer(
      LogLevel.ERROR,
      'createBurnOrder',
      'Missing required Attribute Definition Keys for Burn Order (initial attributes part).'
    );
    throw new Error('Server configuration error for burn order attributes.');
  }

  const request: CreateOrderRequest = {
    idempotencyKey: uuidv4(),
    order: {
      locationId: locationIdForBurnOrder,
      customerId: customerId,
      lineItems: [],
      state: createAsCompleted ? 'COMPLETED' : 'DRAFT',
      source: { name: 'Anonymous Self Checkout Burn' },
    },
  };
  try {
    const response = await squareClient.ordersApi.createOrder(request);
    const order = response.result.order;
    if (!order?.id) {
      logServer(
        LogLevel.ERROR,
        'createBurnOrder',
        'Burn order creation failed or no ID returned from Square.'
      );
      throw new Error('Burn order creation failed (no ID returned).');
    }

    const [flowAttr, originalAttemptIdAttr] = await Promise.all([
      upsertOrderAttribute(order.id, ATTR_DEF_KEY_FLOW!, flow),
      upsertOrderAttribute(order.id, ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID!, originalPurchaseAttemptId),
    ]);

    if (!flowAttr || !originalAttemptIdAttr) {
      logServer(
        LogLevel.WARN,
        'createBurnOrder',
        'Failed to set one or more initial linkage attributes on the DRAFT burn order.',
        { orderId: order.id }
      );
    }
    return {
      order,
      flow: flowAttr,
      original_attemptId: originalAttemptIdAttr,
      attemptId: null,
      mintStatus: null,
      orderMintedTokens: null,
    };
  } catch (error) {
    logServer(
      LogLevel.ERROR,
      'createBurnOrder',
      'Error creating DRAFT burn order or its initial attributes.',
      {
        customerId,
        originalPurchaseAttemptId,
        locationIdForBurnOrder,
        errorDetails: (error as ApiError)?.errors || (error as Error).message,
      }
    );
    throw error;
  }
}

export async function retrieveOrder(orderId: string): Promise<AppOrderWithAttributes | null> {
  if (!squareClient) throw new Error('Square client not initialized for retrieveOrder.');
  const allOrderAttrKeys = [
    ATTR_DEF_KEY_FLOW,
    ATTR_DEF_KEY_ATTEMPT_ID,
    ATTR_DEF_KEY_MINT_STATUS,
    ATTR_DEF_KEY_ORDER_MINTED_TOKENS,
    ATTR_DEF_KEY_BURNED_TOKENS,
    ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID,
    ATTR_DEF_KEY_BURN_DATE,
  ];
  if (allOrderAttrKeys.some(key => !key)) {
    logServer(
      LogLevel.ERROR,
      'retrieveOrder',
      'One or more required Order Attribute Definition Keys are missing.'
    );
    throw new Error('Server configuration error for order attributes retrieval.');
  }

  try {
    const orderResponse = await squareClient.ordersApi.retrieveOrder(orderId);
    const order = orderResponse.result.order;
    if (!order) {
      logServer(LogLevel.WARN, 'retrieveOrder', `Order with ID ${orderId} not found.`);
      return null;
    }

    const [
      flowAttr,
      attemptIdAttr,
      mintStatusAttr,
      mintedTokensAttr,
      burnedTokensAttr,
      originalAttemptIdAttr,
      burnDateAttr,
    ] = await Promise.all([
      retrieveOrderAttribute(orderId, ATTR_DEF_KEY_FLOW!),
      retrieveOrderAttribute(orderId, ATTR_DEF_KEY_ATTEMPT_ID!),
      retrieveOrderAttribute(orderId, ATTR_DEF_KEY_MINT_STATUS!),
      retrieveOrderAttribute(orderId, ATTR_DEF_KEY_ORDER_MINTED_TOKENS!),
      retrieveOrderAttribute(orderId, ATTR_DEF_KEY_BURNED_TOKENS!),
      retrieveOrderAttribute(orderId, ATTR_DEF_KEY_ORIGINAL_ATTEMPT_ID!),
      retrieveOrderAttribute(orderId, ATTR_DEF_KEY_BURN_DATE!),
    ]);

    return {
      order,
      flow: flowAttr,
      attemptId: attemptIdAttr,
      mintStatus: mintStatusAttr,
      orderMintedTokens: mintedTokensAttr,
      orderBurnedTokens: burnedTokensAttr,
      original_attemptId: originalAttemptIdAttr,
      burnDate: burnDateAttr,
    };
  } catch (error) {
    logServer(LogLevel.ERROR, 'retrieveOrder', 'Error retrieving order or its attributes.', {
      orderId,
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
    });
    return null;
  }
}

export async function updateOrderState(
  orderId: string,
  newState: 'COMPLETED' | 'CANCELED' | 'OPEN'
): Promise<Order | null> {
  if (!squareClient) throw new Error('Square Client missing for updateOrderState.');

  try {
    const retrieveResponse = await squareClient.ordersApi.retrieveOrder(orderId);
    const currentOrder = retrieveResponse.result.order;

    if (!currentOrder) {
      logServer(
        LogLevel.ERROR,
        'updateOrderState',
        `Order ${orderId} not found, cannot update state.`
      );
      throw new Error(`Order ${orderId} not found for state update.`);
    }
    if (currentOrder.version === undefined || currentOrder.locationId === undefined) {
      logServer(
        LogLevel.ERROR,
        'updateOrderState',
        `Order ${orderId} is missing version or locationId, cannot update state.`
      );
      throw new Error(
        `Order ${orderId} missing critical data (version/locationId) for state update.`
      );
    }

    const request: UpdateOrderRequest = {
      idempotencyKey: uuidv4(),
      order: {
        locationId: currentOrder.locationId,
        version: currentOrder.version,
        state: newState,
      },
    };
    const response = await squareClient.ordersApi.updateOrder(orderId, request);
    return response.result.order ?? null;
  } catch (error) {
    logServer(
      LogLevel.ERROR,
      'updateOrderState',
      `Error updating order ${orderId} to state ${newState}.`,
      { errorDetails: (error as ApiError)?.errors || (error as Error).message }
    );
    throw error;
  }
}

export async function completeSquareOrder(orderId: string): Promise<AppOrderWithAttributes | null> {
  if (!squareClient) throw new Error('Square client not initialized for completeSquareOrder.');
  try {
    const initialOrderData = await retrieveOrder(orderId);
    const currentOrderBase = initialOrderData?.order;

    if (!currentOrderBase) {
      logServer(
        LogLevel.ERROR,
        'completeSquareOrder',
        `Order ${orderId} not found, cannot complete.`
      );
      throw new Error(`Order ${orderId} not found.`);
    }

    if (currentOrderBase.state === 'COMPLETED') {
      logServer(
        LogLevel.INFO,
        'completeSquareOrder',
        `Order ${orderId} is already in COMPLETED state.`
      );
      return initialOrderData;
    }

    if (currentOrderBase.state !== 'DRAFT' && currentOrderBase.state !== 'OPEN') {
      logServer(
        LogLevel.WARN,
        'completeSquareOrder',
        `Cannot complete order ${orderId} from state: ${currentOrderBase.state}.`
      );
      throw new Error(
        `Order ${orderId} is not in a state that can be completed (current: ${currentOrderBase.state}).`
      );
    }

    const updatedBaseOrder = await updateOrderState(orderId, 'COMPLETED');
    if (!updatedBaseOrder) {
      logServer(
        LogLevel.ERROR,
        'completeSquareOrder',
        `Failed to update order ${orderId} to COMPLETED state.`
      );
      return null;
    }
    return await retrieveOrder(orderId);
  } catch (error) {
    logServer(LogLevel.ERROR, 'completeSquareOrder', 'Failed to complete Square order.', {
      orderId,
      errorDetails: (error as ApiError)?.errors || (error as Error).message,
    });
    return null;
  }
}

export async function findCompletedOrderByCustomAttribute(
  attributeKey: string,
  attributeValue: string,
  lookbackDays: number = 7
): Promise<AppOrderWithAttributes | null> {
  if (!squareClient || !DEFAULT_SQUARE_LOCATION_ID || !attributeKey) {
    logServer(
      LogLevel.ERROR,
      'findCompletedOrderByCustomAttribute',
      'Square Client, Default Location ID, or Attribute Key is missing.'
    );
    return null;
  }

  const lookbackDate = new Date(Date.now() - lookbackDays * 24 * 60 * 60 * 1000).toISOString();

  const filter: import('square').SearchOrdersFilter = {
    stateFilter: { states: ['COMPLETED'] },
    dateTimeFilter: {
      createdAt: {
        startAt: lookbackDate,
      },
    },
  };
  const query: import('square').SearchOrdersQuery = {
    filter: filter,
    sort: { sortField: 'CREATED_AT', sortOrder: 'DESC' },
  };

  try {
    const searchResponse = await squareClient.ordersApi.searchOrders({
      locationIds: [DEFAULT_SQUARE_LOCATION_ID],
      query: query,
      limit: 100,
    });

    const candidateOrders = searchResponse.result.orders;
    if (!candidateOrders || candidateOrders.length === 0) {
      logServer(
        LogLevel.DEBUG,
        'findCompletedOrderByCustomAttribute',
        `No COMPLETED orders found (last ${lookbackDays} days).`
      );
      return null;
    }

    logServer(
      LogLevel.DEBUG,
      'findCompletedOrderByCustomAttribute',
      `Found ${candidateOrders.length} COMPLETED orders (last ${lookbackDays} days) to check: Key='${attributeKey}', Value='${attributeValue}'.`
    );

    for (const candidateOrder of candidateOrders) {
      if (!candidateOrder.id) continue;
      try {
        const attribute = await retrieveOrderAttribute(candidateOrder.id, attributeKey);
        if (attribute?.value === attributeValue) {
          logServer(
            LogLevel.INFO,
            'findCompletedOrderByCustomAttribute',
            'Found matching COMPLETED order.',
            { orderId: candidateOrder.id, attributeKey, attributeValue }
          );
          return await retrieveOrder(candidateOrder.id);
        }
      } catch (attrError: any) {
        logServer(
          LogLevel.WARN,
          'findCompletedOrderByCustomAttribute',
          'Error retrieving attribute for candidate order.',
          { orderId: candidateOrder.id, attributeKey, error: attrError.message }
        );
      }
    }
    logServer(
      LogLevel.DEBUG,
      'findCompletedOrderByCustomAttribute',
      'No matching COMPLETED order found after attribute checks.'
    );
    return null;
  } catch (error) {
    logServer(
      LogLevel.ERROR,
      'findCompletedOrderByCustomAttribute',
      'Error searching COMPLETED orders or checking attributes.',
      {
        attributeKey,
        attributeValue,
        errorDetails: (error as ApiError)?.errors || (error as Error).message,
      }
    );
    return null;
  }
}

export async function createSquareCheckoutLink(
  customerId: string,
  lineItems: OrderLineItem[],
  email: string,
  redirectUrl: string,
  locationIdForLink: string
): Promise<{ checkoutUrl: string | null; orderId: string | null }> {
  if (!squareClient) {
    logServer(LogLevel.ERROR, 'createSquareCheckoutLink', 'Square Client is missing.');
    return { checkoutUrl: null, orderId: null };
  }
  if (!locationIdForLink) {
    logServer(
      LogLevel.ERROR,
      'createSquareCheckoutLink',
      'Location ID is missing for creating payment link.'
    );
    return { checkoutUrl: null, orderId: null };
  }

  try {
    const requestBody = {
      idempotencyKey: uuidv4(),
      order: {
        locationId: locationIdForLink,
        customerId: customerId,
        lineItems: lineItems,
        state: 'DRAFT',
        source: { name: 'Anonymous Self Checkout Mint' },
      },
      checkoutOptions: {
        allowTipping: false,
        redirectUrl: redirectUrl,
        askForShippingAddress: false,
        customerCanTransactOnline: true,
        enableCoupon: false,
        acceptedPaymentMethods: {
          applePay: true,
          googlePay: true,
          cashAppPay: false,
          afterpayClearpay: false,
          card: true,
        },
      },
      prePopulatedData: {
        buyerEmail: email,
      },
    };

    const response = await squareClient.checkoutApi.createPaymentLink(requestBody);
    const paymentLink = response.result.paymentLink as PaymentLink;

    const checkoutUrl = paymentLink?.url ?? null;
    const orderIdFromLink = paymentLink?.orderId ?? null;

    if (!checkoutUrl || !orderIdFromLink) {
      logServer(
        LogLevel.ERROR,
        'createSquareCheckoutLink',
        'Checkout URL or Order ID missing in Square API response.',
        {
          customerId,
          locationIdUsed: locationIdForLink,
          responseOrderId: orderIdFromLink,
          responseCheckoutUrlExists: !!checkoutUrl,
          fullResponse: response.result,
        }
      );
      return { checkoutUrl: null, orderId: null };
    }

    logServer(
      LogLevel.INFO,
      'createSquareCheckoutLink',
      'Successfully created Square payment link and associated order.',
      { customerId, orderId: orderIdFromLink, locationId: locationIdForLink, checkoutUrl }
    );
    return { checkoutUrl, orderId: orderIdFromLink };
  } catch (error) {
    logServer(
      LogLevel.ERROR,
      'createSquareCheckoutLink',
      'Error creating Square checkout payment link.',
      {
        customerId,
        email,
        locationIdAttempted: locationIdForLink,
        errorDetails: (error as ApiError)?.errors || (error as Error).message,
      }
    );
    return { checkoutUrl: null, orderId: null };
  }
}
