import 'server-only';
import { v4 as uuidv4 } from 'uuid';

import { logServer, LogLevel } from '@/src/lib/logging';

import { verificationConfig } from '../../lib/config/verificationConfig';
import { generateToken, verifyToken } from '../../lib/tokenUtils';
import {
  ClaimAttemptTokenPayload,
  AnonymousAttemptTokenPayload,
  Step2TokenPayload,
  Step1TokenPayload,
} from '../../types/anonymousTokens';

// Import verificationConfig to access step3TokenSettings

const JWT_SECRET = process.env.SELF_CHECKOUT_ANONYMOUS_JWT_SECRET;

if (!JWT_SECRET) {
  logServer(
    LogLevel.ERROR,
    'anonymousTokenService',
    'SELF_CHECKOUT_ANONYMOUS_JWT_SECRET environment variable is not set. Anonymous checkout will not function.'
  );
}

export function generateClaimAttemptToken(): string {
  const secret = JWT_SECRET;
  if (!secret) throw new Error('JWT secret is not configured.');

  const payload: ClaimAttemptTokenPayload = {
    claimAttemptId: uuidv4(),
    createdAt: Date.now(),
  };
  logServer(LogLevel.DEBUG, 'generateClaimAttemptToken', 'Generating new claim attempt token', {
    claimAttemptId: payload.claimAttemptId,
  });
  return generateToken(payload, secret);
}

export function verifyClaimAttemptToken(token: string): ClaimAttemptTokenPayload | null {
  const secret = JWT_SECRET;
  if (!secret) {
    logServer(
      LogLevel.ERROR,
      'verifyClaimAttemptToken',
      'Cannot verify token, JWT secret is not configured.'
    );
    return null;
  }
  const payload = verifyToken<ClaimAttemptTokenPayload>(token, secret);
  if (payload) {
    logServer(LogLevel.DEBUG, 'verifyClaimAttemptToken', 'Claim attempt token verified', {
      claimAttemptId: payload.claimAttemptId,
    });
  }
  return payload;
}

export function generateOrUpdateAttemptToken(
  updates: Partial<AnonymousAttemptTokenPayload>, // Can include orderTotal for Step 2
  currentPayload?: AnonymousAttemptTokenPayload | null,
  initialData?: {
    // For Step 1: selectedItems will be enriched, no orderTotal
    claimAttemptId: string;
    email: string;
    selectedItems: import('@/src/features/self-checkout/anonymous/types').SelectedItem[]; // Enriched
    order_id: string;
    locationId: string; // Added: locationId is now required for initial data
    // orderTotal is NOT part of initialData for Step 1
  }
): string {
  const secret = JWT_SECRET;
  if (!secret) throw new Error('JWT secret is not configured.');

  let newPayload: AnonymousAttemptTokenPayload;
  const now = Date.now();
  let expiresIn: string | number | undefined = undefined;

  if (currentPayload) {
    // Update existing token (e.g., Step 1 -> Step 2, or Step 2 -> Step 3)
    newPayload = {
      ...currentPayload, // locationId will be preserved from currentPayload
      ...updates, // 'updates' will add 'orderTotal' when going from Step 1 to Step 2.
      // 'orderTotal' from currentPayload (Step 2) will persist to Step 3 unless 'updates' overwrites it.
      updatedAt: now,
    } as AnonymousAttemptTokenPayload;

    logServer(LogLevel.DEBUG, 'generateOrUpdateAttemptToken', 'Updating attempt token', {
      attemptId: newPayload.attemptId,
      previousStep: currentPayload.step,
      newStep: newPayload.step,
      newStatus: newPayload.status,
      locationId: newPayload.locationId,
      hasOrderTotal: (newPayload as Step2TokenPayload).orderTotal !== undefined,
    });
  } else if (initialData) {
    // Create new Step 1 token
    if (updates.step && updates.step !== 1) {
      logServer(
        LogLevel.WARN,
        'generateOrUpdateAttemptToken',
        'Attempting to create initial token with step other than 1',
        // This check is more of a sanity check; `updates` might override step later if appropriate.
        { requestedStep: updates.step }
      );
    }
    if (!initialData.locationId) {
      logServer(
        LogLevel.ERROR,
        'generateOrUpdateAttemptToken',
        'Cannot generate initial token: locationId is missing in initialData.'
      );
      throw new Error('locationId is required for token generation.');
    }

    const attemptId = (updates as Step1TokenPayload)?.attemptId || uuidv4();
    newPayload = {
      attemptId: attemptId,
      claimAttemptId: initialData.claimAttemptId,
      email: initialData.email,
      createdAt: now,
      updatedAt: now,
      selectedItems: initialData.selectedItems, // Enriched selectedItems
      order_id: initialData.order_id,
      locationId: initialData.locationId, // Store the determined locationId
      step: 1,
      status: 'pending_payment',
      ...updates, // Apply any explicit overrides from 'updates'
    } as Step1TokenPayload; // Explicitly Step1TokenPayload

    logServer(
      LogLevel.DEBUG,
      'generateOrUpdateAttemptToken',
      'Generating initial (Step 1) attempt token',
      {
        attemptId: newPayload.attemptId,
        locationId: newPayload.locationId,
        selectedItemsCount: newPayload.selectedItems.length,
      }
    );
  } else {
    logServer(
      LogLevel.ERROR,
      'generateOrUpdateAttemptToken',
      'Cannot generate token: Requires currentPayload or initialData.'
    );
    throw new Error('Invalid arguments: Missing data for token generation.');
  }

  if (newPayload.step === 3) {
    if (
      verificationConfig.step3TokenSettings.enableJwtExpiry &&
      verificationConfig.step3TokenSettings.jwtExpirySeconds &&
      verificationConfig.step3TokenSettings.jwtExpirySeconds > 0
    ) {
      expiresIn = verificationConfig.step3TokenSettings.jwtExpirySeconds;
      logServer(
        LogLevel.DEBUG,
        'generateOrUpdateAttemptToken',
        `Setting JWT expiry for Step 3 token: ${expiresIn}s`,
        { attemptId: newPayload.attemptId }
      );
    }
  }
  return generateToken(newPayload, secret, expiresIn);
}

export function verifyAttemptToken(token: string): AnonymousAttemptTokenPayload | null {
  const secret = JWT_SECRET;
  if (!secret) {
    logServer(
      LogLevel.ERROR,
      'verifyAttemptToken',
      'Cannot verify token, JWT secret is not configured.'
    );
    return null;
  }
  const payload = verifyToken<AnonymousAttemptTokenPayload>(token, secret);
  if (payload) {
    logServer(
      LogLevel.DEBUG,
      'verifyAttemptToken',
      'Attempt token signature and JWT expiry verified',
      {
        attemptId: payload.attemptId,
        step: payload.step,
        status: payload.status,
        locationId: payload.locationId,
        hasOrderTotal: (payload as Step2TokenPayload).orderTotal !== undefined,
      }
    );
  }
  return payload;
}
