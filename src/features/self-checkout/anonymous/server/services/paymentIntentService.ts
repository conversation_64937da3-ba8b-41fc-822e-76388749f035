import 'server-only';
import { OrderLineItem } from 'square';
import { v4 as uuidv4 } from 'uuid';

import { API_ENDPOINTS, ERROR_MESSAGES, SUBSCRIPTION_DEFAULTS } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';

import { parseCustomerBalance, countTokensInParsedBalance } from '../../lib/squareAttributeUtils';
// Ensure PurchaseRequestItem is imported alongside SelectedItem
import { SelectedItem, PurchaseRequestItem } from '../../types';

import {
  retrieveCatalogObject,
  findCustomerByEmail,
  retrieveCustomer,
  createCustomer,
  updateCustomerBalance,
  createSquareCheckoutLink,
  upsertOrderAttribute,
  listSquareLocations,
} from './anonymousSquareService';
import { verifyClaimAttemptToken, generateOrUpdateAttemptToken } from './anonymousTokenService';

const MAX_TOTAL_TOKENS = 100;

const ATTR_DEF_KEY_FLOW = process.env.SELF_CHECKOUT_ATTR_DEF_KEY_FLOW || 'flowcheckouttype';
const ATTR_DEF_KEY_ATTEMPT_ID =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_ATTEMPT_ID || 'attempttokenid';
const ATTR_DEF_KEY_MINT_STATUS =
  process.env.SELF_CHECKOUT_ATTR_DEF_KEY_MINT_STATUS || 'tokenmintstatus';

export async function createPaymentIntent(
  email: string,
  selectedItemsFromRequest: PurchaseRequestItem[], // Corrected Type: Expects the simpler type from the route
  claimToken: string,
  configuredGlobalItemLimit: number, // Renamed from configuredLimit
  providedLocationIdFromRequest?: string
): Promise<{
  checkoutUrl: string;
  attemptToken: string;
}> {
  logServer(
    LogLevel.INFO,
    'paymentIntentService.createPaymentIntent',
    'Starting payment intent creation (implicit order)',
    { email, providedLocationIdFromRequest }
  );

  const claimPayload = verifyClaimAttemptToken(claimToken);
  if (!claimPayload) {
    logServer(
      LogLevel.ERROR,
      'paymentIntentService.createPaymentIntent',
      'Invalid or missing claim token'
    );
    throw new Error(ERROR_MESSAGES.INVALID_REQUEST);
  }
  const { claimAttemptId } = claimPayload;
  logServer(LogLevel.DEBUG, 'paymentIntentService.createPaymentIntent', 'Claim token verified', {
    claimAttemptId,
  });

  // Determine the final locationId to use for this transaction
  let finalLocationIdToUse: string;
  const defaultSquareLocationId = process.env.SELF_CHECKOUT_SQUARE_LOCATION_ID!;

  const merchantSquareLocations = await listSquareLocations();

  if (providedLocationIdFromRequest) {
    const providedLocationIdUpper = providedLocationIdFromRequest.toUpperCase();
    const matchedLocation = merchantSquareLocations?.find(
      loc => loc.id?.toUpperCase() === providedLocationIdUpper
    );
    if (matchedLocation && matchedLocation.id) {
      finalLocationIdToUse = matchedLocation.id;
      logServer(
        LogLevel.INFO,
        'paymentIntentService.createPaymentIntent',
        `Using provided valid location ID: ${finalLocationIdToUse}`
      );
    } else {
      finalLocationIdToUse = defaultSquareLocationId;
      logServer(
        LogLevel.WARN,
        'paymentIntentService.createPaymentIntent',
        `Invalid locationId '${providedLocationIdFromRequest}' provided. Falling back to default: ${finalLocationIdToUse}`
      );
    }
  } else {
    finalLocationIdToUse = defaultSquareLocationId;
    logServer(
      LogLevel.INFO,
      'paymentIntentService.createPaymentIntent',
      `No locationId provided. Using default: ${finalLocationIdToUse}`
    );
  }

  const tokenTypeAttributeKey = process.env.NEXT_PUBLIC_SQUARE_ITEM_VARIATION_TOKEN_TYPE_ATTR_KEY;

  let totalQuantitySelected = 0;
  if (!selectedItemsFromRequest || selectedItemsFromRequest.length === 0) {
    throw new Error('Item selection cannot be empty.');
  }
  const lineItems: OrderLineItem[] = [];
  const richSelectedItemsForToken: SelectedItem[] = []; // This will be populated for the JWT

  for (const item of selectedItemsFromRequest) {
    // item is PurchaseRequestItem here
    if (item.quantity <= 0 || !Number.isInteger(item.quantity)) {
      throw new Error('Invalid quantity provided.');
    }
    if (item.quantity > configuredGlobalItemLimit) {
      throw new Error(`Quantity for item exceeds limit of ${configuredGlobalItemLimit}.`);
    }
    totalQuantitySelected += item.quantity;
    lineItems.push({
      catalogObjectId: item.variationId,
      quantity: item.quantity.toString(),
    });

    let itemName = 'Unknown Item';
    let itemImageUrl = SUBSCRIPTION_DEFAULTS.PRODUCT_FALLBACK_IMAGE_URL;
    let itemTokenType = 'C'; // Default token type

    try {
      const catalogObject = await retrieveCatalogObject(item.variationId, true);
      if (catalogObject?.itemVariationData) {
        itemName =
          catalogObject.itemVariationData.name ||
          catalogObject.itemData?.name ||
          `Item ${item.variationId}`;

        // Extract TokenType from custom attribute
        if (
          tokenTypeAttributeKey &&
          catalogObject.customAttributeValues?.[tokenTypeAttributeKey]?.stringValue
        ) {
          const typeValue =
            catalogObject.customAttributeValues[tokenTypeAttributeKey]!.stringValue!;
          if (typeValue.length === 1) {
            itemTokenType = typeValue;
          }
        }

        const imageId =
          catalogObject.itemVariationData.imageIds?.[0] || catalogObject.itemData?.imageIds?.[0];
        if (imageId) {
          itemImageUrl = `${API_ENDPOINTS.SELF_CHECKOUT_GET_IMAGE}?imageId=${imageId}`;
        }
      } else if (catalogObject?.itemData) {
        itemName = catalogObject.itemData.name || `Item ${item.variationId}`;
        // TokenType for itemData itself is less common, but check if defined
        if (
          tokenTypeAttributeKey &&
          catalogObject.customAttributeValues?.[tokenTypeAttributeKey]?.stringValue
        ) {
          const typeValue =
            catalogObject.customAttributeValues[tokenTypeAttributeKey]!.stringValue!;
          if (typeValue.length === 1) {
            itemTokenType = typeValue;
          }
        }
        const imageId = catalogObject.itemData.imageIds?.[0];
        if (imageId) {
          itemImageUrl = `${API_ENDPOINTS.SELF_CHECKOUT_GET_IMAGE}?imageId=${imageId}`;
        }
      } else {
        logServer(
          LogLevel.WARN,
          'paymentIntentService.createPaymentIntent',
          `Could not retrieve full details for variation ID: ${item.variationId}. Using defaults.`,
          { variationId: item.variationId }
        );
      }
    } catch (catError: any) {
      logServer(
        LogLevel.ERROR,
        'paymentIntentService.createPaymentIntent',
        `Error fetching catalog details for variation ${item.variationId}. Using defaults.`,
        { error: catError.message, variationId: item.variationId }
      );
    }
    richSelectedItemsForToken.push({
      // Populating the enriched array
      variationId: item.variationId,
      quantity: item.quantity,
      name: itemName,
      imageUrl: itemImageUrl,
      tokentype: itemTokenType, // Add the determined token type
    });
  }
  logServer(
    LogLevel.DEBUG,
    'paymentIntentService.createPaymentIntent',
    'Item selection validated and enriched',
    { totalQuantitySelected, itemsForTokenCount: richSelectedItemsForToken.length }
  );

  let customerId: string;
  let fetchedCustomerData:
    | import('@/src/features/self-checkout/anonymous/types').AppCustomerWithAttributes
    | null = null;

  const baseCustomer = await findCustomerByEmail(email);
  if (baseCustomer?.id) {
    customerId = baseCustomer.id;
    fetchedCustomerData = await retrieveCustomer(customerId);
    if (!fetchedCustomerData?.customer) {
      throw new Error('Failed to retrieve customer details.');
    }
  } else {
    fetchedCustomerData = await createCustomer(email, claimAttemptId);
    if (!fetchedCustomerData?.customer?.id) {
      throw new Error('Failed to create customer profile.');
    }
    customerId = fetchedCustomerData.customer.id;
  }

  let currentBalanceString: string;
  const rawBalanceAttribute = fetchedCustomerData.clamshellTokenBalance;
  if (!rawBalanceAttribute || typeof rawBalanceAttribute.value !== 'string') {
    try {
      const updatedCustomerDataWithBalance = await updateCustomerBalance(customerId, ':U');
      if (
        !updatedCustomerDataWithBalance?.clamshellTokenBalance?.value ||
        typeof updatedCustomerDataWithBalance.clamshellTokenBalance.value !== 'string'
      ) {
        throw new Error('Failed to prepare customer token balance. Initialization unsuccessful.');
      }
      currentBalanceString = updatedCustomerDataWithBalance.clamshellTokenBalance.value;
    } catch (upsertError: any) {
      throw new Error(
        'Failed to prepare customer token balance due to an update error.',
        upsertError
      );
    }
  } else {
    currentBalanceString = rawBalanceAttribute.value;
  }
  const parsedBalance = parseCustomerBalance(currentBalanceString);
  if (!parsedBalance.isValid) {
    throw new Error(
      'Your account data is configured incorrectly. Please contact support immediately.'
    );
  }
  if (parsedBalance.accountStatus === 'F') {
    throw new Error(
      'Your account is currently unable to make new purchases. Please contact support.'
    );
  }
  if (parsedBalance.accountStatus === null) {
    throw new Error('Your account data is incomplete or corrupt. Please contact support.');
  }

  const currentTokenCount = countTokensInParsedBalance(parsedBalance);
  if (currentTokenCount + totalQuantitySelected > MAX_TOTAL_TOKENS) {
    const error = new Error(
      `Purchase would exceed the maximum limit of ${MAX_TOTAL_TOKENS} tokens. You currently have ${currentTokenCount}.`
    );
    (error as any).statusCode = 400;
    throw error;
  }

  const attemptIdForFlow = uuidv4();
  const redirectUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/claim?attemptId=${attemptIdForFlow}`;
  const checkoutResult = await createSquareCheckoutLink(
    customerId,
    lineItems,
    email,
    redirectUrl,
    finalLocationIdToUse
  );

  if (!checkoutResult || !checkoutResult.checkoutUrl || !checkoutResult.orderId) {
    throw new Error('Failed to create payment checkout link or retrieve order ID.');
  }
  const { checkoutUrl, orderId } = checkoutResult;

  try {
    await Promise.all([
      upsertOrderAttribute(orderId, ATTR_DEF_KEY_FLOW, 'anonymous'),
      upsertOrderAttribute(orderId, ATTR_DEF_KEY_ATTEMPT_ID, attemptIdForFlow),
      upsertOrderAttribute(orderId, ATTR_DEF_KEY_MINT_STATUS, 'mintable'),
    ]);
  } catch (attrError: any) {
    logServer(
      LogLevel.ERROR,
      'paymentIntentService.createPaymentIntent',
      'Failed to upsert one or more custom attributes to order. Non-fatal for payment link.',
      { orderId, error: attrError.message }
    );
  }

  let attemptToken: string;
  try {
    attemptToken = generateOrUpdateAttemptToken({ attemptId: attemptIdForFlow }, null, {
      claimAttemptId,
      email,
      selectedItems: richSelectedItemsForToken, // Use the ENRICHED items for the token
      order_id: orderId,
      locationId: finalLocationIdToUse,
    });
  } catch (tokenError: any) {
    throw new Error('Failed to initialize purchase session state.', tokenError);
  }

  logServer(
    LogLevel.INFO,
    'paymentIntentService.createPaymentIntent',
    'Payment intent creation successful',
    { attemptId: attemptIdForFlow, orderId, locationIdUsed: finalLocationIdToUse }
  );

  return { checkoutUrl, attemptToken };
}
