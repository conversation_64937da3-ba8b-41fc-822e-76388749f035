import 'server-only';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';

import { Step2TokenPayload } from '../../types/anonymousTokens';
import { step2TokenSchema } from '../anonymousValidationSchemas';

import { executeAnonymousBurn } from './anonymousBurnFlowService';
import { verifyAttemptToken, generateOrUpdateAttemptToken } from './anonymousTokenService';

/**
 * Handles the claim action step for anonymous self-checkout.
 * - Verifies the incoming Step 2 attempt token.
 * - Executes the token burn flow using the `anonymousBurnFlowService`.
 * - Issues the final Step 3 attempt token upon successful burn.
 *
 * @param attemptTokenJwt - The Step 2 JWT string from the X-Attempt-Token header.
 * @returns An object containing the final Step 3 attemptToken.
 * @throws Throws errors for invalid token, burn flow failures (e.g., already claimed, balance update failed).
 */
export async function executeClaim(attemptTokenJwt: string): Promise<{ attemptToken: string }> {
  logServer(LogLevel.INFO, 'claimActionService.executeClaim', 'Starting claim action');

  // 1. Verify and Validate Incoming Token
  const tokenPayload = verifyAttemptToken(attemptTokenJwt);
  if (!tokenPayload) {
    const error = new Error(ERROR_MESSAGES.INVALID_REQUEST);
    (error as any).statusCode = 401; // Unauthorized
    throw error;
  }

  const validationResult = step2TokenSchema.safeParse(tokenPayload);
  if (!validationResult.success) {
    logServer(
      LogLevel.WARN,
      'claimActionService.executeClaim',
      'Incoming token failed schema validation',
      {
        attemptId: tokenPayload.attemptId,
        errors: validationResult.error.flatten(),
      }
    );
    const error = new Error('Invalid token state for claiming.');
    (error as any).statusCode = 400; // Bad Request
    throw error;
  }

  const validPayload = validationResult.data as Step2TokenPayload;
  const { order_id, attemptId, locationId } = validPayload; // locationId is from the original mint transaction
  logServer(
    LogLevel.DEBUG,
    'claimActionService.executeClaim',
    'Attempt token verified and validated (Step 2)',
    { attemptId, order_id, locationId }
  );

  // 2. Execute Burn Flow
  logServer(LogLevel.INFO, 'claimActionService.executeClaim', 'Executing anonymous burn flow', {
    order_id,
    attemptId,
    locationIdForBurn: locationId, // Log the locationId to be used for burn
  });
  // Pass locationId to executeAnonymousBurn
  const burnResult = await executeAnonymousBurn(order_id, attemptId, locationId);

  if (!burnResult.success) {
    logServer(LogLevel.ERROR, 'claimActionService.executeClaim', 'Burn flow failed', {
      attemptId,
      order_id,
      locationId,
      error: burnResult.error,
      burnOrderId: burnResult.burnOrderId,
    });
    let statusCode = 500;
    let message = burnResult.error || ERROR_MESSAGES.INTERNAL_SERVER_ERROR;
    if (burnResult.error === 'Already Claimed' || burnResult.isAlreadyClaimed) {
      // Added isAlreadyClaimed check
      statusCode = 409;
      message = 'This purchase has already been claimed.';
    } else if (burnResult.error?.includes('Tokens invalid or already burned')) {
      statusCode = 428;
      message = 'Tokens to claim are invalid or already used.';
    } else if (burnResult.error === 'Failed to update token balance.') {
      statusCode = 500;
      message = 'Failed to update token balance. Please try again or contact support.';
    }
    const error = new Error(message);
    (error as any).statusCode = statusCode;
    throw error;
  }

  if (!burnResult.burnOrderId) {
    logServer(
      LogLevel.ERROR,
      'claimActionService.executeClaim',
      'Burn flow succeeded but burnOrderId is missing',
      { attemptId, order_id, locationId }
    );
    throw new Error(ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
  }

  const burnOrderId = burnResult.burnOrderId;
  logServer(LogLevel.INFO, 'claimActionService.executeClaim', 'Burn flow successful', {
    attemptId,
    order_id,
    burnOrderId,
    locationId,
  });

  // 3. Generate Final Attempt Token (Step 3)
  let finalAttemptToken: string;
  try {
    logServer(
      LogLevel.DEBUG,
      'claimActionService.executeClaim',
      'Generating Step 3 attempt token',
      { attemptId }
    );
    // validPayload (Step 2) already contains the correct locationId, it will be preserved
    finalAttemptToken = generateOrUpdateAttemptToken(
      {
        step: 3,
        status: 'claimed',
        burnOrderId: burnOrderId,
      },
      validPayload
    );
  } catch (tokenError) {
    logServer(
      LogLevel.ERROR,
      'claimActionService.executeClaim',
      'Failed to generate Step 3 attempt token',
      { attemptId, error: (tokenError as Error).message }
    );
    throw new Error('Failed to finalize claim session.');
  }

  logServer(LogLevel.INFO, 'claimActionService.executeClaim', 'Claim action successful', {
    attemptId,
    order_id,
    burnOrderId,
    locationId,
  });
  return { attemptToken: finalAttemptToken };
}
