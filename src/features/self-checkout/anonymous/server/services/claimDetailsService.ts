import 'server-only';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';

import { Step1TokenPayload } from '../../types/anonymousTokens';
import { step1TokenSchema } from '../anonymousValidationSchemas';

import { retrieveOrder } from './anonymousSquareService';
import { verifyAttemptToken, generateOrUpdateAttemptToken } from './anonymousTokenService';

export async function getClaimDetails(attemptTokenJwt: string): Promise<{ attemptToken: string }> {
  logServer(LogLevel.INFO, 'claimDetailsService.getClaimDetails', 'Starting claim details fetch');

  const tokenPayload = verifyAttemptToken(attemptTokenJwt);
  if (!tokenPayload) {
    throw new Error(ERROR_MESSAGES.INVALID_REQUEST);
  }

  const validationResult = step1TokenSchema.safeParse(tokenPayload);
  if (!validationResult.success) {
    logServer(
      LogLevel.WARN,
      'claimDetailsService.getClaimDetails',
      'Incoming token failed schema validation',
      {
        attemptId: tokenPayload.attemptId,
        errors: validationResult.error.flatten(),
      }
    );
    const error = new Error('Invalid token state for fetching claim details.');
    (error as any).statusCode = 400;
    throw error;
  }

  const validPayload = validationResult.data as Step1TokenPayload; // This is a Step 1 payload
  const { order_id, attemptId } = validPayload;
  logServer(
    LogLevel.DEBUG,
    'claimDetailsService.getClaimDetails',
    'Attempt token verified and validated (Step 1)',
    { attemptId, order_id }
  );

  const orderData = await retrieveOrder(order_id);
  if (!orderData?.order) {
    logServer(LogLevel.ERROR, 'claimDetailsService.getClaimDetails', 'Square order not found', {
      order_id,
      attemptId,
    });
    const error = new Error('Associated order not found.');
    (error as any).statusCode = 404;
    throw error;
  }
  const order = orderData.order;
  logServer(LogLevel.DEBUG, 'claimDetailsService.getClaimDetails', 'Square order retrieved', {
    order_id,
    orderState: order.state,
  });

  if (order.state !== 'COMPLETED') {
    logServer(
      LogLevel.WARN,
      'claimDetailsService.getClaimDetails',
      'Order is not completed (not paid)',
      { order_id, attemptId, orderState: order.state }
    );
    const error = new Error(
      'Order payment is not complete. Please wait or contact support if this persists.'
    );
    (error as any).statusCode = 409;
    throw error;
  }
  logServer(
    LogLevel.DEBUG,
    'claimDetailsService.getClaimDetails',
    'Order payment verified (COMPLETED)',
    { order_id }
  );

  // Extract actual order total from Square order
  const actualOrderTotalInCents = order.totalMoney?.amount ? Number(order.totalMoney.amount) : 0;
  if (order.totalMoney?.amount === undefined || order.totalMoney?.amount === null) {
    logServer(
      LogLevel.WARN,
      'claimDetailsService.getClaimDetails',
      'Order totalMoney.amount is missing in Square Order object. Defaulting orderTotal to 0 for token.',
      { order_id, attemptId }
    );
  }

  const mintStatus = orderData.mintStatus?.value as string | undefined;
  const mintedTokens = orderData.orderMintedTokens?.value as string | undefined;

  logServer(
    LogLevel.DEBUG,
    'claimDetailsService.getClaimDetails',
    'Checking mint status attributes',
    { order_id, mintStatus, mintedTokens: mintedTokens ? 'Present' : 'Absent/Empty' }
  );

  if (mintStatus !== 'minted') {
    logServer(
      LogLevel.WARN,
      'claimDetailsService.getClaimDetails',
      'Order mint status is not "minted"',
      { order_id, attemptId, mintStatus }
    );
    const error = new Error(
      'Token minting is not yet complete. Please wait a moment and try again.'
    );
    (error as any).statusCode = 409;
    throw error;
  }
  if (!mintedTokens || mintedTokens.trim() === '') {
    logServer(
      LogLevel.WARN,
      'claimDetailsService.getClaimDetails',
      'Order is minted but orderMintedTokens attribute is missing or empty',
      { order_id, attemptId }
    );
    const error = new Error(
      'Token details are not available yet. Please wait a moment and try again.'
    );
    (error as any).statusCode = 409;
    throw error;
  }
  logServer(
    LogLevel.DEBUG,
    'claimDetailsService.getClaimDetails',
    'Order minting verified (minted with tokens)',
    { order_id }
  );

  let updatedAttemptToken: string;
  try {
    logServer(
      LogLevel.DEBUG,
      'claimDetailsService.getClaimDetails',
      'Generating Step 2 attempt token',
      { attemptId }
    );
    updatedAttemptToken = generateOrUpdateAttemptToken(
      {
        step: 2,
        status: 'ready_to_claim',
        orderTotal: actualOrderTotalInCents, // Add the actual order total here
      },
      validPayload // currentPayload is the Step 1 payload (contains rich selectedItems)
    );
  } catch (tokenError) {
    logServer(
      LogLevel.ERROR,
      'claimDetailsService.getClaimDetails',
      'Failed to generate Step 2 attempt token',
      { attemptId, error: (tokenError as Error).message }
    );
    throw new Error('Failed to update session state.');
  }
  logServer(
    LogLevel.INFO,
    'claimDetailsService.getClaimDetails',
    'Claim details fetched and verified successfully, Step 2 token generated with orderTotal.',
    { attemptId, order_id, orderTotal: actualOrderTotalInCents }
  );

  return { attemptToken: updatedAttemptToken };
}
