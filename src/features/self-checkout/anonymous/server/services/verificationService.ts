import 'server-only';
import { ERROR_MESSAGES } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';

import { Step3TokenPayload } from '../../types/anonymousTokens';
import { step3TokenSchemaForVerification } from '../anonymousValidationSchemas';

import { retrieveOrder } from './anonymousSquareService';
import { verifyAttemptToken } from './anonymousTokenService';

/**
 * Handles the verification tap step for anonymous self-checkout.
 * Verifies token (including time window) and checks Burn Order status.
 */
export async function verifyClaimTap(attemptTokenJwt: string): Promise<{ success: boolean }> {
  logServer(LogLevel.INFO, 'verificationService.verifyClaimTap', 'Starting verification tap');

  // 1. Verify and Validate Incoming Token (with time window)
  const tokenPayload = verifyAttemptToken(attemptTokenJwt);
  if (!tokenPayload) {
    const error = new Error(ERROR_MESSAGES.INVALID_REQUEST);
    (error as any).statusCode = 401;
    throw error;
  }

  const validationResult = step3TokenSchemaForVerification.safeParse(tokenPayload);
  if (!validationResult.success) {
    logServer(
      LogLevel.WARN,
      'verificationService.verifyClaimTap',
      'Incoming token failed schema validation or time window',
      {
        attemptId: tokenPayload.attemptId,
        errors: validationResult.error.flatten(),
      }
    );
    const error = new Error('Verification failed: Invalid or expired token.');
    (error as any).statusCode = 401;
    throw error;
  }

  const validPayload = validationResult.data as Step3TokenPayload;
  const { burnOrderId, attemptId } = validPayload;
  logServer(
    LogLevel.DEBUG,
    'verificationService.verifyClaimTap',
    'Attempt token verified and validated (Step 3, within time window)',
    { attemptId, burnOrderId }
  );

  // 2. Retrieve Square Burn Order
  logServer(LogLevel.DEBUG, 'verificationService.verifyClaimTap', 'Retrieving Square burn order', {
    burnOrderId,
  });
  // retrieveOrder returns AppOrderWithAttributes
  const burnOrderData = await retrieveOrder(burnOrderId);
  if (!burnOrderData?.order) {
    // Check base order object
    logServer(LogLevel.ERROR, 'verificationService.verifyClaimTap', 'Square burn order not found', {
      burnOrderId,
      attemptId,
    });
    const error = new Error('Claim record not found.');
    (error as any).statusCode = 404;
    throw error;
  }
  const burnOrder = burnOrderData.order; // Use the base order object
  logServer(LogLevel.DEBUG, 'verificationService.verifyClaimTap', 'Square burn order retrieved', {
    burnOrderId,
    orderState: burnOrder.state,
  });

  // 3. Verify Burn Order Status using base order state
  if (burnOrder.state !== 'COMPLETED') {
    logServer(LogLevel.WARN, 'verificationService.verifyClaimTap', 'Burn order is not completed', {
      burnOrderId,
      attemptId,
      orderState: burnOrder.state,
    });
    // Verification fails if order is not completed
    return { success: false };
  }
  logServer(
    LogLevel.DEBUG,
    'verificationService.verifyClaimTap',
    'Burn order status verified (COMPLETED)',
    { burnOrderId }
  );

  // 4. Verification Successful
  logServer(LogLevel.INFO, 'verificationService.verifyClaimTap', 'Verification tap successful', {
    attemptId,
    burnOrderId,
  });
  return { success: true };
}
