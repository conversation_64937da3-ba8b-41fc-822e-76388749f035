import 'server-only';
import {
  CatalogCustomAttributeValue,
  CatalogObject,
  CatalogItemVariation,
  CatalogItem as SquareSdkCatalogItem, // Renamed to avoid conflict with local CatalogItem
} from 'square';

import { API_ENDPOINTS, SUBSCRIPTION_DEFAULTS } from '@/src/lib/constants';
import { logServer, LogLevel } from '@/src/lib/logging';

import { CatalogItem } from '../../types'; // This is your internal CatalogItem type

import {
  searchCatalogItemsByLocationAndCategory,
  listSquareLocations,
} from './anonymousSquareService';
import { verifyClaimAttemptToken, generateClaimAttemptToken } from './anonymousTokenService';

/**
 * Handles the initialization logic for the anonymous self-checkout flow.
 * Verifies an existing persistent token or generates a new one.
 * Fetches and returns product catalog information relevant to the flow.
 *
 * @param existingClaimToken - The optional JWT string from the X-Claim-Attempt-Token header.
 * @param locationIdFromRequest - Optional locationId provided by the client to fetch a location-specific catalog.
 * @returns An object containing the claimAttemptToken (either existing valid or new) and catalog info.
 * @throws Throws an error if token generation fails or catalog info cannot be retrieved.
 */
export async function initializeFlow(
  existingClaimToken?: string,
  locationIdFromRequest?: string
): Promise<{
  claimAttemptToken: string;
  catalog: CatalogItem[];
}> {
  const functionName = 'initializationService.initializeFlow_DeeperDive';
  let claimAttemptToken = '';
  let isValidExistingToken = false;

  logServer(LogLevel.INFO, functionName, 'Initializing anonymous flow (Deeper Dive for Price)', {
    locationIdFromRequest,
    hasExistingClaimToken: !!existingClaimToken,
  });

  let locationIdToUse: string | undefined = locationIdFromRequest;
  const defaultSquareLocationId = process.env.SELF_CHECKOUT_SQUARE_LOCATION_ID;
  logServer(LogLevel.DEBUG, functionName, 'Env Vars for Location/Category/TokenKey', {
    defaultSquareLocationId,
    NEXT_PUBLIC_SELF_CHECKOUT_CATEGORY_ID: process.env.NEXT_PUBLIC_SELF_CHECKOUT_CATEGORY_ID,
    NEXT_PUBLIC_SQUARE_ITEM_VARIATION_TOKEN_TYPE_ATTR_KEY:
      process.env.NEXT_PUBLIC_SQUARE_ITEM_VARIATION_TOKEN_TYPE_ATTR_KEY,
  });

  if (locationIdToUse) {
    const merchantLocations = await listSquareLocations();
    const isValidClientLocation = merchantLocations?.some(
      loc => loc.id?.toUpperCase() === locationIdToUse!.toUpperCase()
    );
    if (!isValidClientLocation) {
      logServer(
        LogLevel.WARN,
        functionName,
        `Invalid locationId '${locationIdToUse}' provided by client. Falling back to default.`,
        { defaultSquareLocationId }
      );
      locationIdToUse = defaultSquareLocationId;
    } else {
      logServer(
        LogLevel.INFO,
        functionName,
        `Using valid client-provided locationId: ${locationIdToUse}`
      );
    }
  } else {
    locationIdToUse = defaultSquareLocationId;
    logServer(
      LogLevel.INFO,
      functionName,
      `No locationId from client. Using default: ${locationIdToUse}`
    );
  }

  if (!locationIdToUse) {
    logServer(
      LogLevel.ERROR,
      functionName,
      'No valid Square Location ID could be determined (neither client-provided nor default is set/valid). Cannot fetch catalog.'
    );
    throw new Error('Store location information is unavailable.');
  }

  const categoryIdForSelfCheckout = process.env.NEXT_PUBLIC_SELF_CHECKOUT_CATEGORY_ID;
  const tokenTypeAttributeKey = process.env.NEXT_PUBLIC_SQUARE_ITEM_VARIATION_TOKEN_TYPE_ATTR_KEY;

  if (!categoryIdForSelfCheckout) {
    logServer(
      LogLevel.ERROR,
      functionName,
      'Server configuration error: NEXT_PUBLIC_SELF_CHECKOUT_CATEGORY_ID is missing.'
    );
    throw new Error('Product catalog category configuration is incomplete.');
  }
  if (!tokenTypeAttributeKey) {
    logServer(
      LogLevel.WARN,
      functionName,
      'Server configuration warning: NEXT_PUBLIC_SQUARE_ITEM_VARIATION_TOKEN_TYPE_ATTR_KEY is missing. Token types will default.'
    );
  }

  if (existingClaimToken) {
    logServer(LogLevel.DEBUG, functionName, 'Verifying existing claim token');
    const payload = verifyClaimAttemptToken(existingClaimToken);
    if (payload) {
      claimAttemptToken = existingClaimToken;
      isValidExistingToken = true;
      logServer(LogLevel.DEBUG, functionName, 'Existing claim token is valid', {
        claimAttemptId: payload.claimAttemptId,
      });
    } else {
      logServer(LogLevel.WARN, functionName, 'Existing claim token is invalid or expired');
    }
  } else {
    logServer(LogLevel.DEBUG, functionName, 'No existing claim token provided');
  }

  if (!isValidExistingToken) {
    try {
      logServer(LogLevel.INFO, functionName, 'Generating new claim attempt token');
      claimAttemptToken = generateClaimAttemptToken();
    } catch (error) {
      logServer(LogLevel.ERROR, functionName, 'Failed to generate claim attempt token', {
        error: (error as Error).message,
      });
      throw new Error('Failed to initialize session.');
    }
  }

  const catalog: CatalogItem[] = [];
  const globalItemPurchaseLimit = parseInt(
    process.env.SELF_CHECKOUT_CLAMSHELL_PURCHASE_LIMIT || '10',
    10
  );

  try {
    logServer(
      LogLevel.DEBUG,
      functionName,
      'Fetching dynamic catalog. Expecting CatalogObject[] from service call.',
      {
        categoryId: categoryIdForSelfCheckout,
        locationId: locationIdToUse,
      }
    );

    const rawCatalogObjectsFromSearch: CatalogObject[] | null =
      await searchCatalogItemsByLocationAndCategory(categoryIdForSelfCheckout, locationIdToUse);

    logServer(
      LogLevel.INFO,
      functionName,
      `Received ${rawCatalogObjectsFromSearch?.length ?? 0} CatalogObject items from service call.`
    );

    if (rawCatalogObjectsFromSearch && rawCatalogObjectsFromSearch.length > 0) {
      for (const variationCatalogObject of rawCatalogObjectsFromSearch) {
        console.log(
          'DEBUG: Full variationCatalogObject received by loop:',
          JSON.stringify(
            variationCatalogObject,
            (key, value) => (typeof value === 'bigint' ? value.toString() + 'n' : value),
            2
          )
        );

        if (
          variationCatalogObject.type !== 'ITEM_VARIATION' ||
          !variationCatalogObject.itemVariationData
        ) {
          logServer(
            LogLevel.WARN,
            functionName,
            `Skipping object as it's not a valid ITEM_VARIATION or missing itemVariationData. ID: ${variationCatalogObject.id}, Type: ${variationCatalogObject.type}`
          );
          continue;
        }

        const variationId = variationCatalogObject.id;
        logServer(
          LogLevel.DEBUG,
          functionName,
          `Processing variation CatalogObject: ${variationId}`
        );

        const firstLevelItemVariationData = variationCatalogObject.itemVariationData;
        // The actual CatalogItemVariation data is nested one level deeper as per logs
        const itemVariationDetails: CatalogItemVariation | undefined = (
          firstLevelItemVariationData as any
        )?.itemVariationData;

        if (!itemVariationDetails) {
          logServer(
            LogLevel.WARN,
            functionName,
            `Skipping variation ${variationId}: Deeply nested itemVariationData (containing priceMoney, etc.) not found. Structure might be different than expected or missing.`,
            { firstLevelItemVariationData } // Log the first level to see what it contains
          );
          continue;
        }

        const parentItemDetails: SquareSdkCatalogItem | undefined = variationCatalogObject.itemData;

        const nameToUse = parentItemDetails?.name || itemVariationDetails?.name || 'Unknown Item';
        logServer(LogLevel.DEBUG, functionName, `[NameResolution] For ${variationId}`, {
          parentName: parentItemDetails?.name,
          variationName: itemVariationDetails?.name,
          nameToUse,
        });

        let price = 0;
        if (
          itemVariationDetails?.priceMoney?.amount !== undefined &&
          itemVariationDetails.priceMoney.amount !== null
        ) {
          price = Number(itemVariationDetails.priceMoney.amount);
        }
        logServer(LogLevel.DEBUG, functionName, `[PriceCalculation] For ${variationId}`, {
          priceMoneyObject: itemVariationDetails?.priceMoney,
          rawAmount: itemVariationDetails?.priceMoney?.amount,
          calculatedPriceInCents: price,
        });

        let tokentype = 'C';
        if (tokenTypeAttributeKey && variationCatalogObject.customAttributeValues) {
          const attrValueObject = variationCatalogObject.customAttributeValues[
            tokenTypeAttributeKey
          ] as CatalogCustomAttributeValue | undefined;

          if (attrValueObject && typeof attrValueObject.stringValue === 'string') {
            if (attrValueObject.stringValue.length === 1) {
              tokentype = attrValueObject.stringValue;
              logServer(
                LogLevel.DEBUG,
                functionName,
                `[TokenType] Variation ${variationId} has tokentype: ${tokentype} from attribute '${attrValueObject.name}'.`
              );
            } else if (attrValueObject.stringValue.length > 0) {
              logServer(
                LogLevel.WARN,
                functionName,
                `[TokenType] Attribute for ${variationId} is '${attrValueObject.stringValue}' (not single char). Defaulting.`
              );
            } else {
              logServer(
                LogLevel.DEBUG,
                functionName,
                `[TokenType] Attribute for ${variationId} is an empty string. Defaulting.`
              );
            }
          } else if (attrValueObject) {
            logServer(
              LogLevel.WARN,
              functionName,
              `[TokenType] Attribute for ${variationId} exists but stringValue is not string/null. Defaulting.`,
              { attrValueObject }
            );
          } else {
            logServer(
              LogLevel.DEBUG,
              functionName,
              `[TokenType] Attribute key '${tokenTypeAttributeKey}' not found for ${variationId}. Defaulting.`
            );
          }
        } else if (tokenTypeAttributeKey) {
          logServer(
            LogLevel.DEBUG,
            functionName,
            `[TokenType] Variation ${variationId} has no customAttributeValues. Defaulting.`
          );
        } else {
          logServer(
            LogLevel.DEBUG,
            functionName,
            `[TokenType] No tokenTypeAttributeKey configured. Defaulting for ${variationId}.`
          );
        }

        const imageIdFromVariation = itemVariationDetails?.imageIds?.[0];
        const imageIdFromParent = parentItemDetails?.imageIds?.[0];
        const imageIdToUse = imageIdFromVariation || imageIdFromParent;

        const image = imageIdToUse
          ? `${API_ENDPOINTS.SELF_CHECKOUT_GET_IMAGE}?imageId=${imageIdToUse}`
          : SUBSCRIPTION_DEFAULTS.PRODUCT_FALLBACK_IMAGE_URL;
        logServer(LogLevel.DEBUG, functionName, `[ImageResolution] For ${variationId}`, {
          imageIdFromVariation,
          imageIdFromParent,
          imageIdToUse,
          finalImageUrl: image,
        });

        const catalogItemToAdd: CatalogItem = {
          variationId: variationId,
          name: nameToUse,
          price: price,
          limit: globalItemPurchaseLimit,
          image: image,
          tokentype: tokentype,
        };
        catalog.push(catalogItemToAdd);
        logServer(
          LogLevel.DEBUG,
          functionName,
          'Constructed internal CatalogItem from variation CatalogObject',
          { item: catalogItemToAdd }
        );
      }
      logServer(
        LogLevel.INFO,
        functionName,
        `Dynamic catalog populated with ${catalog.length} items.`
      );
    } else {
      logServer(LogLevel.WARN, functionName, 'No CatalogObject items returned from service call.');
    }
  } catch (catalogError) {
    logServer(LogLevel.ERROR, functionName, 'Error fetching or processing dynamic catalog', {
      error: (catalogError as Error).message,
      stack: (catalogError as Error).stack,
    });
  }

  logServer(
    LogLevel.INFO,
    functionName,
    'Initialization successful. Returning token and catalog.',
    { catalogCount: catalog.length }
  );
  return { claimAttemptToken, catalog };
}
