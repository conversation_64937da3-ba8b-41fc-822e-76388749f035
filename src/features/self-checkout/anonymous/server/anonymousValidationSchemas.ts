import 'server-only';
import { z } from 'zod';

import { verificationConfig } from '../lib/config/verificationConfig';

// Schema for SelectedItem as it appears in the token
const SelectedItemInTokenSchema = z.object({
  variationId: z.string().min(1),
  quantity: z.number().int().min(1),
  name: z.string().min(1, 'Item name cannot be empty in token'),
  tokentype: z.string().length(1, 'Token type must be a single character in token'),
  imageUrl: z.string().min(1, 'Item image URL cannot be empty in token'), // Basic check for non-empty string
});

// Base schema for all attempt tokens
const BaseAttemptTokenSchemaInternal = z.object({
  attemptId: z.string().uuid(),
  claimAttemptId: z.string().uuid(),
  email: z.string().email(),
  createdAt: z.number().positive(),
  updatedAt: z.number().positive(),
  step: z.number().int().min(1).max(3),
  status: z.enum(['pending_payment', 'ready_to_claim', 'claimed']),
  selectedItems: z.array(SelectedItemInTokenSchema).min(1),
  order_id: z.string().min(1),
  locationId: z.string().min(1), // Added: Location ID is now required in the token
});

// Step 1 Schema
export const step1TokenSchema = BaseAttemptTokenSchemaInternal.extend({
  step: z.literal(1),
  status: z.literal('pending_payment'),
  // No orderTotal in Step 1
  // locationId is inherited
});

// Step 2 Schema
export const step2TokenSchema = BaseAttemptTokenSchemaInternal.extend({
  step: z.literal(2),
  status: z.literal('ready_to_claim'),
  orderTotal: z.number().int().nonnegative(), // orderTotal is in cents, non-negative integer
  // locationId is inherited
});

// Step 3 Schema
export const step3TokenSchema = BaseAttemptTokenSchemaInternal.extend({
  step: z.literal(3),
  status: z.literal('claimed'),
  orderTotal: z.number().int().nonnegative(), // Carried from Step 2
  burnOrderId: z.string().min(1),
  // locationId is inherited
});

// Schema for Step 3 token used specifically in /verify-tap endpoint
const SERVER_VERIFICATION_WINDOW_MS_INTERNAL =
  verificationConfig.serverVerificationWindowHours * 60 * 60 * 1000;

export const step3TokenSchemaForVerification = step3TokenSchema.refine(
  data => Date.now() - data.updatedAt <= SERVER_VERIFICATION_WINDOW_MS_INTERNAL,
  {
    message: `Verification window expired (${verificationConfig.serverVerificationWindowHours} hours). Claim was made too long ago.`,
  }
);

// Schema for /payment-intent request body
export const paymentIntentPayloadSchema = z.object({
  email: z.string().email({ message: 'Valid email is required.' }),
  selectedItems: z // This is for the request, only variationId and quantity
    .array(
      z.object({
        variationId: z.string({ required_error: 'Product selection is missing.' }).min(1),
        quantity: z
          .number({ required_error: 'Quantity is missing.' })
          .int()
          .min(1, 'Quantity must be at least 1.'),
      }),
      { required_error: 'Item selection is required.' }
    )
    .min(1, 'At least one item must be selected.'),
  locationId: z.string().min(1).optional(), // Added: Optional locationId from frontend
});
