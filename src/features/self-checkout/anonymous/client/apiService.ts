import { createAsyncThunk } from '@reduxjs/toolkit';
import { jwtDecode } from 'jwt-decode';

import { apiPost } from '@/src/lib/apiClient';
import { API_ENDPOINTS } from '@/src/lib/constants';
import { LogLevel } from '@/src/lib/logging';
import { AppDispatch, RootState } from '@/src/store'; // Assuming AppDispatch and RootState are exported

import { logClient } from '../lib/logging';
import {
  FlowStatus,
  setCatalog,
  setClaimAttemptToken,
  setClaimDetailsRetry,
  setClaimListAttempts,
  setCurrentAttempt,
  setError,
  setFlowStatus,
  setLastAttemptEmail,
} from '../slices/anonymousFlowSlice';
import { CatalogItem, PurchaseRequestItem } from '../types'; // Ensure CatalogItem is your internal type
import {
  AnonymousAttemptTokenPayload,
  ClaimAttemptTokenPayload,
  isStep1Payload,
  isStep2Payload,
  isStep3Payload,
} from '../types/anonymousTokens';

import {
  AnonymousAttemptDexieRecord,
  getAllAttemptsByClaimId,
  getAttemptByAttemptId,
  getSetting,
  saveOrUpdateAttempt,
  saveSetting,
} from './dexieService';

const decodeAndSaveAttempt = async (tokenString: string): Promise<AnonymousAttemptDexieRecord> => {
  logClient(LogLevel.DEBUG, 'decodeAndSaveAttempt', 'Decoding and saving attempt token', {
    tokenSnippet: `${tokenString.substring(0, 10)}...`,
  });
  const payload = jwtDecode<AnonymousAttemptTokenPayload>(tokenString);
  const record: Omit<AnonymousAttemptDexieRecord, 'id'> = {
    attemptId: payload.attemptId,
    claimAttemptId: payload.claimAttemptId,
    latestTokenString: tokenString,
    latestPayload: payload,
    lastUpdated: payload.updatedAt,
  };
  try {
    await saveOrUpdateAttempt(record);
    logClient(LogLevel.DEBUG, 'decodeAndSaveAttempt', 'Dexie saveOrUpdateAttempt completed', {
      attemptId: payload.attemptId,
    });
    const savedRecord = await getAttemptByAttemptId(payload.attemptId);
    if (!savedRecord) {
      logClient(
        LogLevel.ERROR,
        'decodeAndSaveAttempt',
        'Failed to retrieve attempt immediately after saving',
        { attemptId: payload.attemptId }
      );
      throw new Error('Failed to save/retrieve attempt from Dexie');
    }
    return savedRecord;
  } catch (dexieError: any) {
    logClient(LogLevel.ERROR, 'decodeAndSaveAttempt', 'Dexie operation failed', {
      attemptId: payload.attemptId,
      error: dexieError.message,
      stack: dexieError.stack,
    });
    throw dexieError;
  }
};

export const initializeAnonymousFlow = createAsyncThunk<
  { claimAttemptToken: string; catalog: CatalogItem[] }, // Return type
  string | undefined, // Type of the argument passed to the thunk (locationId)
  { dispatch: AppDispatch; rejectValue: string; state: RootState } // Explicit ThunkAPI config
>(
  'anonymousFlow/initialize',
  async (locationId: string | undefined, { dispatch, rejectWithValue }) => {
    // Accept optional locationId
    dispatch(setFlowStatus('initializing'));
    logClient(LogLevel.INFO, 'initializeAnonymousFlow', 'Starting initialization');
    try {
      const claimToken = await getSetting('claimAttemptToken');
      logClient(LogLevel.DEBUG, 'initializeAnonymousFlow', 'Existing token from Dexie', {
        tokenExists: !!claimToken,
        locationIdForRequest: locationId,
      });

      const requestBody: { locationId?: string } = {};
      if (locationId) {
        requestBody.locationId = locationId;
      }
      dispatch(setFlowStatus('loadingInitData'));
      const response = await apiPost(
        API_ENDPOINTS.SELF_CHECKOUT_ANONYMOUS_INIT,
        requestBody, // Pass requestBody (which might contain locationId)
        {
          headers: claimToken ? { 'X-Claim-Attempt-Token': claimToken } : undefined,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        logClient(LogLevel.ERROR, 'initializeAnonymousFlow', 'API call failed', {
          status: response.status,
          error: errorData,
        });
        // throw new Error(errorData.error || 'Initialization failed');
        return rejectWithValue(errorData.error || 'Initialization API call failed');
      }

      const { claimAttemptToken: newClaimToken, catalog } = await response.json();

      await saveSetting('claimAttemptToken', newClaimToken);
      logClient(LogLevel.DEBUG, 'initializeAnonymousFlow', 'Saved claimAttemptToken to Dexie');

      dispatch(setClaimAttemptToken(newClaimToken));
      dispatch(setCatalog(catalog));
      dispatch(setFlowStatus('readyToBuy'));
      logClient(LogLevel.INFO, 'initializeAnonymousFlow', 'Initialization successful');

      return { claimAttemptToken: newClaimToken, catalog };
    } catch (error: any) {
      logClient(LogLevel.ERROR, 'initializeAnonymousFlow', 'Initialization failed', {
        error: error.message,
        stack: error.stack,
      });
      dispatch(setError(error.message || 'Failed to initialize'));
      return rejectWithValue(error.message || 'Failed to initialize due to an exception');
    }
  }
);

interface CreatePaymentIntentThunkArg {
  email: string;
  selectedItems: PurchaseRequestItem[];
  locationId?: string; // Added: Optional locationId
}

export const createPaymentIntent = createAsyncThunk<
  { checkoutUrl: string; attemptToken: string },
  CreatePaymentIntentThunkArg,
  { dispatch: AppDispatch; rejectValue: string; state: RootState } // Added dispatch to ThunkAPI config
>(
  'anonymousFlow/createPaymentIntent',
  async ({ email, selectedItems, locationId }, { dispatch, getState, rejectWithValue }) => {
    dispatch(setFlowStatus('creatingPayment'));
    logClient(LogLevel.INFO, 'createPaymentIntent', 'Starting payment intent creation', {
      email,
      locationId,
    });
    try {
      const state = getState();
      const claimToken = state.anonymousFlow.claimAttemptToken;

      if (!claimToken) {
        logClient(LogLevel.ERROR, 'createPaymentIntent', 'Claim token is missing');
        return rejectWithValue('Session token missing. Please refresh.');
      }

      const requestBody: any = { email, selectedItems };
      if (locationId) {
        requestBody.locationId = locationId;
      }

      const response = await apiPost(
        API_ENDPOINTS.SELF_CHECKOUT_ANONYMOUS_PAYMENT_INTENT,
        requestBody, // Use the constructed requestBody
        {
          headers: { 'X-Claim-Attempt-Token': claimToken },
          useLimitedUseToken: true,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        logClient(LogLevel.WARN, 'createPaymentIntent', 'API call failed', {
          status: response.status,
          error: errorData,
        });
        if (response.status === 400 && errorData.error?.includes('limit')) {
          return rejectWithValue(errorData.error);
        }
        return rejectWithValue(errorData.error || 'Failed to create payment intent.');
      }

      const { checkoutUrl, attemptToken } = await response.json();

      try {
        await decodeAndSaveAttempt(attemptToken);
        logClient(LogLevel.INFO, 'createPaymentIntent', 'Step 1 token saved to Dexie', {
          attemptId: jwtDecode<AnonymousAttemptTokenPayload>(attemptToken).attemptId,
        });
      } catch (dexieError: any) {
        logClient(LogLevel.ERROR, 'createPaymentIntent', 'Failed to save Step 1 token to Dexie', {
          error: dexieError.message,
          stack: dexieError.stack,
        });
        return rejectWithValue('Failed to save session locally. Please try again.');
      }

      logClient(LogLevel.INFO, 'createPaymentIntent', 'Proceeding to redirect', { checkoutUrl });
      return { checkoutUrl, attemptToken };
    } catch (error: any) {
      logClient(LogLevel.ERROR, 'createPaymentIntent', 'Payment intent creation failed', {
        error: error.message,
        stack: error.stack,
      });
      const message = (error as Error).message || 'Failed to start payment.';
      dispatch(setError(message));
      return rejectWithValue(message);
    }
  }
);

export const loadClaimDetails = createAsyncThunk<
  AnonymousAttemptDexieRecord | undefined, // Fulfilled payload type
  string, // Argument type (attemptId)
  { dispatch: AppDispatch; rejectValue: { status?: number; message: string }; state: RootState } // ThunkAPI config
>('anonymousFlow/loadClaimDetails', async (attemptId: string, { dispatch, rejectWithValue }) => {
  logClient(LogLevel.INFO, 'loadClaimDetails', `[${attemptId}] Starting fetch`, { attemptId });
  let attemptRecord: AnonymousAttemptDexieRecord | undefined;
  try {
    logClient(LogLevel.DEBUG, 'loadClaimDetails', `[${attemptId}] Fetching record from Dexie`);
    attemptRecord = await getAttemptByAttemptId(attemptId);

    if (!attemptRecord) {
      logClient(
        LogLevel.WARN,
        'loadClaimDetails',
        `[${attemptId}] Attempt record not found in Dexie`,
        { attemptId }
      );
      const notFoundErrorMsg = `Purchase attempt (${attemptId.slice(-6)}) not found locally. Please check your purchase list or start a new checkout.`;
      dispatch(setError(notFoundErrorMsg));
      dispatch(setFlowStatus('error'));
      return rejectWithValue({ status: 404, message: notFoundErrorMsg });
    }

    logClient(LogLevel.DEBUG, 'loadClaimDetails', `[${attemptId}] Found record in Dexie`, {
      attemptId,
      step: attemptRecord.latestPayload.step,
      status: attemptRecord.latestPayload.status,
    });

    if (
      isStep2Payload(attemptRecord.latestPayload) ||
      isStep3Payload(attemptRecord.latestPayload)
    ) {
      logClient(
        LogLevel.INFO,
        'loadClaimDetails',
        `[${attemptId}] Attempt already processed (Step ${attemptRecord.latestPayload.step}), using Dexie data.`,
        {
          attemptId,
          currentStatus: attemptRecord.latestPayload.status,
        }
      );
      dispatch(setCurrentAttempt(attemptRecord));
      const statusMap: Record<string, FlowStatus> = {
        ready_to_claim: 'readyToClaim',
        claimed: 'claimed',
      };
      const targetFlowStatus = statusMap[attemptRecord.latestPayload.status] || 'error';
      dispatch(setFlowStatus(targetFlowStatus));
      logClient(
        LogLevel.INFO,
        'loadClaimDetails',
        `[${attemptId}] Fulfilled with existing state: ${targetFlowStatus}`
      );
      return attemptRecord;
    }

    if (!isStep1Payload(attemptRecord.latestPayload)) {
      logClient(
        LogLevel.ERROR,
        'loadClaimDetails',
        `[${attemptId}] Record found but payload is not Step 1, 2, or 3. This is an inconsistent state.`,
        { attemptId, payload: attemptRecord.latestPayload }
      );
      const inconsistentStateError = 'Invalid local purchase data state.';
      dispatch(setError(inconsistentStateError));
      dispatch(setFlowStatus('error'));
      return rejectWithValue({ status: 500, message: inconsistentStateError });
    }

    const step1TokenString = attemptRecord.latestTokenString;
    logClient(
      LogLevel.DEBUG,
      'loadClaimDetails',
      `[${attemptId}] Found Step 1 record, calling /claim-details API`
    );

    const response = await apiPost(
      API_ENDPOINTS.SELF_CHECKOUT_ANONYMOUS_CLAIM_DETAILS,
      {},
      { headers: { 'X-Attempt-Token': step1TokenString } }
    );

    logClient(
      LogLevel.DEBUG,
      'loadClaimDetails',
      `[${attemptId}] API response status: ${response.status}`
    );

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage =
        errorData.error || `Failed to fetch purchase details (Status: ${response.status}).`;
      logClient(LogLevel.WARN, 'loadClaimDetails', `[${attemptId}] API call failed`, {
        status: response.status,
        error: errorMessage,
        attemptId,
      });
      if (response.status !== 409) {
        dispatch(setError(errorMessage));
        dispatch(setFlowStatus('error'));
      }
      return rejectWithValue({ status: response.status, message: errorMessage });
    }

    const { attemptToken: step2TokenString } = await response.json();
    logClient(LogLevel.DEBUG, 'loadClaimDetails', `[${attemptId}] Received Step 2 token from API`);

    const updatedRecord = await decodeAndSaveAttempt(step2TokenString);
    logClient(LogLevel.INFO, 'loadClaimDetails', `[${attemptId}] Step 2 token saved to Dexie`, {
      attemptId,
    });

    dispatch(setCurrentAttempt(updatedRecord));
    dispatch(setFlowStatus('readyToClaim'));
    logClient(
      LogLevel.INFO,
      'loadClaimDetails',
      `[${attemptId}] Fulfilled successfully, status set to readyToClaim`
    );
    return updatedRecord;
  } catch (error: any) {
    const generalErrorMessage = error.message || 'Failed to load purchase details.';
    logClient(LogLevel.ERROR, 'loadClaimDetails', `[${attemptId}] Thunk failed catastrophically`, {
      attemptId,
      error: generalErrorMessage,
      stack: error.stack,
    });
    dispatch(setError(generalErrorMessage));
    dispatch(setFlowStatus('error'));
    return rejectWithValue({ message: generalErrorMessage });
  }
});

export const claimTokens = createAsyncThunk<
  AnonymousAttemptDexieRecord, // Fulfilled payload type
  string, // Argument type (attemptId)
  { dispatch: AppDispatch; rejectValue: string; state: RootState } // ThunkAPI config
>('anonymousFlow/claimTokens', async (attemptId: string, { dispatch, rejectWithValue }) => {
  dispatch(setFlowStatus('claiming'));
  logClient(LogLevel.INFO, 'claimTokens', 'Starting claim process', { attemptId });
  try {
    const attemptRecord = await getAttemptByAttemptId(attemptId);
    if (!attemptRecord || !isStep2Payload(attemptRecord.latestPayload)) {
      logClient(LogLevel.ERROR, 'claimTokens', 'Attempt record invalid or not Step 2', {
        attemptId,
        payloadStep: attemptRecord?.latestPayload?.step,
      });
      const errorMessage = 'Invalid data for claiming. Please refresh or try again.';
      dispatch(setError(errorMessage));
      dispatch(setFlowStatus('error'));
      return rejectWithValue(errorMessage);
    }
    const step2TokenString = attemptRecord.latestTokenString;

    const response = await apiPost(
      API_ENDPOINTS.SELF_CHECKOUT_ANONYMOUS_CLAIM,
      {},
      {
        headers: { 'X-Attempt-Token': step2TokenString },
        useLimitedUseToken: true,
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      logClient(LogLevel.WARN, 'claimTokens', 'API call failed', {
        status: response.status,
        error: errorData,
        attemptId,
      });
      let userMessage = errorData.error || 'Failed to claim items.';
      if (response.status === 409) {
        userMessage = 'This purchase has already been claimed.';
      } else if (response.status === 428) {
        userMessage = 'Tokens invalid or already used.';
      }
      dispatch(setError(userMessage));
      dispatch(setFlowStatus('error'));
      return rejectWithValue(userMessage);
    }

    const { attemptToken: step3TokenString } = await response.json();

    const updatedRecord = await decodeAndSaveAttempt(step3TokenString);
    logClient(LogLevel.INFO, 'claimTokens', 'Claim successful, Step 3 token saved', {
      attemptId,
    });

    dispatch(setCurrentAttempt(updatedRecord));
    dispatch(setFlowStatus('claimed'));
    return updatedRecord;
  } catch (error: any) {
    logClient(LogLevel.ERROR, 'claimTokens', 'Claim failed catastrophically', {
      attemptId,
      error: error.message,
      stack: error.stack,
    });
    const message = (error as Error).message || 'Failed to claim items.';
    dispatch(setError(message));
    dispatch(setFlowStatus('error'));
    return rejectWithValue(message);
  }
});

export const verifyClaimTap = createAsyncThunk<
  { success: boolean }, // Fulfilled payload type
  string, // Argument type (attemptId)
  { dispatch: AppDispatch; rejectValue: string; state: RootState } // ThunkAPI config
>('anonymousFlow/verifyClaimTap', async (attemptId: string, { dispatch, rejectWithValue }) => {
  dispatch(setFlowStatus('verifying'));
  logClient(LogLevel.INFO, 'verifyClaimTap', 'Starting verification tap', { attemptId });
  try {
    const attemptRecord = await getAttemptByAttemptId(attemptId);
    if (!attemptRecord || !isStep3Payload(attemptRecord.latestPayload)) {
      logClient(LogLevel.ERROR, 'verifyClaimTap', 'Attempt record invalid or not Step 3', {
        attemptId,
        payloadStep: attemptRecord?.latestPayload?.step,
      });
      const errorMessage = 'Invalid data for verification. Please refresh.';
      dispatch(setError(errorMessage));
      dispatch(setFlowStatus('error'));
      return rejectWithValue(errorMessage);
    }
    const step3TokenString = attemptRecord.latestTokenString;

    const response = await apiPost(
      API_ENDPOINTS.SELF_CHECKOUT_ANONYMOUS_VERIFY_TAP,
      {},
      {
        headers: { 'X-Attempt-Token': step3TokenString },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      logClient(LogLevel.WARN, 'verifyClaimTap', 'API call failed', {
        status: response.status,
        error: errorData,
        attemptId,
      });
      let userMessage = errorData.error || 'Verification failed.';
      if (response.status === 401) {
        userMessage = errorData.error || 'Verification window expired or token invalid.';
      } else if (response.status === 404) {
        userMessage = 'Claim record not found for verification.';
      }
      dispatch(setError(userMessage));
      dispatch(setFlowStatus('claimed')); // Revert to 'claimed' on verification failure
      return rejectWithValue(userMessage);
    }

    const { success } = await response.json();

    if (success) {
      logClient(LogLevel.INFO, 'verifyClaimTap', 'Verification successful', { attemptId });
      dispatch(setFlowStatus('verified'));
    } else {
      logClient(LogLevel.WARN, 'verifyClaimTap', 'Verification returned false from API', {
        attemptId,
      });
      dispatch(setError('Verification check failed. Attendant should re-verify.'));
      dispatch(setFlowStatus('claimed')); // Revert to 'claimed'
    }

    return { success };
  } catch (error: any) {
    logClient(LogLevel.ERROR, 'verifyClaimTap', 'Verification failed catastrophically', {
      attemptId,
      error: error.message,
      stack: error.stack,
    });
    const message = (error as Error).message || 'Verification failed.';
    dispatch(setError(message));
    dispatch(setFlowStatus('claimed')); // Revert to 'claimed'
    return rejectWithValue(message);
  }
});

export const loadClaimList = createAsyncThunk<
  AnonymousAttemptDexieRecord[], // Fulfilled payload type
  void, // Argument type (none for this thunk)
  { dispatch: AppDispatch; rejectValue: string; state: RootState } // ThunkAPI config
>('anonymousFlow/loadClaimList', async (_, { dispatch, getState, rejectWithValue }) => {
  dispatch(setFlowStatus('loadingClaimList'));
  logClient(LogLevel.INFO, 'loadClaimList', 'Loading claim list from Dexie');
  try {
    const state = getState();
    const claimToken = state.anonymousFlow.claimAttemptToken;

    if (!claimToken) {
      logClient(LogLevel.WARN, 'loadClaimList', 'Cannot load list, claim token missing');
      dispatch(setClaimListAttempts([]));
      dispatch(setLastAttemptEmail(null));
      const currentCatalog = state.anonymousFlow.catalog;
      dispatch(setFlowStatus(currentCatalog && currentCatalog.length > 0 ? 'readyToBuy' : 'idle'));
      return []; // Return empty array as fulfilled value
    }

    const claimPayload = jwtDecode<ClaimAttemptTokenPayload>(claimToken);
    const attempts = await getAllAttemptsByClaimId(claimPayload.claimAttemptId);

    dispatch(setClaimListAttempts(attempts));

    if (attempts.length > 0) {
      const sortedAttempts = [...attempts].sort(
        (a, b) => b.latestPayload.createdAt - a.latestPayload.createdAt
      );
      if (sortedAttempts[0]?.latestPayload?.email) {
        dispatch(setLastAttemptEmail(sortedAttempts[0].latestPayload.email));
        logClient(
          LogLevel.DEBUG,
          'loadClaimList',
          'Set lastAttemptEmail from most recent attempt.',
          { email: sortedAttempts[0].latestPayload.email }
        );
      } else {
        dispatch(setLastAttemptEmail(null));
      }
    } else {
      dispatch(setLastAttemptEmail(null));
    }

    dispatch(setFlowStatus('claimListReady'));
    logClient(LogLevel.INFO, 'loadClaimList', `Loaded ${attempts.length} attempts`);
    return attempts;
  } catch (error: any) {
    logClient(LogLevel.ERROR, 'loadClaimList', 'Failed to load claim list', {
      error: error.message,
      stack: error.stack,
    });
    const message = (error as Error).message || 'Failed to load purchase history.';
    dispatch(setError(message));
    dispatch(setFlowStatus('claimListReady'));
    return rejectWithValue(message);
  }
});

export const retryClaimDetails = createAsyncThunk<
  AnonymousAttemptDexieRecord | undefined | null, // Fulfilled type (null if setting retry state)
  string, // Argument type (attemptId)
  { dispatch: AppDispatch; rejectValue: string | undefined; state: RootState } // rejectValue can be string or undefined
>('anonymousFlow/retryClaimDetails', async (attemptId: string, { dispatch, rejectWithValue }) => {
  logClient(LogLevel.INFO, 'retryClaimDetails', `[${attemptId}] Retrying claim details fetch`);
  try {
    const resultAction = await dispatch(loadClaimDetails(attemptId));

    if (loadClaimDetails.fulfilled.match(resultAction)) {
      logClient(LogLevel.INFO, 'retryClaimDetails', `[${attemptId}] Retry successful`);
      return resultAction.payload; // This is AnonymousAttemptDexieRecord | undefined
    } else if (loadClaimDetails.rejected.match(resultAction)) {
      const rejectionPayload = resultAction.payload as
        | { status?: number; message?: string }
        | undefined;
      const status = typeof rejectionPayload === 'object' ? rejectionPayload.status : undefined;
      const message: string | undefined =
        typeof rejectionPayload === 'object' && typeof rejectionPayload.message === 'string'
          ? rejectionPayload.message
          : typeof rejectionPayload === 'string' // This case seems unlikely with current loadClaimDetails rejectValue type
            ? rejectionPayload
            : undefined;

      if (status === 409) {
        logClient(
          LogLevel.WARN,
          'retryClaimDetails',
          `[${attemptId}] Order still not ready (409) on retry. Setting retry state.`
        );
        dispatch(setClaimDetailsRetry({ attemptId, retryAfterMs: 10000 }));
        return null; // Indicate that we are setting a retry state, not a final resolution
      } else {
        const finalMessage = message || 'Retry failed.';
        logClient(
          LogLevel.ERROR,
          'retryClaimDetails',
          `[${attemptId}] Retry failed with non-409 error`,
          { error: finalMessage, status }
        );
        dispatch(setError(finalMessage));
        return rejectWithValue(finalMessage);
      }
    }
    const unexpectedErrorMessage = 'Unexpected outcome during purchase detail retry.';
    logClient(
      LogLevel.ERROR,
      'retryClaimDetails',
      `[${attemptId}] Unexpected outcome during retry: ${unexpectedErrorMessage}`
    );
    dispatch(setError(unexpectedErrorMessage));
    dispatch(setFlowStatus('error'));
    return rejectWithValue(unexpectedErrorMessage);
  } catch (error: any) {
    logClient(
      LogLevel.ERROR,
      'retryClaimDetails',
      `[${attemptId}] Retry thunk execution failed catastrophically`,
      {
        error: error.message,
        stack: error.stack,
      }
    );
    const message = (error as Error).message || 'Retry failed.';
    dispatch(setError(message));
    dispatch(setFlowStatus('error'));
    return rejectWithValue(message);
  }
});
