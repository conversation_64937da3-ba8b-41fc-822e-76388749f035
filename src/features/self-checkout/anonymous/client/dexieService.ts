import <PERSON><PERSON>, { Table } from 'dexie';

import { logClient, LogLevel } from '@/src/lib/logging'; // Import LogLevel from shared logging

import { AnonymousAttemptTokenPayload } from '../types/anonymousTokens';

// Interface for the settings table
export interface SettingsRecord {
  key: string; // Primary Key (e.g., "claimAttemptToken")
  value: string | null; // The JWT string or null
}

// Interface for the attempts table record
export interface AnonymousAttemptDexieRecord {
  id?: number; // Auto-incrementing primary key (optional for <PERSON><PERSON>)
  attemptId: string; // Unique ID for the attempt (indexed, unique)
  claimAttemptId: string; // Links back to the browser session (indexed)
  latestTokenString: string; // The most recent JWT string for this attempt
  latestPayload: AnonymousAttemptTokenPayload; // Decoded payload of the latest token
  lastUpdated: number; // Timestamp (ms) of the last update (indexed for sorting)
}

export class SelfCheckoutDexie extends Dexie {
  // Explicitly declare tables and their types
  attempts!: Table<AnonymousAttemptDexieRecord, number>; // Use number for auto-incrementing key
  settings!: Table<SettingsRecord, string>; // Use string for 'key'

  constructor() {
    super('veryMulberrySelfCheckoutDB_v1'); // Database name
    logClient(LogLevel.INFO, 'SelfCheckoutDexie.constructor', 'Database instance created/opened');
    this.version(1).stores({
      // Define schema for version 1
      // ++id: auto-incrementing primary key
      // &attemptId: unique index on attemptId
      // claimAttemptId: index for querying by claim ID
      // lastUpdated: index for sorting/cleanup
      attempts: '++id, &attemptId, claimAttemptId, lastUpdated',
      // key: primary key for settings table
      settings: 'key',
    });
    // Initialize tables (optional but good practice)
    this.attempts = this.table('attempts');
    this.settings = this.table('settings');
    logClient(LogLevel.DEBUG, 'SelfCheckoutDexie.constructor', 'Tables initialized');
  }
}

// Create and export a single instance of the Dexie database
export const db = new SelfCheckoutDexie();

// --- CRUD Functions ---

/**
 * Saves or updates a setting in the settings table.
 * @param key The key of the setting (e.g., "claimAttemptToken").
 * @param value The value to save (JWT string or null).
 * @returns Promise resolving to the key upon successful save/update.
 */
export async function saveSetting(key: string, value: string | null): Promise<string> {
  logClient(LogLevel.DEBUG, 'dexieService.saveSetting', `Attempting to save setting`, {
    key,
    value: value ? `${value.substring(0, 10)}...` : null,
  });
  try {
    const resultKey = await db.settings.put({ key, value }, key); // Use put for upsert behavior
    logClient(LogLevel.INFO, 'dexieService.saveSetting', `Successfully saved/updated setting`, {
      key: resultKey,
    });
    return resultKey;
  } catch (error: any) {
    // Catch specific error
    logClient(LogLevel.ERROR, 'dexieService.saveSetting', `Failed to save/update setting`, {
      key,
      errorName: error.name,
      errorMessage: error.message,
      stack: error.stack,
    });
    throw error; // Re-throw to allow caller to handle
  }
}

/**
 * Retrieves a setting value from the settings table.
 * @param key The key of the setting to retrieve.
 * @returns Promise resolving to the value (string or null) or null if not found.
 */
export async function getSetting(key: string): Promise<string | null> {
  logClient(LogLevel.DEBUG, 'dexieService.getSetting', `Attempting to retrieve setting`, { key });
  try {
    const record = await db.settings.get(key);
    logClient(LogLevel.INFO, 'dexieService.getSetting', `Setting retrieval result`, {
      key,
      found: !!record,
      value: record?.value ? `${record.value.substring(0, 10)}...` : null,
    });
    return record?.value ?? null;
  } catch (error: any) {
    // Catch specific error
    logClient(LogLevel.ERROR, 'dexieService.getSetting', `Failed to retrieve setting`, {
      key,
      errorName: error.name,
      errorMessage: error.message,
      stack: error.stack,
    });
    throw error; // Re-throw
  }
}

/**
 * Saves a new attempt record or updates an existing one based on attemptId.
 * @param attemptData The attempt data to save or update (excluding the auto-incrementing id).
 * @returns Promise resolving to the primary key (id) of the saved/updated record.
 * @throws Throws Dexie error if the operation fails.
 */
export async function saveOrUpdateAttempt(
  attemptData: Omit<AnonymousAttemptDexieRecord, 'id'>
): Promise<number> {
  logClient(
    LogLevel.DEBUG,
    'dexieService.saveOrUpdateAttempt',
    `Attempting to save/update attempt`,
    {
      attemptId: attemptData.attemptId,
      step: attemptData.latestPayload.step,
      status: attemptData.latestPayload.status,
    }
  );
  try {
    // Check if an attempt with this attemptId already exists
    const existingAttempt = await db.attempts
      .where('attemptId')
      .equals(attemptData.attemptId)
      .first();

    const dataToSave: AnonymousAttemptDexieRecord = {
      ...attemptData,
      lastUpdated: Date.now(), // Ensure lastUpdated is always set on save/update
      // If existingAttempt is found, use its auto-incremented 'id' for the update.
      // Otherwise, 'id' will be undefined, and Dexie's 'put' will auto-increment.
      ...(existingAttempt && existingAttempt.id !== undefined && { id: existingAttempt.id }),
    };

    // 'put' will update if 'id' is provided and exists, otherwise it adds.
    // The unique index on 'attemptId' is the primary concern for ConstraintError.
    // By ensuring we update the existing record (by its primary key 'id') when an attemptId
    // match is found, we avoid trying to insert a duplicate attemptId.
    const id = await db.attempts.put(dataToSave);

    logClient(
      LogLevel.INFO,
      'dexieService.saveOrUpdateAttempt',
      `Successfully ${existingAttempt ? 'updated' : 'saved'} attempt`,
      {
        attemptId: attemptData.attemptId,
        dexieId: id,
      }
    );
    if (typeof id !== 'number') {
      logClient(
        LogLevel.ERROR,
        'dexieService.saveOrUpdateAttempt',
        `Dexie put operation did not return a valid ID`,
        { attemptId: attemptData.attemptId, returnedId: id }
      );
      throw new Error(`Failed to save/update attempt: Invalid ID returned by Dexie.`);
    }
    return id;
  } catch (error: any) {
    logClient(LogLevel.ERROR, 'dexieService.saveOrUpdateAttempt', `Failed to save/update attempt`, {
      attemptId: attemptData.attemptId,
      errorName: error.name,
      errorMessage: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

/**
 * Retrieves a specific attempt record by its attemptId.
 * @param attemptId The unique ID of the attempt to retrieve.
 * @returns Promise resolving to the attempt record or undefined if not found.
 */
export async function getAttemptByAttemptId(
  attemptId: string
): Promise<AnonymousAttemptDexieRecord | undefined> {
  logClient(
    LogLevel.DEBUG,
    'dexieService.getAttemptByAttemptId',
    `Attempting to retrieve attempt`,
    {
      attemptId,
    }
  );
  try {
    // Explicitly use the index 'attemptId' for the query
    const record = await db.attempts.where('attemptId').equals(attemptId).first();
    logClient(LogLevel.INFO, 'dexieService.getAttemptByAttemptId', `Attempt retrieval result`, {
      attemptId,
      found: !!record,
      step: record?.latestPayload?.step,
    });
    return record;
  } catch (error: any) {
    // Catch specific error
    logClient(LogLevel.ERROR, 'dexieService.getAttemptByAttemptId', `Failed to retrieve attempt`, {
      attemptId,
      errorName: error.name,
      errorMessage: error.message,
      stack: error.stack,
    });
    throw error; // Re-throw
  }
}

/**
 * Retrieves all attempt records associated with a specific claimAttemptId.
 * Sorted by lastUpdated descending (most recent first).
 * @param claimAttemptId The ID of the browser session.
 * @returns Promise resolving to an array of attempt records.
 */
export async function getAllAttemptsByClaimId(
  claimAttemptId: string
): Promise<AnonymousAttemptDexieRecord[]> {
  logClient(
    LogLevel.DEBUG,
    'dexieService.getAllAttemptsByClaimId',
    `Attempting to retrieve all attempts for claim`,
    {
      claimAttemptId,
    }
  );
  try {
    const records = await db.attempts
      .where('claimAttemptId')
      .equals(claimAttemptId)
      .reverse() // Sort descending by primary key (usually correlates with time)
      .sortBy('lastUpdated'); // Explicitly sort by lastUpdated descending
    logClient(
      LogLevel.INFO,
      'dexieService.getAllAttemptsByClaimId',
      `Retrieved ${records.length} attempts for claim`,
      {
        claimAttemptId,
      }
    );
    return records;
  } catch (error: any) {
    // Catch specific error
    logClient(
      LogLevel.ERROR,
      'dexieService.getAllAttemptsByClaimId',
      `Failed to retrieve attempts for claim`,
      {
        claimAttemptId,
        errorName: error.name,
        errorMessage: error.message,
        stack: error.stack,
      }
    );
    throw error; // Re-throw
  }
}

/**
 * Deletes a specific attempt record by its attemptId.
 * @param attemptId The unique ID of the attempt to delete.
 * @returns Promise resolving when the deletion is complete.
 */
export async function deleteAttemptById(attemptId: string): Promise<void> {
  logClient(LogLevel.DEBUG, 'dexieService.deleteAttemptById', `Attempting to delete attempt`, {
    attemptId,
  });
  try {
    await db.attempts.where('attemptId').equals(attemptId).delete();
    logClient(LogLevel.INFO, 'dexieService.deleteAttemptById', `Successfully deleted attempt`, {
      attemptId,
    });
  } catch (error: any) {
    // Catch specific error
    logClient(LogLevel.ERROR, 'dexieService.deleteAttemptById', `Failed to delete attempt`, {
      attemptId,
      errorName: error.name,
      errorMessage: error.message,
      stack: error.stack,
    });
    throw error; // Re-throw
  }
}

/**
 * Removes old 'pending_payment' attempts for a given claimAttemptId.
 * @param claimAttemptId The ID of the browser session.
 * @param thresholdMs The age threshold in milliseconds (e.g., 24 * 60 * 60 * 1000 for 24 hours).
 * @returns Promise resolving to the number of records deleted.
 */
export async function cleanupOldPendingAttempts(
  claimAttemptId: string,
  thresholdMs: number
): Promise<number> {
  const cutoffTimestamp = Date.now() - thresholdMs;
  logClient(
    LogLevel.INFO,
    'dexieService.cleanupOldPendingAttempts',
    `Attempting to cleanup old pending attempts`,
    {
      claimAttemptId,
      thresholdMs,
      cutoffTimestamp,
    }
  );
  try {
    const count = await db.attempts
      .where('claimAttemptId')
      .equals(claimAttemptId)
      .and(record => record.latestPayload.status === 'pending_payment')
      .and(record => record.lastUpdated < cutoffTimestamp)
      .delete();
    logClient(
      LogLevel.INFO,
      'dexieService.cleanupOldPendingAttempts',
      `Cleanup successful. Deleted ${count} old pending records.`,
      { claimAttemptId }
    );
    return count;
  } catch (error: any) {
    // Catch specific error
    logClient(
      LogLevel.ERROR,
      'dexieService.cleanupOldPendingAttempts',
      `Cleanup failed for old pending attempts`,
      {
        claimAttemptId,
        errorName: error.name,
        errorMessage: error.message,
        stack: error.stack,
      }
    );
    // Do not re-throw here to allow the main flow to continue.
    // The original function returns 0 on error.
    return 0;
  }
}
