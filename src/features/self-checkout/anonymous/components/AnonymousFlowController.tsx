import { useSearchParams, useRouter } from 'next/navigation'; // Added for reading URL query params
import { useEffect, useState, useMemo } from 'react';

import Spinner from '@/src/components/spinner';
import {
  selectIsAuthenticated,
  selectAuthLoading,
} from '@/src/features/auth/slices/authenticationSlice';
import { useAppDispatch, useAppSelector } from '@/src/store';

import { initializeAnonymousFlow, loadClaimList } from '../client/apiService';
import { logClient, LogLevel } from '../lib/logging';
import {
  selectAnonymousFlowStatus,
  selectAnonymousFlowError,
  selectCatalog,
  selectClaimAttemptToken,
  setFlowStatus,
  FlowStatus,
} from '../slices/anonymousFlowSlice';

import BuyView from './BuyView';
import ClaimListView from './ClaimListView';

const AnonymousFlowController: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams(); // For reading locationId

  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isAuthLoading = useAppSelector(selectAuthLoading);

  const flowStatus = useAppSelector(selectAnonymousFlowStatus);
  const flowError = useAppSelector(selectAnonymousFlowError);
  const catalog = useAppSelector(selectCatalog);
  const claimAttemptToken = useAppSelector(selectClaimAttemptToken);

  const [claimListLoadDispatchedForToken, setClaimListLoadDispatchedForToken] = useState<
    string | null
  >(null);

  // Memoize locationId from URL to avoid re-triggering effects unnecessarily
  const locationIdFromUrl = useMemo(() => {
    return searchParams?.get('locationId') || undefined;
  }, [searchParams]);

  useEffect(() => {
    if (!isAuthLoading && isAuthenticated) {
      logClient(
        LogLevel.WARN,
        'AnonymousFlowController.useEffect[AuthRedirect]',
        'User is authenticated, redirecting to / from /self-check.'
      );
      router.replace('/');
    }
  }, [isAuthLoading, isAuthenticated, router]);

  // Effect for status synchronization and initialization
  useEffect(() => {
    if (isAuthLoading || isAuthenticated) {
      return;
    }
    logClient(LogLevel.DEBUG, 'AnonymousFlowController.useEffect[StatusSync]', 'Effect running', {
      locationIdFromUrl,
      flowStatus,
    });

    // If essentials are loaded (catalog and token)
    if (catalog && claimAttemptToken) {
      // Check if the current flowStatus is one that indicates a "stale" state
      // for the /self-check page (e.g., user was on /claim and navigated back).
      // These statuses should be reset to 'readyToBuy'.
      // Transient states like 'creatingPayment', 'loadingInitData', 'initializing', 'error', 'loadingClaimList', 'claimListReady', 'claimDetailsRetryWait'
      // should NOT be reset by this specific logic, as they are part of active processes or valid states for this page.
      const staleStatusesForFarmCheckout: FlowStatus[] = [
        'readyToClaim', // Came from /claim without finishing
        'claiming', // Came from /claim during claim process
        'claimed', // Came from /claim after claiming
        'verifying', // Came from /claim during verification
        'verified', // Came from /claim after verification
        'loadingClaimDetails', // If user navigates back while claim details are loading for a specific attempt
      ];

      if (staleStatusesForFarmCheckout.includes(flowStatus)) {
        logClient(
          LogLevel.INFO,
          'AnonymousFlowController.useEffect[StatusSync]',
          `Resetting stale flowStatus '${flowStatus}' to 'readyToBuy' for /self-check page.`
        );
        dispatch(setFlowStatus('readyToBuy')); // This also clears currentAttempt in the slice
      }
      // If flowStatus is 'readyToBuy', 'error', 'loadingClaimList', 'claimListReady', 'claimDetailsRetryWait', 'creatingPayment', etc., do nothing here.
    }
    // If essentials are NOT loaded, and we're not already initializing or in an error state
    else if (!catalog && !['initializing', 'loadingInitData', 'error'].includes(flowStatus)) {
      logClient(
        LogLevel.INFO,
        'AnonymousFlowController.useEffect[StatusSync]',
        `No catalog, and not initializing/error. Dispatching initializeAnonymousFlow. Current status: ${flowStatus}`,
        { locationIdToInitializeWith: locationIdFromUrl }
      );
      dispatch(initializeAnonymousFlow(locationIdFromUrl)); // Pass locationIdFromUrl
    }
  }, [
    isAuthLoading,
    isAuthenticated,
    catalog,
    claimAttemptToken,
    flowStatus,
    dispatch,
    locationIdFromUrl,
  ]);

  useEffect(() => {
    const isListCurrentlyLoading = flowStatus === 'loadingClaimList';
    const canLoadClaimList =
      flowStatus === 'readyToBuy' && // Only attempt to load list if we are generally ready
      catalog &&
      catalog.length > 0 &&
      claimAttemptToken &&
      claimAttemptToken !== claimListLoadDispatchedForToken &&
      !isListCurrentlyLoading;

    logClient(
      LogLevel.DEBUG,
      'AnonymousFlowController.useEffect[ClaimListLoad]',
      'Checking conditions to load claim list.',
      {
        flowStatus,
        catalogExistsAndPopulated: !!(catalog && catalog.length > 0),
        claimAttemptTokenExists: !!claimAttemptToken,
        isNewTokenForListLoad: claimAttemptToken !== claimListLoadDispatchedForToken,
        isListLoading: isListCurrentlyLoading,
        canLoadClaimList,
      }
    );

    if (canLoadClaimList) {
      logClient(
        LogLevel.INFO,
        'AnonymousFlowController.useEffect[ClaimListLoad]',
        'Conditions met, dispatching loadClaimList.',
        { claimAttemptToken }
      );
      dispatch(loadClaimList());
      setClaimListLoadDispatchedForToken(claimAttemptToken);
    }
  }, [flowStatus, catalog, dispatch, claimAttemptToken, claimListLoadDispatchedForToken]);

  useEffect(() => {
    const isListCurrentlyLoading = flowStatus === 'loadingClaimList';
    const handleReFocusOrVisibility = () => {
      logClient(
        LogLevel.DEBUG,
        'AnonymousFlowController.handleReFocusOrVisibility',
        'Event triggered.',
        { visibilityState: document.visibilityState }
      );
      if (
        document.visibilityState === 'visible' &&
        claimAttemptToken &&
        catalog &&
        catalog.length > 0 &&
        !isListCurrentlyLoading &&
        flowStatus !== 'initializing' && // Don't refresh if still initializing
        flowStatus !== 'loadingInitData' && // Don't refresh if still loading init data
        flowStatus !== 'creatingPayment' // Don't refresh list if payment creation is in progress
      ) {
        logClient(
          LogLevel.INFO,
          'AnonymousFlowController',
          'Page re-focused/visible, refreshing claim list.',
          { claimAttemptToken, currentFlowStatus: flowStatus }
        );
        dispatch(loadClaimList());
      } else {
        logClient(
          LogLevel.DEBUG,
          'AnonymousFlowController.handleReFocusOrVisibility',
          'Skipping refresh.',
          {
            visibilityState: document.visibilityState,
            claimAttemptTokenExists: !!claimAttemptToken,
            catalogExistsAndPopulated: !!(catalog && catalog.length > 0),
            currentFlowStatus: flowStatus,
            isListLoading: isListCurrentlyLoading,
          }
        );
      }
    };

    window.addEventListener('visibilitychange', handleReFocusOrVisibility);
    window.addEventListener('focus', handleReFocusOrVisibility);

    logClient(LogLevel.DEBUG, 'AnonymousFlowController', 'Added visibility/focus listeners.');

    return () => {
      window.removeEventListener('visibilitychange', handleReFocusOrVisibility);
      window.removeEventListener('focus', handleReFocusOrVisibility);
      logClient(LogLevel.DEBUG, 'AnonymousFlowController', 'Removed visibility/focus listeners.');
    };
  }, [dispatch, claimAttemptToken, catalog, flowStatus]);

  logClient(LogLevel.DEBUG, 'AnonymousFlowController.render', 'Determining UI to render.', {
    flowStatus,
    isAuthLoading,
    catalogExists: !!catalog,
  });

  if (isAuthLoading) {
    logClient(LogLevel.DEBUG, 'AnonymousFlowController.render', 'Rendering: Auth loading spinner.');
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <Spinner />
      </div>
    );
  }

  if (isAuthenticated) {
    logClient(
      LogLevel.DEBUG,
      'AnonymousFlowController.render',
      'Rendering: Authenticated, should be redirecting.'
    );
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] space-y-2">
        <Spinner />
        <p className="ml-2 text-gray-600">Redirecting...</p>
      </div>
    );
  }

  // Show main initialization spinner if status is idle, initializing, or loadingInitData AND catalog isn't loaded yet.
  // This ensures that even if catalog is null briefly after init, we show loading.
  if (flowStatus === 'idle' || flowStatus === 'initializing' || flowStatus === 'loadingInitData') {
    logClient(
      LogLevel.DEBUG,
      'AnonymousFlowController.render',
      'Rendering: Main initialization spinner.',
      { flowStatus }
    );
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] space-y-2">
        <Spinner />
        <p className="ml-2 text-gray-600">Initializing session...</p>
      </div>
    );
  }

  if (flowStatus === 'error' && flowError) {
    logClient(LogLevel.ERROR, 'AnonymousFlowController.render', 'Rendering: Error UI.', {
      flowError,
    });
    return (
      <div className="p-4 text-center text-red-600">
        <p>Error: {flowError || 'An unexpected error occurred.'}</p>
        <button
          onClick={() => {
            setClaimListLoadDispatchedForToken(null); // Reset this to allow list loading on next successful init
            logClient(
              LogLevel.INFO,
              'AnonymousFlowController.render',
              'Retry clicked, dispatching initializeAnonymousFlow.'
            );
            dispatch(setFlowStatus('idle')); // Explicitly set to idle to re-trigger init
            dispatch(initializeAnonymousFlow(locationIdFromUrl)); // Pass locationId on retry
          }}
          className="mt-2 px-4 py-1 bg-mulberry text-white rounded hover:bg-mulberryHover"
        >
          Retry
        </button>
      </div>
    );
  }

  // Active states where BuyView and ClaimListView should be shown
  // 'creatingPayment' is included here as an active state for this page.
  const activeFarmCheckoutViewStatuses: FlowStatus[] = [
    'readyToBuy',
    'creatingPayment',
    'loadingClaimList',
    'claimListReady',
    'claimDetailsRetryWait',
    // 'error' is handled above, but if an error occurs while in one of these states,
    // the UI for BuyView/ClaimListView might still be relevant with an error message.
  ];

  if (activeFarmCheckoutViewStatuses.includes(flowStatus) || (flowStatus === 'error' && catalog)) {
    // Show content even on error if catalog exists
    if (catalog && catalog.length > 0) {
      logClient(
        LogLevel.DEBUG,
        'AnonymousFlowController.render',
        'Rendering: BuyView and ClaimListView layout.',
        { flowStatus }
      );
      return (
        <div className="space-y-6">
          <BuyView />
          <ClaimListView />
        </div>
      );
    } else if (
      catalog &&
      catalog.length === 0 &&
      (flowStatus === 'readyToBuy' || flowStatus === 'claimListReady')
    ) {
      // Only show "no products" if the status is stable and implies products should be shown.
      logClient(
        LogLevel.WARN,
        'AnonymousFlowController.render',
        'Catalog is empty. No items to display for purchase.',
        { flowStatus }
      );
      return (
        <div className="p-4 text-center text-gray-600 space-y-6">
          <p>No products are currently available for self-checkout.</p>
          <ClaimListView />
        </div>
      );
    } else {
      // Fallback spinner if catalog is somehow null/empty in an active state where it's expected.
      logClient(
        LogLevel.WARN,
        'AnonymousFlowController.render',
        'In active flow state but catalog is missing or empty in an unexpected way. Rendering loading.',
        { flowStatus, catalogExists: !!catalog, catalogLength: catalog?.length }
      );
      return (
        <div className="flex flex-col items-center justify-center min-h-[200px] space-y-2">
          <Spinner />
          <p className="ml-2 text-gray-600">Loading products...</p>
        </div>
      );
    }
  }

  logClient(
    LogLevel.WARN,
    'AnonymousFlowController.render',
    'Rendering: Fallback loading spinner for unexpected state.',
    { flowStatus, catalogExists: !!catalog, catalogLength: catalog?.length }
  );
  return (
    <div className="flex flex-col items-center justify-center min-h-[200px] space-y-2">
      <Spinner />
      <p className="ml-2 text-gray-600">Please wait...</p>
    </div>
  );
};

export default AnonymousFlowController;
