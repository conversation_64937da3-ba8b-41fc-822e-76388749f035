'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation'; // Added: For reading URL query params
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Input } from '@/src/components/ui/input';
import { Label } from '@/src/components/ui/label';
import { apiGet } from '@/src/lib/apiClient';
import { useAppDispatch, useAppSelector } from '@/src/store';

import { createPaymentIntent } from '../client/apiService';
import { logClient, LogLevel } from '../lib/logging';
import {
  selectCatalog,
  selectAnonymousFlowStatus,
  selectAnonymousFlowError,
  FlowStatus,
  selectLastAttemptEmail,
} from '../slices/anonymousFlowSlice';
import { PurchaseRequestItem } from '../types';

const buyFormSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
});

type BuyFormValues = z.infer<typeof buyFormSchema>;

const CatalogImage: React.FC<{ src?: string; alt: string }> = ({ src, alt }) => {
  const [imageSrc, setImageSrc] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let objectUrl: string | undefined;

    const fetchImage = async () => {
      if (!src) {
        setIsLoading(false);
        return;
      }
      if (!src.startsWith('/api/')) {
        setImageSrc(src);
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      try {
        const response = await apiGet(src);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }
        const blob = await response.blob();
        objectUrl = URL.createObjectURL(blob);
        setImageSrc(objectUrl);
      } catch (error) {
        console.error('Error fetching image for BuyView:', error);
        setImageSrc(undefined);
      } finally {
        setIsLoading(false);
      }
    };

    fetchImage();

    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [src]);

  if (isLoading && src) {
    return <div className="w-16 h-16 bg-gray-200 rounded animate-pulse"></div>;
  }

  if (!imageSrc && src) {
    return (
      <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center text-gray-400 text-xs">
        No Image
      </div>
    );
  }

  if (!src) {
    return (
      <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center text-gray-400 text-xs">
        No Image
      </div>
    );
  }

  return (
    <Image
      src={imageSrc || ''}
      alt={alt}
      width={64}
      height={64}
      className="rounded object-cover"
      unoptimized={!!imageSrc && imageSrc.startsWith('blob:')}
    />
  );
};

const BuyView: React.FC = () => {
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams(); // Added: Hook to access search params
  const catalog = useAppSelector(selectCatalog);
  const flowStatus = useAppSelector(selectAnonymousFlowStatus);
  const error = useAppSelector(selectAnonymousFlowError);
  const lastAttemptEmail = useAppSelector(selectLastAttemptEmail);
  const isLoading = flowStatus === 'creatingPayment';
  const [isRedirecting, setIsRedirecting] = useState(false);

  const [itemQuantities, setItemQuantities] = useState<Record<string, number>>({});

  const isMountedRef = useRef(false);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    setError: setFormError,
    clearErrors,
    reset,
  } = useForm<BuyFormValues>({
    resolver: zodResolver(buyFormSchema),
    defaultValues: {
      email: '',
    },
  });

  useEffect(() => {
    if (lastAttemptEmail && !isDirty) {
      reset({ email: lastAttemptEmail });
      logClient(
        LogLevel.DEBUG,
        'BuyView.useEffect[EmailPrepop]',
        'Pre-populated email from last attempt.',
        { email: lastAttemptEmail }
      );
    }
  }, [lastAttemptEmail, reset, isDirty]);

  const memoizedClearErrors = useCallback(() => {
    clearErrors();
  }, [clearErrors]);

  const prevFlowStatusRef = useRef<FlowStatus | null>(null);
  const prevErrorRef = useRef<string | null>(null);

  useEffect(() => {
    const justResolvedReduxError = prevErrorRef.current !== null && error === null;
    const justTransitionedFromErrorStatus =
      prevFlowStatusRef.current === 'error' && flowStatus !== 'error';

    if (justResolvedReduxError || justTransitionedFromErrorStatus) {
      logClient(
        LogLevel.DEBUG,
        'BuyView.useEffect[ErrorClear]',
        'Global error resolved or status transitioned from error, clearing form errors.',
        {
          prevError: prevErrorRef.current,
          currentError: error,
          prevStatus: prevFlowStatusRef.current,
          currentStatus: flowStatus,
        }
      );
      memoizedClearErrors();
    }

    prevFlowStatusRef.current = flowStatus;
    prevErrorRef.current = error;
  }, [error, flowStatus, memoizedClearErrors]);

  useEffect(() => {
    if (catalog) {
      const initialQuantities = catalog.reduce(
        (acc, item) => {
          acc[item.variationId] = 0;
          return acc;
        },
        {} as Record<string, number>
      );
      setItemQuantities(initialQuantities);
    }
  }, [catalog]);

  const handleQuantityChange = (variationId: string, change: number, limit: number) => {
    setItemQuantities(prev => {
      const currentQuantity = prev[variationId] || 0;
      const newQuantity = Math.max(0, Math.min(currentQuantity + change, limit));
      return { ...prev, [variationId]: newQuantity };
    });
  };

  const onSubmit = async (data: BuyFormValues) => {
    memoizedClearErrors();
    const itemsToPurchase: PurchaseRequestItem[] = Object.entries(itemQuantities)
      .filter(([, quantity]) => quantity > 0)
      .map(([variationId, quantity]) => ({ variationId, quantity }));

    if (itemsToPurchase.length === 0) {
      setFormError('root.serverError', {
        type: 'manual',
        message: 'Please select at least one item.',
      });
      return;
    }

    let locationIdFromUrl: string | null = null;
    if (searchParams) {
      for (const [key, value] of searchParams.entries()) {
        if (key.toLowerCase() === 'locationid') {
          locationIdFromUrl = value;
          break;
        }
      }
    }
    logClient(LogLevel.INFO, 'BuyView.onSubmit', 'Preparing to create payment intent.', {
      email: data.email,
      itemsCount: itemsToPurchase.length,
      locationIdFromUrl: locationIdFromUrl,
    });

    const resultAction = await dispatch(
      createPaymentIntent({
        email: data.email,
        selectedItems: itemsToPurchase,
        locationId: locationIdFromUrl || undefined, // Added: Pass locationId if present
      })
    );

    if (createPaymentIntent.fulfilled.match(resultAction)) {
      if (isMountedRef.current) {
        const { checkoutUrl } = resultAction.payload;
        setIsRedirecting(true);
        window.location.href = checkoutUrl;
      } else {
        logClient(
          LogLevel.INFO,
          'BuyView.onSubmit',
          'Component unmounted before redirect could occur.'
        );
      }
    } else if (createPaymentIntent.rejected.match(resultAction)) {
      if (isMountedRef.current) {
        setFormError('root.serverError', {
          type: 'manual',
          message: (resultAction.payload as string) || 'Failed to proceed.',
        });
        setIsRedirecting(false);
      }
    }
  };

  if (!catalog || catalog.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-center text-mulberry font-bold text-xl py-2">
            Buy and Claim Clamshells
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-4">Loading products...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center text-mulberry font-bold text-xl py-2">
          Buy and Claim Clamshells
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address (for receipt)</Label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  {...field}
                  disabled={isLoading || isRedirecting}
                />
              )}
            />
            {errors.email && <p className="text-sm text-red-600">{errors.email.message}</p>}
          </div>

          <div className="space-y-4">
            {catalog.map(item => (
              <div
                key={item.variationId}
                className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 border rounded-md bg-gray-50 space-y-2 sm:space-y-0"
              >
                <div className="flex items-center space-x-3 w-full sm:w-auto">
                  <CatalogImage src={item.image} alt={item.name} />
                  <div className="flex-grow">
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-600">${(item.price / 100).toFixed(2)} each</p>
                    <p className="text-xs text-gray-500">Limit: {item.limit}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-1.5 sm:space-x-2 self-end sm:self-center">
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="h-8 w-8 flex-shrink-0"
                    onClick={() => handleQuantityChange(item.variationId, -1, item.limit)}
                    disabled={
                      isLoading || isRedirecting || (itemQuantities[item.variationId] || 0) <= 0
                    }
                  >
                    -
                  </Button>
                  <span className="w-8 sm:w-10 text-center font-medium">
                    {itemQuantities[item.variationId] || 0}
                  </span>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="h-8 w-8 flex-shrink-0"
                    onClick={() => handleQuantityChange(item.variationId, 1, item.limit)}
                    disabled={
                      isLoading ||
                      isRedirecting ||
                      (itemQuantities[item.variationId] || 0) >= item.limit
                    }
                  >
                    +
                  </Button>
                </div>
              </div>
            ))}
            {errors.root?.serverError && (
              <p className="text-sm text-red-600 text-center">{errors.root.serverError.message}</p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full bg-mulberry hover:bg-mulberryHover"
            disabled={isLoading || isRedirecting}
          >
            {isRedirecting ? (
              <div className="flex items-center justify-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Redirecting to Payment...
              </div>
            ) : isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Processing...
              </div>
            ) : (
              'Proceed to Checkout'
            )}
          </Button>

          {flowStatus === 'error' && error && !errors.root?.serverError && !errors.email && (
            <p className="text-sm text-red-600 text-center">{error}</p>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default BuyView;
