'use client';

import React, { useState, useEffect } from 'react';

import { Button } from '@/src/components/ui/button';

interface RetryClaimDetailsButtonProps {
  attemptId: string;
  retryAllowedAt: number;
  onRetry: (attemptId: string) => void;
  onCancel: (attemptId: string) => void;
  isProcessing: boolean;
}

const RetryClaimDetailsButton: React.FC<RetryClaimDetailsButtonProps> = ({
  attemptId,
  retryAllowedAt,
  onRetry,
  onCancel,
  isProcessing,
}) => {
  const [timeLeft, setTimeLeft] = useState<number>(Math.max(0, retryAllowedAt - Date.now()));
  const [canRetry, setCanRetry] = useState<boolean>(timeLeft <= 0);

  useEffect(() => {
    setCanRetry(timeLeft <= 0); // Initial check

    if (timeLeft > 0) {
      const intervalId = setInterval(() => {
        const remaining = Math.max(0, retryAllowedAt - Date.now());
        setTimeLeft(remaining);
        if (remaining <= 0) {
          setCanRetry(true);
          clearInterval(intervalId);
        }
      }, 1000);

      return () => clearInterval(intervalId); // Cleanup on unmount or prop change
    }
  }, [retryAllowedAt, timeLeft]); // Rerun effect if retryAllowedAt changes

  const secondsLeft = Math.ceil(timeLeft / 1000);

  return (
    <div className="flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto">
      <Button
        size="sm"
        className="w-full sm:w-auto"
        onClick={() => onRetry(attemptId)}
        disabled={!canRetry || isProcessing}
      >
        {isProcessing ? 'Loading...' : canRetry ? 'Retry Claim' : `Retry in ${secondsLeft}s`}
      </Button>
      {!canRetry && (
        <Button
          size="sm"
          variant="ghost"
          className="text-xs text-gray-500 hover:text-gray-700 w-full sm:w-auto"
          onClick={() => onCancel(attemptId)}
          disabled={isProcessing}
        >
          Cancel Wait
        </Button>
      )}
    </div>
  );
};

export default RetryClaimDetailsButton;
