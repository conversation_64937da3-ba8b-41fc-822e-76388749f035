'use client';

import { format } from 'date-fns';
import { CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';

import { getPacificTimeNow, convertToPacificTime } from '@/src/lib/timezone';
import { cn } from '@/src/lib/utils';

import { AnonymousAttemptDexieRecord } from '../client/dexieService';
import {
  verificationConfig,
  getStyleForVerifiedCertificate,
} from '../lib/config/verificationConfig';
import { SelectedItem } from '../types';
import { isStep3Payload, isStep2Payload } from '../types/anonymousTokens';

import ClaimCertificateItemImage from './ClaimCertificateItemImage';

interface ClaimCertificateProps {
  currentAttempt: AnonymousAttemptDexieRecord | null;
  onVerify: () => void;
  isVerifying: boolean;
  isVerified: boolean;
  flowError: string | null;
}

const ClaimCertificate: React.FC<ClaimCertificateProps> = ({
  currentAttempt,
  onVerify,
  isVerifying,
  isVerified,
  flowError,
}) => {
  const [verificationTapCount, setVerificationTapCount] = useState(0);
  const lastVerificationTapTimeRef = useRef<number>(0);

  const [timestampTapCount, setTimestampTapCount] = useState(0);
  const firstTimestampTapTimeRef = useRef<number>(0);
  const timestampTapTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Reset tap counts when the attempt changes or verification state changes significantly
    setVerificationTapCount(0);
    setTimestampTapCount(0);
    if (timestampTapTimeoutRef.current) {
      clearTimeout(timestampTapTimeoutRef.current);
    }
  }, [currentAttempt?.attemptId, isVerified, isVerifying]); // Listen to attemptId specifically

  const handleCertificateTap = () => {
    const now = Date.now();

    if (!isVerified && !isVerifying) {
      // Only allow verification taps if not already verified or verifying
      let currentTapCount = verificationTapCount;
      if (
        verificationTapCount > 0 &&
        now - lastVerificationTapTimeRef.current > verificationConfig.verificationTapWindowMs
      ) {
        currentTapCount = 1; // Reset if too much time passed
        setVerificationTapCount(1);
      } else {
        currentTapCount += 1;
        setVerificationTapCount(currentTapCount);
      }
      lastVerificationTapTimeRef.current = now;

      if (currentTapCount >= verificationConfig.tapsToInitiateVerification) {
        onVerify();
        setVerificationTapCount(0); // Reset after initiating verification
      }
    }

    // Timestamp display logic (easter egg)
    if (currentAttempt) {
      if (timestampTapTimeoutRef.current) clearTimeout(timestampTapTimeoutRef.current);
      let currentTSCount = timestampTapCount;
      if (
        currentTSCount > 0 &&
        (now - firstTimestampTapTimeRef.current) / 1000 >
          verificationConfig.tapsToDisplayTimestamp.windowSeconds
      ) {
        currentTSCount = 1;
        setTimestampTapCount(1);
        firstTimestampTapTimeRef.current = now;
      } else {
        currentTSCount += 1;
        setTimestampTapCount(currentTSCount);
        if (currentTSCount === 1) firstTimestampTapTimeRef.current = now;
      }

      if (currentTSCount >= verificationConfig.tapsToDisplayTimestamp.count) {
        if (
          (now - firstTimestampTapTimeRef.current) / 1000 <=
          verificationConfig.tapsToDisplayTimestamp.windowSeconds
        ) {
          const claimTimestamp = currentAttempt.latestPayload.updatedAt;
          const localClaimTime = convertToPacificTime(new Date(claimTimestamp));
          const gmtClaimTime = new Date(claimTimestamp);
          toast.info(
            `Claimed At:\nPT: ${format(localClaimTime, 'MMM d, yyyy h:mm:ss a zzz')}\nGMT: ${format(gmtClaimTime, "MMM d, yyyy HH:mm:ss 'GMT'")}`,
            { duration: 10000 }
          );
          setTimestampTapCount(0); // Reset after displaying
        } else {
          // Taps were too slow for timestamp display, reset to 1 for a new sequence
          setTimestampTapCount(1);
          firstTimestampTapTimeRef.current = now;
        }
      } else {
        // Set a timeout to reset the timestamp tap count if not enough taps are made within the window
        timestampTapTimeoutRef.current = setTimeout(
          () => {
            setTimestampTapCount(0);
          },
          verificationConfig.tapsToDisplayTimestamp.windowSeconds * 1000 + 500 // Add a little buffer
        );
      }
    }
  };

  let certificateContent;
  let containerClasses =
    'p-4 sm:p-6 rounded-lg shadow-xl text-center cursor-pointer transition-all duration-300 ease-in-out min-h-[250px] w-full flex flex-col items-center justify-center space-y-2'; // Reduced default space-y
  let iconClasses = 'w-10 h-10 sm:w-12 sm:h-12 mb-2'; // Added margin-bottom to icon

  const getDisplayItems = (): SelectedItem[] => {
    if (
      currentAttempt &&
      (isStep2Payload(currentAttempt.latestPayload) || isStep3Payload(currentAttempt.latestPayload))
    ) {
      return currentAttempt.latestPayload.selectedItems;
    }
    return [];
  };

  const displayItems = getDisplayItems();
  const orderTotal =
    currentAttempt &&
    (isStep2Payload(currentAttempt.latestPayload) || isStep3Payload(currentAttempt.latestPayload))
      ? currentAttempt.latestPayload.orderTotal
      : undefined;

  if (isVerifying) {
    containerClasses = cn(containerClasses, 'bg-gray-100 text-gray-700');
    iconClasses = cn(iconClasses, 'text-mulberry');
    certificateContent = (
      <>
        <div>
          {' '}
          {/* Wrapper for text elements */}
          <p className="text-lg sm:text-xl font-semibold">Verifying...</p>
          <p className="text-xs sm:text-sm text-gray-600">Please wait a moment.</p>{' '}
          {/* Reduced top margin */}
        </div>
      </>
    );
  } else if (isVerified && currentAttempt && isStep3Payload(currentAttempt.latestPayload)) {
    const payload = currentAttempt.latestPayload;
    const minutesElapsed = (Date.now() - payload.updatedAt) / (1000 * 60);
    const style = getStyleForVerifiedCertificate(minutesElapsed);
    containerClasses = cn(containerClasses, style.backgroundColor, style.textColor);
    if (style.borderColor) containerClasses = cn(containerClasses, `border-2 ${style.borderColor}`);
    iconClasses = cn(iconClasses, style.textColor || 'text-white');

    certificateContent = (
      <>
        <CheckCircle className={iconClasses} />
        <p className="text-lg sm:text-xl font-semibold">{style.label}</p>
        <div className="w-full max-w-xs mx-auto space-y-1.5 my-1 text-left text-xs sm:text-sm">
          {' '}
          {/* Reduced space and margin */}
          {displayItems.map(item => (
            <div
              key={item.variationId}
              className="flex items-center justify-between p-1.5 rounded bg-white/20" // Reduced padding
            >
              <div className="flex items-center space-x-2">
                <ClaimCertificateItemImage src={item.imageUrl} alt={item.name} size={36} />{' '}
                {/* Slightly smaller image */}
                <span>
                  {item.quantity} x {item.name}
                </span>
              </div>
            </div>
          ))}
        </div>
        {orderTotal !== undefined && (
          <p className="text-sm sm:text-md font-semibold">
            Total Paid: ${(orderTotal / 100).toFixed(2)}
          </p>
        )}
        <p className="text-xs sm:text-sm mt-1">
          {' '}
          {/* Added small margin */}
          Verified on: {format(getPacificTimeNow(), 'MMM d, h:mm a')}
        </p>
      </>
    );
  } else if (flowError) {
    // This handles errors when trying to verify or if already in error state for Step 3
    containerClasses = cn(containerClasses, 'bg-red-100 border-red-300 text-red-700');
    iconClasses = cn(iconClasses, 'text-red-500');
    certificateContent = (
      <>
        <AlertTriangle className={iconClasses} />
        <p className="text-lg sm:text-xl font-semibold">Verification Issue</p>
        <p className="text-xs sm:text-sm px-2">{flowError}</p>{' '}
        {/* Added padding for longer messages */}
        {!isVerifying && ( // Allow retry only if not currently in the process of verifying
          <button
            onClick={e => {
              e.stopPropagation(); // Prevent triggering handleCertificateTap if this button is part of it
              onVerify(); // Re-trigger the verification process
            }}
            className="mt-2 px-3 py-1.5 text-xs bg-mulberry text-white rounded hover:bg-mulberryHover flex items-center"
          >
            <RefreshCw size={14} className="mr-1.5" /> Try Verification Again
          </button>
        )}
      </>
    );
  } else {
    // Initial "Claimed" state (flowStatus === 'claimed')
    containerClasses = cn(containerClasses, 'bg-green-100 border-green-300 text-green-700');
    iconClasses = cn(iconClasses, 'text-green-600');
    certificateContent = (
      <>
        <CheckCircle className={iconClasses} />
        <p className="text-lg sm:text-xl font-semibold">Items Claimed</p>
        <div className="w-full max-w-xs mx-auto space-y-1.5 my-1 text-left text-xs sm:text-sm">
          {displayItems.map(item => (
            <div
              key={item.variationId}
              className="flex items-center justify-between p-1.5 rounded bg-black/5"
            >
              <div className="flex items-center space-x-2">
                <ClaimCertificateItemImage src={item.imageUrl} alt={item.name} size={36} />
                <span>
                  {item.quantity} x {item.name}
                </span>
              </div>
            </div>
          ))}
        </div>
        {orderTotal !== undefined && (
          <p className="text-sm sm:text-md font-semibold">
            Total Paid: ${(orderTotal / 100).toFixed(2)}
          </p>
        )}
        <p className="text-xs sm:text-sm mt-1">Present this to an attendant for verification.</p>
      </>
    );
  }

  return (
    <div
      className={containerClasses}
      onClick={handleCertificateTap}
      role="button"
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === ' ' || e.key === 'Enter') handleCertificateTap();
      }}
    >
      {certificateContent}
    </div>
  );
};

export default ClaimCertificate;
