'use client';

import { formatDistanceToNowStrict } from 'date-fns';
import { useRouter } from 'next/navigation';
import React, { useState, useMemo } from 'react';

import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { logClient, LogLevel } from '@/src/lib/logging';
import { useAppDispatch, useAppSelector } from '@/src/store';

import { retryClaimDetails } from '../client/apiService';
import {
  selectClaimListAttempts,
  selectAnonymousFlowStatus,
  selectClaimDetailsRetryInfo,
  clearClaimDetailsRetry,
  selectClaimAttemptToken,
} from '../slices/anonymousFlowSlice';
import { isStep1Payload, isStep2Payload, isStep3Payload } from '../types/anonymousTokens';

import ClaimCertificateItemImage from './ClaimCertificateItemImage';
import RetryClaimDetailsButton from './RetryClaimDetailsButton';

const ClaimListView: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const allAttemptsFromState = useAppSelector(selectClaimListAttempts);
  const status = useAppSelector(selectAnonymousFlowStatus);
  const retryInfo = useAppSelector(selectClaimDetailsRetryInfo);
  const claimAttemptToken = useAppSelector(selectClaimAttemptToken);
  const isLoadingList = status === 'loadingClaimList';
  const [loadingAttemptId, setLoadingAttemptId] = useState<string | null>(null);

  const displayableAttempts = useMemo(() => {
    // Sort by creation time, most recent first
    return [...allAttemptsFromState].sort(
      (a, b) => b.latestPayload.createdAt - a.latestPayload.createdAt
    );
  }, [allAttemptsFromState]);

  const handleRetryOrView = async (attemptId: string) => {
    setLoadingAttemptId(attemptId);
    logClient(LogLevel.INFO, 'ClaimListView', 'Attempting retry/view for attempt', {
      attemptId,
    });
    try {
      const resultAction = await dispatch(retryClaimDetails(attemptId));
      if (retryClaimDetails.fulfilled.match(resultAction) && resultAction.payload) {
        // Only redirect if fulfilled AND payload exists (meaning it's not a 409 retry scenario from within retryClaimDetails)
        router.push(`/claim?attemptId=${attemptId}`);
      }
      // If rejected or sets retry state (payload is null for 409 from retryClaimDetails), UI will update via Redux.
    } catch (error) {
      logClient(LogLevel.ERROR, 'ClaimListView', 'Retry/view failed for attempt', {
        attemptId,
        error,
      });
    } finally {
      setLoadingAttemptId(null);
    }
  };

  const handleCancelRetry = (attemptId: string) => {
    if (retryInfo?.attemptId === attemptId) {
      dispatch(clearClaimDetailsRetry());
    }
  };

  if (isLoadingList && displayableAttempts.length === 0) {
    // Only show main loader if list is empty
    return (
      <Card>
        <CardHeader>
          <CardTitle>Past Purchases</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">Loading purchase history...</p>
        </CardContent>
      </Card>
    );
  }

  if (claimAttemptToken && !isLoadingList && displayableAttempts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Past Purchases</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">No past purchases found for this device.</p>
        </CardContent>
      </Card>
    );
  }

  if (!claimAttemptToken && !isLoadingList) {
    // This state should be brief as AnonymousFlowController initializes the token.
    return (
      <Card>
        <CardHeader>
          <CardTitle>Past Purchases</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">Initializing session to check purchases...</p>
        </CardContent>
      </Card>
    );
  }

  if (displayableAttempts.length > 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Past Purchases</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingList && (
            <p className="text-sm text-gray-500 mb-2 text-center">Updating list...</p>
          )}
          <ul className="space-y-3">
            {displayableAttempts.map(attempt => {
              const payload = attempt.latestPayload;
              const isStep1 = isStep1Payload(payload);
              const isStep2 = isStep2Payload(payload);
              const isStep3 = isStep3Payload(payload);

              const isRetryingThisAttempt = retryInfo?.attemptId === attempt.attemptId;
              const isProcessingThisAttempt = loadingAttemptId === attempt.attemptId;

              let itemClasses =
                'p-3 border rounded-md bg-white flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0';
              if (isStep1 && !isStep2 && !isStep3)
                itemClasses += ' border-l-4 border-orange-400'; // More specific
              else if (isStep2 && !isStep3) itemClasses += ' border-l-4 border-blue-500';
              else if (isStep3) itemClasses += ' border-l-4 border-green-500';

              let relativeTime = 'some time ago';
              try {
                relativeTime = formatDistanceToNowStrict(new Date(payload.createdAt), {
                  addSuffix: true,
                });
              } catch (e: any) {
                console.warn('Error parsing date for ClaimListView item', e);
              }

              return (
                <li key={attempt.attemptId} className={itemClasses}>
                  <div className="flex-grow space-y-1">
                    <p className="text-xs font-medium text-gray-500">{relativeTime}</p>
                    <p className="text-xs text-gray-400 truncate">Email: {payload.email}</p>
                    {payload.selectedItems.map(item => (
                      <div
                        key={item.variationId}
                        className="flex items-center space-x-2 text-sm text-gray-800"
                      >
                        <ClaimCertificateItemImage src={item.imageUrl} alt={item.name} size={32} />
                        <span>
                          {item.quantity} x {item.name}
                        </span>
                      </div>
                    ))}
                    {(isStep2 || isStep3) && payload.orderTotal !== undefined && (
                      <p className="text-sm font-semibold">
                        Total: ${(payload.orderTotal / 100).toFixed(2)}
                      </p>
                    )}
                    {isStep1 &&
                      !isStep2 &&
                      !isStep3 && ( // More specific condition
                        <p className="text-sm text-gray-500 italic">Total: Pending confirmation</p>
                      )}
                    <p className="text-sm font-semibold capitalize mt-1">
                      Status:{' '}
                      <span
                        className={
                          isStep3
                            ? 'text-green-600'
                            : isStep2
                              ? 'text-blue-500'
                              : isStep1
                                ? 'text-orange-500'
                                : 'text-gray-600'
                        }
                      >
                        {isStep1 && !isStep2 && !isStep3
                          ? 'Payment Not Completed'
                          : payload.status.replace(/_/g, ' ')}
                      </span>
                    </p>
                  </div>
                  <div className="flex-shrink-0 w-full sm:w-auto pt-2 sm:pt-0">
                    {isStep1 && !isStep2 && !isStep3 && !isRetryingThisAttempt && (
                      <Button
                        size="sm"
                        className="w-full sm:w-auto bg-orange-500 hover:bg-orange-600 text-white"
                        onClick={() => router.push(`/claim?attemptId=${attempt.attemptId}`)}
                        disabled={isProcessingThisAttempt}
                      >
                        {isProcessingThisAttempt ? 'Loading...' : 'Check Status'}
                      </Button>
                    )}
                    {isStep2 && !isStep3 && !isRetryingThisAttempt && (
                      <Button
                        size="sm"
                        className="w-full sm:w-auto bg-blue-500 hover:bg-blue-600 text-white"
                        onClick={() => handleRetryOrView(attempt.attemptId)}
                        disabled={isProcessingThisAttempt}
                      >
                        {isProcessingThisAttempt ? 'Loading...' : 'Claim Now'}
                      </Button>
                    )}
                    {isStep3 && (
                      <Button
                        size="sm"
                        className="w-full sm:w-auto"
                        variant="outline"
                        onClick={() => router.push(`/claim?attemptId=${attempt.attemptId}`)}
                        disabled={isProcessingThisAttempt}
                      >
                        View Claim
                      </Button>
                    )}
                    {isRetryingThisAttempt && retryInfo?.retryAllowedAt && (
                      <RetryClaimDetailsButton
                        attemptId={attempt.attemptId}
                        retryAllowedAt={retryInfo.retryAllowedAt}
                        onRetry={() => {
                          handleRetryOrView(attempt.attemptId); // Use consistent handler
                        }}
                        onCancel={() => handleCancelRetry(attempt.attemptId)}
                        isProcessing={isProcessingThisAttempt}
                      />
                    )}
                  </div>
                </li>
              );
            })}
          </ul>
        </CardContent>
      </Card>
    );
  }

  // Fallback if claimAttemptToken exists, list is empty, and not loading (should be rare if controller logic is sound)
  if (claimAttemptToken && displayableAttempts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Past Purchases</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">No past purchases found for this device.</p>
        </CardContent>
      </Card>
    );
  }

  // Default initial state before anything significant loads
  return (
    <Card>
      <CardHeader>
        <CardTitle>Past Purchases</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Checking for past purchases...</p>
      </CardContent>
    </Card>
  );
};

export default ClaimListView;
