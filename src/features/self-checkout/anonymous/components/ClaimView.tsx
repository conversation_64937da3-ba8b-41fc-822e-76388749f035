'use client';

import React, { useEffect, useState } from 'react'; // Added useState
import { toast } from 'sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { logClient, LogLevel } from '@/src/lib/logging';
import { useAppDispatch, useAppSelector } from '@/src/store';

import { claimTokens, verifyClaimTap } from '../client/apiService';
import {
  selectAnonymousFlowStatus,
  selectAnonymousFlowError,
  selectCurrentAttempt,
} from '../slices/anonymousFlowSlice';
import { isStep1Payload, isStep2Payload, isStep3Payload } from '../types/anonymousTokens';

import ClaimButton from './ClaimButton';
import ClaimCertificate from './ClaimCertificate';
import ClaimCertificateItemImage from './ClaimCertificateItemImage';

interface ClaimViewProps {
  attemptId: string;
}

const ClaimView: React.FC<ClaimViewProps> = ({ attemptId }) => {
  const dispatch = useAppDispatch();
  const flowStatus = useAppSelector(selectAnonymousFlowStatus);
  const error = useAppSelector(selectAnonymousFlowError);
  const currentAttempt = useAppSelector(selectCurrentAttempt);

  const [isClaimingLocally, setIsClaimingLocally] = useState(false); // Added local state

  const isLoadingInitialDetails = flowStatus === 'loadingClaimDetails';
  // isProcessingClaim is still useful for UI text, but button disablement also considers isClaimingLocally
  const isProcessingClaim = flowStatus === 'claiming' || isClaimingLocally;
  const isVerifying = flowStatus === 'verifying';
  const isVerified = flowStatus === 'verified';
  const isClaimedState = flowStatus === 'claimed';

  useEffect(() => {
    if (currentAttempt?.attemptId !== attemptId && flowStatus !== 'loadingClaimDetails') {
      logClient(
        LogLevel.WARN,
        'ClaimView.useEffect',
        `Attempt ID mismatch or not loaded. Prop: ${attemptId}, State: ${currentAttempt?.attemptId}`
      );
    }
  }, [attemptId, currentAttempt, flowStatus, dispatch]);

  const handleClaim = () => {
    if (isClaimingLocally) {
      // Prevent multiple dispatches
      logClient(LogLevel.DEBUG, 'ClaimView.handleClaim', 'Already claiming locally, skipping.');
      return;
    }
    if (currentAttempt && isStep2Payload(currentAttempt.latestPayload)) {
      setIsClaimingLocally(true); // Set local flag immediately
      dispatch(claimTokens(currentAttempt.attemptId)).finally(() => {
        setIsClaimingLocally(false); // Reset local flag
      });
    } else {
      logClient(LogLevel.WARN, 'ClaimView.handleClaim', 'Attempted claim in invalid state', {
        status: flowStatus,
        payload: currentAttempt?.latestPayload,
      });
    }
  };

  const handleVerify = () => {
    if (currentAttempt && isStep3Payload(currentAttempt.latestPayload)) {
      dispatch(verifyClaimTap(currentAttempt.attemptId));
    } else {
      logClient(
        LogLevel.WARN,
        'ClaimView.handleVerify',
        'Attempted verify but current attempt is not in Step 3 or is missing.',
        {
          status: flowStatus,
          payloadStep: currentAttempt?.latestPayload?.step,
          currentAttemptExists: !!currentAttempt,
        }
      );
      toast.error('Cannot verify at this moment. Claim data is inconsistent or not ready.');
    }
  };

  const renderContent = () => {
    if (isLoadingInitialDetails) {
      return (
        <div className="flex flex-col items-center justify-center space-y-2 py-10">
          <p>Loading your purchase details...</p>
        </div>
      );
    }

    // Display for "claiming" state, driven by either local or global state
    if (isProcessingClaim && !isClaimedState && !isVerified && !isVerifying) {
      return (
        <div className="flex flex-col items-center justify-center space-y-2 py-10">
          <p>Claiming your items... Please wait.</p>
        </div>
      );
    }

    if (flowStatus === 'error' && !currentAttempt && !isLoadingInitialDetails) {
      return (
        <p className="text-center text-red-600 py-10">{error || 'An unexpected error occurred.'}</p>
      );
    }

    if (currentAttempt) {
      const payload = currentAttempt.latestPayload;

      if (flowStatus === 'readyToClaim' && isStep2Payload(payload) && !isClaimingLocally) {
        return (
          <div className="text-center space-y-4 w-full max-w-md mx-auto">
            <p className="text-lg font-semibold">Your purchase is ready to claim:</p>
            <div className="space-y-2 my-3 border p-3 rounded-md bg-gray-50 max-w-sm mx-auto">
              {payload.selectedItems.map(item => (
                <div key={item.variationId} className="flex items-center justify-between text-left">
                  <div className="flex items-center space-x-2">
                    <ClaimCertificateItemImage src={item.imageUrl} alt={item.name} size={40} />
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                    </div>
                  </div>
                </div>
              ))}
              {payload.orderTotal !== undefined && (
                <p className="text-md font-bold text-right pt-2 border-t mt-2">
                  Total: ${(payload.orderTotal / 100).toFixed(2)}
                </p>
              )}
            </div>
            <p className="text-sm text-gray-700">Tap below to claim your items.</p>
            <ClaimButton onClick={handleClaim} state="ready_to_claim" />
          </div>
        );
      }

      if (
        isClaimedState ||
        isVerifying ||
        isVerified ||
        (flowStatus === 'error' && isStep3Payload(payload))
      ) {
        const certificateError = flowStatus === 'error' && isStep3Payload(payload) ? error : null;
        if (isStep3Payload(payload) || certificateError) {
          return (
            <ClaimCertificate
              currentAttempt={currentAttempt}
              onVerify={handleVerify}
              isVerifying={isVerifying}
              isVerified={isVerified}
              flowError={certificateError}
            />
          );
        } else if (isClaimedState && !isStep3Payload(payload)) {
          logClient(
            LogLevel.ERROR,
            'ClaimView.renderContent',
            'Inconsistent state: flowStatus is "claimed" but payload is not Step 3.',
            { payload }
          );
          return (
            <p className="text-center text-red-600 py-10">
              Claim process incomplete. Please try refreshing.
            </p>
          );
        }
      }

      if (flowStatus === 'error' && (isStep1Payload(payload) || isStep2Payload(payload))) {
        // If it's an error but we are locally claiming, show the claiming UI instead of error
        if (isClaimingLocally && isStep2Payload(payload)) {
          return (
            <div className="flex flex-col items-center justify-center space-y-2 py-10">
              <p>Claiming your items... Please wait.</p>
            </div>
          );
        }
        return (
          <p className="text-center text-red-600 py-10">
            {error || 'An error occurred before claim completion.'}
          </p>
        );
      }
    }

    logClient(LogLevel.WARN, 'ClaimView.renderContent', 'Reached fallback rendering state.', {
      flowStatus,
      currentAttemptExists: !!currentAttempt,
      payloadStep: currentAttempt?.latestPayload?.step,
    });
    return (
      <div className="text-center text-gray-500 py-10">
        Loading status... (State: {flowStatus}, Step: {currentAttempt?.latestPayload?.step || 'N/A'}
        )
      </div>
    );
  };

  useEffect(() => {
    if (flowStatus === 'error' && error && !isClaimingLocally) {
      // Do not show toast if we are locally processing a claim that might result in an error
      const isVerificationContextError =
        currentAttempt && isStep3Payload(currentAttempt.latestPayload);
      const isPageLevelError = error.match(
        /Order payment is not complete|Token minting is not yet complete|Order processing is not complete|Purchase attempt .* not found locally/i
      );

      if (!isVerificationContextError && !isPageLevelError) {
        toast.error(error);
      }
    }
  }, [error, flowStatus, currentAttempt, isClaimingLocally]);

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle className="text-center text-2xl font-bold text-mulberry">Claim Status</CardTitle>
      </CardHeader>
      <CardContent className="min-h-[300px] sm:min-h-[350px] flex items-center justify-center p-2 sm:p-4 md:p-6">
        {renderContent()}
      </CardContent>
    </Card>
  );
};

export default ClaimView;
