'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { CustomPhoneInput } from '@/src/components/phone-input';
import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/src/components/ui/form';
import { Input } from '@/src/components/ui/input';
import { Textarea } from '@/src/components/ui/textarea';
import { ROUTES, VALIDATION_ERROR_MESSAGES } from '@/src/lib/constants';
import { useAppDispatch, useAppSelector } from '@/src/store';

import SubscriptionStepper from '../components/SubscriptionStepper';
import SubscriptionTitle from '../components/SubscriptionTitle';
import { updateMissingAddressLog } from '../slices/addressValidationSlice';
import {
  setRecipientDetails,
  getOrCreateRecipientCustomer, // New action for recipient
  enableRedirect,
  selectRecipientSquareCustomerId, // New selector
  selectRecipientCustomerLoading, // New selector
  selectRecipientCustomerError, // New selector
  selectShouldRedirect,
  selectRecipientDetails,
  setRecipientCustomerLoading, // New selector
} from '../slices/subscriptionCustomerSlice';
import { selectSubscriptionDetails } from '../slices/subscriptionDetailsSlice';

// Define form schema with zod
const giftDetailsSchema = z
  .object({
    firstName: z.string().min(2, VALIDATION_ERROR_MESSAGES.FIRST_NAME_MIN),
    lastName: z.string().min(2, VALIDATION_ERROR_MESSAGES.LAST_NAME_MIN),
    email: z.string().email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID),
    confirmEmail: z.string().email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID),
    phone: z.string().min(1, VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER),
    address: z.string().min(5, VALIDATION_ERROR_MESSAGES.ADDRESS_IS_REQUIRED),
    addressLine2: z.string().optional(),
    giftMessage: z.string().max(255, 'Gift message cannot exceed 255 characters').optional(),
    deliveryNotes: z.string().optional(),
  })
  .refine(data => !data.email || data.email === data.confirmEmail, {
    message: 'Email addresses must match',
    path: ['confirmEmail'],
  });

type GiftDetailsFormValues = z.infer<typeof giftDetailsSchema>;

function GiftDetails() {
  /**
   * Start Initials
   */
  const router = useRouter();
  const dispatch = useAppDispatch();
  const subscriptionDetails = useAppSelector(selectSubscriptionDetails);
  const recipientDetails = useAppSelector(selectRecipientDetails);
  const recipientSquareCustomerId = useAppSelector(selectRecipientSquareCustomerId);
  const customerLoading = useAppSelector(selectRecipientCustomerLoading);
  const customerError = useAppSelector(selectRecipientCustomerError);
  const shouldRedirect = useAppSelector(selectShouldRedirect);

  // Calculate current step based on whether deliveryDays exist
  const currentStep = subscriptionDetails.deliveryDays?.length ? 5 : 4;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<GiftDetailsFormValues>({
    resolver: zodResolver(giftDetailsSchema),
    defaultValues: {
      firstName: recipientDetails?.firstName || '',
      lastName: recipientDetails?.lastName || '',
      email: recipientDetails?.email || '',
      confirmEmail: recipientDetails?.email || '',
      phone: recipientDetails?.phone || '',
      // For address fields, still use subscription details
      address: subscriptionDetails?.address,
      addressLine2: recipientDetails?.addressLine2 || '',
      giftMessage: recipientDetails?.giftMessage || '',
      deliveryNotes: recipientDetails?.deliveryNotes || '',
    },
  });
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Check if product details are missing and redirect if needed
  useEffect(() => {
    if (!subscriptionDetails.variationId) {
      router.push(ROUTES.SUBSCRIPTION_PRODUCT);
    } else if (!subscriptionDetails.zoneId) {
      router.push(ROUTES.SUBSCRIPTION);
    }
  }, [subscriptionDetails, router]);

  // Add this effect to handle redirect after customer creation
  useEffect(() => {
    // Only redirect if we have a recipient customer ID AND shouldRedirect is true
    if (recipientSquareCustomerId && shouldRedirect) {
      // Navigate to payment page
      router.push(ROUTES.SUBSCRIPTION_PAYMENT);
      dispatch(setRecipientCustomerLoading(false));
    }
  }, [recipientSquareCustomerId, shouldRedirect]);

  // Handle customer error
  useEffect(() => {
    if (customerError) {
      toast.error(`Customer error: ${customerError}`);
      dispatch(setRecipientCustomerLoading(false));
    }
  }, [customerError]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const onSubmit = (data: GiftDetailsFormValues) => {
    const recipientData = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email || '',
      phone: data.phone || '',
      addressLine1: subscriptionDetails?.addressLine1 || '',
      addressLine2: data.addressLine2 || '',
      city: subscriptionDetails?.city || '',
      state: subscriptionDetails?.state || '',
      zipCode: subscriptionDetails?.zipCode || '',
      deliveryNotes: data.deliveryNotes || '',
      giftMessage: data.giftMessage || '',
    };

    dispatch(
      updateMissingAddressLog({
        data: {
          recipient: recipientData,
        },
      })
    );
    // Store recipient details in customerDetails
    // Only update the recipient field, preserving other required fields
    dispatch(setRecipientDetails(recipientData));

    // Re-enable redirect before creating/getting customer
    dispatch(enableRedirect());

    dispatch(
      getOrCreateRecipientCustomer({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        phone: data.phone || '',
        addressLine1: subscriptionDetails?.addressLine1 || '',
        addressLine2: data.addressLine2 || '',
        city: subscriptionDetails?.city || '',
        state: subscriptionDetails?.state || '',
        zipCode: subscriptionDetails?.zipCode || '',
      })
    );
    dispatch(setRecipientCustomerLoading(true));
  };
  /**
   * End Custom Methods
   */

  if (customerLoading) {
    return <Spinner />;
  }

  return (
    <>
      <SubscriptionTitle />

      {/* Move stepper to top of page */}
      <div className="max-w-5xl mx-auto mt-8">
        <SubscriptionStepper currentStep={currentStep} />
      </div>

      <div className="max-w-3xl mx-auto md:mb-6 mb-16">
        <Card>
          <CardContent className="p-2 md:p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipient First Name *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipient Last Name *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipient Email *</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Recipient Email *</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipient Phone Number *</FormLabel>
                      <FormControl>
                        <CustomPhoneInput
                          value={field.value || ''}
                          onChange={value => field.onChange(value)}
                          className="w-full"
                          country="us"
                          disabled={false}
                          disableDropdown={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipient Address *</FormLabel>
                      <FormControl>
                        <Input {...field} className="bg-gray-100" disabled />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="addressLine2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipient Apartment/Suite</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Apt, Suite, Floor, etc." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="giftMessage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gift Message To Recipient (optional)</FormLabel>
                      <FormControl>
                        <Textarea {...field} className="min-h-[50px]" maxLength={255} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deliveryNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Recipient Delivery Notes (optional — include anything the driver should
                        know: gate codes, access tips, etc.)
                      </FormLabel>
                      <FormControl>
                        <Textarea {...field} className="min-h-[50px]" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full bg-mulberry text-white hover:bg-mulberryHover"
                >
                  Continue
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </>
  );
}

export default GiftDetails;
