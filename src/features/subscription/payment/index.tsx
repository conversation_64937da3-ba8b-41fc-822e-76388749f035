'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { CreditCard, PaymentForm } from 'react-square-web-payments-sdk';
import { toast } from 'sonner';
import { z } from 'zod';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';
import { Input } from '@/src/components/ui/input';
import { Separator } from '@/src/components/ui/separator';
import { BUTTON_TEXTS, ROUTES, SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';
import { base64EncodeJson, isGiftHomeDelivery, isOneTimeHomeDelivery } from '@/src/lib/utils';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';
import { Users } from '@/src/types/Users';

import SubscriptionStepper from '../components/SubscriptionStepper';
import SubscriptionTitle from '../components/SubscriptionTitle';
import SubscriptionTypeSelector from '../components/SubscriptionTypeSelector';
import {
  validateDiscountCode,
  clearDiscount,
  selectAppliedDiscountInfo,
  selectDiscountLoading,
  selectDiscountError as selectDiscountValidationError,
  selectUrlDiscountCode,
  referralCode,
  appliedReferralDiscount,
  validateReferralCode,
  setUrlDiscountCode,
  clearReferralDiscount,
  setReferralCode,
} from '../slices/discountValidationSlice';
import {
  selectCustomerDetails,
  selectSquareCustomerId,
  selectRecipientDetails,
  selectRecipientSquareCustomerId,
  CustomerDetailsInterface,
} from '../slices/subscriptionCustomerSlice';
import {
  selectSubscriptionDetails,
  SubscriptionDetailsInterface,
} from '../slices/subscriptionDetailsSlice';
import {
  createSubscription,
  selectSubscriptionLoading,
  selectSubscriptionError,
  selectSubscriptionId,
  setLoading,
  selectOrderId,
} from '../slices/subscriptionPaymentSlice';
import { selectSubscriptionProduct } from '../slices/subscriptionProductSlice';

function SubscriptionPayment() {
  /**
   * Start Initials
   */
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useAppDispatch();

  const [paymentStatus, setPaymentStatus] = useState('idle');
  const [discountCodeInput, setDiscountCodeInput] = useState('');
  const [referralCodeInput, setReferralCodeInput] = useState('');
  const [isApplyingReferral, setIsApplyingReferral] = useState(false);
  const [isApplyingDiscount, setIsApplyingDiscount] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [referralValidationError, setReferralValidationError] = useState<string | null>(null);
  const appliedDiscountInfo = useAppSelector(selectAppliedDiscountInfo);
  const appliedReferralDiscountInfo = useAppSelector(appliedReferralDiscount);
  const isDiscountLoading = useAppSelector(selectDiscountLoading);
  const discountValidationError = useAppSelector(selectDiscountValidationError);

  const { user } = useAppSelector((state: RootState) => state.authentication);
  const urlDiscountCode = useAppSelector(selectUrlDiscountCode);
  const customerDetails = useAppSelector(selectCustomerDetails);
  const squareCustomerId = useAppSelector(selectSquareCustomerId);
  const recipientDetails = useAppSelector(selectRecipientDetails);
  const recipientSquareCustomerId = useAppSelector(selectRecipientSquareCustomerId);
  const subscriptionDetails = useAppSelector(selectSubscriptionDetails);
  const subscriptionProduct = useAppSelector(selectSubscriptionProduct);

  const subscriptionId = useAppSelector(selectSubscriptionId);
  const orderId = useAppSelector(selectOrderId);
  const isLoading = useAppSelector(selectSubscriptionLoading);
  const error = useAppSelector(selectSubscriptionError);
  const urlReferralCode = useAppSelector(referralCode);

  const paymentSchema = z.object({
    termsAccepted: z.boolean().refine(val => val === true, {
      message: 'You must accept the terms to continue',
    }),
  });

  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    control,
    watch,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    formState: { errors },
  } = useForm({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      termsAccepted: true,
    },
  });

  const termsAccepted = watch('termsAccepted');

  const isOneTimeOrder = isOneTimeHomeDelivery(subscriptionDetails.orderFor);

  const isGiftOrder = isGiftHomeDelivery(subscriptionDetails.orderFor);

  const currentStep = isGiftOrder
    ? subscriptionDetails.deliveryDays?.length
      ? 6
      : 5
    : subscriptionDetails.deliveryDays?.length
      ? 5
      : 4;
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */

  useEffect(() => {
    const isPaymentPage = pathname === ROUTES.SUBSCRIPTION_PAYMENT;
    if (!isPaymentPage) return;

    if (
      urlDiscountCode &&
      subscriptionDetails.variationId &&
      !appliedDiscountInfo &&
      !isDiscountLoading &&
      !appliedReferralDiscountInfo
    ) {
      dispatch(
        validateDiscountCode({
          discountCode: urlDiscountCode,
          productVariationId: subscriptionDetails.variationId,
        })
      )
        .unwrap()
        .then(result => {
          if (result.isValid) toast.success('Discount applied automatically!');
          else toast.error(result.message || 'Invalid or expired discount code from link.');
        })
        .catch(err => toast.error(err || 'Failed to automatically validate discount code.'));
    }

    if (
      urlReferralCode &&
      subscriptionDetails.variationId &&
      !appliedReferralDiscountInfo &&
      !isDiscountLoading &&
      !appliedDiscountInfo
    ) {
      setIsApplyingReferral(true);
      dispatch(validateReferralCode())
        .unwrap()
        .then(result => {
          if (result.valid) {
            toast.success('Referral discount applied automatically!');
            setReferralValidationError(null);
            setValidationError(null);
          } else {
            const errorMessage = result.error || 'Invalid or expired referral code from link.';
            setReferralValidationError(errorMessage);
            setValidationError(null);
            toast.error(errorMessage);
          }
        })
        .catch(err => {
          const errorMessage = err || 'Failed to automatically validate referral code.';
          setReferralValidationError(errorMessage);
          setValidationError(null);
          toast.error(errorMessage);
        })
        .finally(() => {
          setIsApplyingReferral(false);
        });
    }
  }, [
    dispatch,
    urlDiscountCode,
    urlReferralCode,
    subscriptionDetails.variationId,
    appliedDiscountInfo,
    appliedReferralDiscountInfo,
    isDiscountLoading,
    pathname,
  ]);

  useEffect(() => {
    if (appliedDiscountInfo) {
      setDiscountCodeInput(appliedDiscountInfo.discountCodeName || urlDiscountCode || '');
      setValidationError(null);
    }
  }, [appliedDiscountInfo, urlDiscountCode]);

  useEffect(() => {
    if (appliedReferralDiscountInfo) {
      setReferralCodeInput(urlReferralCode || '');
      setReferralValidationError(null);
    }
  }, [appliedReferralDiscountInfo, urlReferralCode]);

  useEffect(() => {
    if (error) {
      toast.error(error);
      setPaymentStatus('error');
      dispatch(setLoading(false));
    }
  }, [error]);

  useEffect(() => {
    if (subscriptionId || orderId) {
      // Create token payload for gift subscription
      const tokenData = {
        orderFor: subscriptionDetails.orderFor,
      };

      // Use utility function instead of manual encoding
      const token = base64EncodeJson(tokenData);

      router.push(`${ROUTES.SUBSCRIPTION_CONFIRMATION}?t=${token}`);
      dispatch(setLoading(false));
    }
  }, [subscriptionId, subscriptionDetails, orderId]);

  // Check if customer details exist, if not redirect to user info page
  useEffect(() => {
    const redirectTo = () => {
      if (!customerDetails || !squareCustomerId) {
        return ROUTES.SUBSCRIPTION_USER_INFO;
      }

      if ((!recipientDetails || !recipientSquareCustomerId) && isGiftOrder) {
        return ROUTES.SUBSCRIPTION_GIFT_DETAILS;
      }

      if (!subscriptionDetails.variationId) {
        return ROUTES.SUBSCRIPTION_PRODUCT;
      }

      return null;
    };

    const route = redirectTo();
    if (route) {
      router.push(route);
    }
  }, [
    customerDetails,
    squareCustomerId,
    recipientDetails,
    recipientSquareCustomerId,
    subscriptionDetails,
  ]);

  // Clear applied discount if product details change (e.g., user goes back)
  useEffect(() => {
    dispatch(clearDiscount());
    setDiscountCodeInput('');
    setReferralCodeInput('');
  }, [subscriptionDetails.variationId]);

  // Update validation error when discount validation error changes
  useEffect(() => {
    if (discountValidationError) {
      setValidationError(discountValidationError);
      setReferralValidationError(null);
    }
  }, [discountValidationError]);

  // Clear discount error when referral error occurs
  useEffect(() => {
    if (referralValidationError) {
      setValidationError(null);
    }
  }, [referralValidationError]);

  // --- Referral Code Validation Handler ---
  const handleApplyReferralCode = async () => {
    if (!referralCodeInput.trim()) {
      setReferralValidationError('Please enter a referral code.');
      setValidationError(null);
      return;
    }
    if (!subscriptionDetails.variationId) {
      setReferralValidationError('Product selection is missing.');
      setValidationError(null);
      return;
    }
    if (appliedDiscountInfo) {
      setReferralValidationError(
        'Please remove the discount code before applying a referral code.'
      );
      setValidationError(null);
      return;
    }
    if (!user?.email) {
      setReferralValidationError('Please sign in to use a referral code.');
      setValidationError(null);
      return;
    }
    setReferralValidationError(null);
    setIsApplyingReferral(true);
    dispatch(setReferralCode(referralCodeInput.trim()));
    dispatch(validateReferralCode())
      .unwrap()
      .then(result => {
        if (result.valid) {
          toast.success('Referral discount applied!');
          setReferralCodeInput(referralCodeInput.trim());
        } else {
          setReferralValidationError(result.error || 'Invalid or expired referral code.');
          dispatch(setReferralCode(null));
        }
      })
      .catch(err => {
        setReferralValidationError(err || 'Failed to validate referral code.');
        dispatch(setReferralCode(null));
      })
      .finally(() => {
        setIsApplyingReferral(false);
      });
  };

  const handleRemoveReferralCode = () => {
    setReferralCodeInput('');
    setReferralValidationError(null);
    dispatch(clearDiscount());
    dispatch(clearReferralDiscount());
    toast.info('Referral discount removed.');
  };

  // --- Discount Validation Handler ---
  const handleApplyDiscount = async () => {
    if (!discountCodeInput.trim()) {
      setValidationError('Please enter a discount code.');
      setReferralValidationError(null);
      return;
    }
    if (!subscriptionDetails.variationId) {
      setValidationError('Product selection is missing.');
      setReferralValidationError(null);
      return;
    }
    if (referralCodeInput) {
      setValidationError('Please remove the referral code before applying a discount code.');
      setReferralValidationError(null);
      return;
    }
    setValidationError(null);
    setIsApplyingDiscount(true);
    dispatch(
      validateDiscountCode({
        discountCode: discountCodeInput.trim(),
        productVariationId: subscriptionDetails.variationId,
      })
    )
      .unwrap()
      .then(result => {
        if (result.isValid) {
          toast.success('Discount applied!');
          setDiscountCodeInput(discountCodeInput.trim());
        } else {
          setValidationError(result.message || 'Invalid or expired discount code.');
        }
      })
      .catch(err => {
        setValidationError(err || 'Failed to validate discount code.');
      })
      .finally(() => {
        setIsApplyingDiscount(false);
      });
  };

  const handleRemoveDiscount = () => {
    setDiscountCodeInput('');
    setValidationError(null);
    dispatch(clearDiscount());
    // Clear discount code from URL if present
    if (urlDiscountCode) {
      dispatch(setUrlDiscountCode(null));
    }
    toast.info('Discount removed.');
  };

  const handlePaymentFormSubmit = async (token: { token?: string; status: string }) => {
    if (!termsAccepted) {
      toast.error('You must accept the terms before subscribing');
      return;
    }
    if (isApplyingDiscount) {
      toast.warning('Please wait for discount validation to complete.');
      return;
    }

    setPaymentStatus('processing');
    if (!token?.token) {
      setPaymentStatus('error');
      toast.error('Payment processing failed. Please try again.');
      return;
    }
    if (!customerDetails) {
      setPaymentStatus('error');
      toast.error('Customer information is missing.');
      return;
    }

    try {
      const currentAppliedDiscount = appliedDiscountInfo;

      dispatch(
        createSubscription({
          sourceId: token.token,
          userId: user?.id || '',
          firstName: customerDetails?.firstName || user?.first_name || '',
          lastName: customerDetails?.lastName || user?.last_name || '',
          discountId: currentAppliedDiscount?.discountId,
          targetType: currentAppliedDiscount?.targetType,
        })
      );
      dispatch(setLoading(true));
    } catch (err) {
      dispatch(setLoading(false));
      setPaymentStatus('error');
      toast.error('Failed to initiate subscription creation. Please try again.');
      console.error('Error dispatching createSubscription:', err);
    }
  };

  const {
    regularTotal,
    firstCycleTotal,
    discountDisplay,
    appliedDiscountAmount,
    originalProductPrice,
    originalDeliveryFeePrice,
  } = useMemo(() => {
    const baseProductPrice =
      (appliedDiscountInfo?.originalProductPrice ?? (subscriptionDetails.productPrice || 0) * 100) /
      100;
    const baseDeliveryCharge =
      (appliedDiscountInfo?.originalDeliveryFeePrice ??
        (subscriptionDetails.deliveryCharge || 0) * 100) / 100;
    const quantity = subscriptionDetails.quantity || 1;

    const calcRegularSubtotal = baseProductPrice * quantity;
    let calcRegularTotal = calcRegularSubtotal + baseDeliveryCharge;

    let calcFirstCycleTotal = calcRegularTotal;
    let calcDiscountDisplay = '';
    let calcAppliedDiscountAmount = 0;

    if (appliedDiscountInfo) {
      calcAppliedDiscountAmount = (appliedDiscountInfo.discountAmount ?? 0) / 100;
      calcFirstCycleTotal =
        (appliedDiscountInfo.firstCycleTotalPreview ?? calcRegularTotal * 100) / 100;

      const targetText =
        appliedDiscountInfo.targetType === 'DELIVERY_FEE' ? 'Delivery Fee' : 'Product';
      if (appliedDiscountInfo.type?.includes('PERCENTAGE') && appliedDiscountInfo.value) {
        calcDiscountDisplay = `Discount (${appliedDiscountInfo.value}% off ${targetText})`;
      } else {
        calcDiscountDisplay = `Discount (-$${calcAppliedDiscountAmount.toFixed(2)} off ${targetText})`;
      }
    } else if (appliedReferralDiscountInfo) {
      // Handle referral discount
      const referralDiscountAmount = appliedReferralDiscountInfo.discountCents / 100;
      calcAppliedDiscountAmount = referralDiscountAmount;

      // Calculate first cycle total based on delivery type and discount type
      if (isOneTimeOrder) {
        calcFirstCycleTotal = appliedReferralDiscountInfo.finalSubtotalCents / 100;
      } else {
        // For subscription orders, use the discounted price for first week only
        calcFirstCycleTotal = appliedReferralDiscountInfo.finalSubtotalCents / 100;
        // Keep the regular total as the non-discounted price
        calcRegularTotal = calcRegularSubtotal + baseDeliveryCharge;
      }

      if (appliedReferralDiscountInfo.discountType === 'PERCENTAGE') {
        calcDiscountDisplay = `Referral Discount (${appliedReferralDiscountInfo.discountValue}% off)`;
      } else {
        calcDiscountDisplay = `Referral Discount (-$${calcAppliedDiscountAmount.toFixed(2)} off)`;
      }
    }

    return {
      regularTotal: calcRegularTotal,
      firstCycleTotal: calcFirstCycleTotal,
      discountDisplay: calcDiscountDisplay,
      appliedDiscountAmount: calcAppliedDiscountAmount,
      originalProductPrice: baseProductPrice,
      originalDeliveryFeePrice: baseDeliveryCharge,
    };
  }, [appliedDiscountInfo, appliedReferralDiscountInfo, subscriptionDetails, isOneTimeOrder]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  /**
   * End Custom Methods
   */

  console.log(appliedReferralDiscountInfo);

  if (isLoading || paymentStatus === 'processing') return <Spinner />;

  return (
    <>
      <SubscriptionTitle />
      <div className="max-w-5xl mx-auto mt-8">
        <SubscriptionStepper currentStep={currentStep} />
      </div>
      <div className="max-w-3xl mx-auto md:mb-6 mb-16">
        <Card>
          <CardContent className="p-4 md:p-6 space-y-6">
            {subscriptionProduct?.customAttributes?.enableOneTimeDelivery && (
              <>
                <section>
                  <SubscriptionTypeSelector />
                </section>

                <Separator />
              </>
            )}

            <section>
              <h2 className="text-lg font-semibold mb-2">Pricing Details</h2>
              <div className="bg-gray-50 p-4 rounded-md space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>
                    {subscriptionDetails.productName} ({subscriptionDetails.variationName}):
                  </span>
                  <span>${originalProductPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Delivery Charge:</span>
                  <span>${originalDeliveryFeePrice.toFixed(2)}</span>
                </div>
                {(appliedDiscountInfo || appliedReferralDiscountInfo) && (
                  <div className="flex justify-between text-red-600">
                    <span>{discountDisplay}:</span>
                    <span>-${appliedDiscountAmount.toFixed(2)}</span>
                  </div>
                )}
                <Separator className="my-1" />
                {isOneTimeOrder ? (
                  <div className="flex justify-between font-bold text-base">
                    <span>Total</span>
                    <span>${firstCycleTotal.toFixed(2)}</span>
                  </div>
                ) : (
                  <>
                    {(appliedDiscountInfo || appliedReferralDiscountInfo) && (
                      <div className="flex justify-between font-semibold text-gray-700">
                        <span>First week total:</span>
                        <span>${firstCycleTotal.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-bold text-base">
                      <span>
                        {appliedDiscountInfo || appliedReferralDiscountInfo
                          ? 'Regular Total:'
                          : 'Total per delivery:'}
                      </span>
                      <span>${regularTotal.toFixed(2)}</span>
                    </div>
                  </>
                )}
              </div>
            </section>

            <section>
              <h2 className="text-lg font-semibold mb-2">Discount Code</h2>
              <div className="flex gap-2">
                <Input
                  type="text"
                  placeholder="Enter discount code"
                  value={discountCodeInput}
                  onChange={e => {
                    setDiscountCodeInput(e.target.value.toUpperCase());
                    setValidationError(null);
                  }}
                  disabled={!!appliedDiscountInfo}
                  className={validationError ? 'border-red-500' : ''}
                />
                {appliedDiscountInfo ? (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleRemoveDiscount}
                    disabled={isApplyingDiscount}
                  >
                    Remove
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={handleApplyDiscount}
                    disabled={
                      isApplyingDiscount ||
                      !discountCodeInput.trim() ||
                      !!appliedReferralDiscountInfo
                    }
                    className="bg-mulberry hover:bg-mulberryHover"
                  >
                    {isApplyingDiscount ? 'Applying...' : BUTTON_TEXTS.APPLY_DISCOUNT}
                  </Button>
                )}
              </div>
              {validationError && <p className="text-red-500 text-sm mt-1">{validationError}</p>}
            </section>

            <section>
              <h2 className="text-lg font-semibold mb-2">Referral Code</h2>
              <div className="flex gap-2">
                <Input
                  type="text"
                  placeholder="Enter referral code"
                  value={referralCodeInput}
                  onChange={e => {
                    setReferralCodeInput(e.target.value.toUpperCase());
                    setReferralValidationError(null);
                  }}
                  disabled={!!appliedReferralDiscountInfo}
                  className={referralValidationError ? 'border-red-500' : ''}
                />
                {appliedReferralDiscountInfo ? (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleRemoveReferralCode}
                    disabled={isApplyingReferral}
                  >
                    Remove
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={handleApplyReferralCode}
                    disabled={
                      isApplyingReferral || !referralCodeInput.trim() || !!appliedDiscountInfo
                    }
                    className="bg-mulberry hover:bg-mulberryHover"
                  >
                    {isApplyingReferral ? 'Applying...' : 'Apply'}
                  </Button>
                )}
              </div>
              {referralValidationError && (
                <p className="text-red-500 text-sm mt-1">{referralValidationError}</p>
              )}
            </section>
            <Separator />

            <section>
              {isGiftOrder && (
                <div>
                  A gift email will be sent to {recipientDetails?.firstName}{' '}
                  {recipientDetails?.lastName} at {recipientDetails?.email}
                  {recipientDetails?.giftMessage && <> with the following gift message:</>}
                  {recipientDetails?.giftMessage && (
                    <div className="mt-2 p-3 bg-gray-100 rounded-md italic">
                      {recipientDetails?.giftMessage}
                    </div>
                  )}
                  <Separator />
                </div>
              )}

              {/* <ImportantInformation
                isGiftOrder={isGiftOrder}
                isOneTimeOrder={isOneTimeOrder}
                subscriptionDetails={subscriptionDetails}
              /> */}

              {/* <div className="flex items-center bg-mulberryLightest p-4 space-x-3 mt-3 border-mulberry border rounded-lg">
                <Controller
                  name="termsAccepted"
                  control={control}
                  render={({ field }) => (
                    <>
                      <input
                        type="checkbox"
                        id="termsAccepted"
                        className="h-5 w-5 text-mulberry  focus:ring-mulberry rounded accent-mulberry"
                        checked={field.value}
                        onChange={e => field.onChange(e.target.checked)}
                        required
                      />
                      <div className="flex flex-col">
                        <label
                          htmlFor="termsAccepted"
                          className="text-mulberry cursor-pointer text-base font-bold"
                        >
                          I understand and accept the terms listed above.
                        </label>
                      </div>
                    </>
                  )}
                />
              </div>
              {errors.termsAccepted && (
                <p className="text-red-500 text-sm mt-1">{errors.termsAccepted.message}</p>
              )} */}
            </section>

            <section>
              <PaymentInformation
                handlePaymentFormSubmit={handlePaymentFormSubmit}
                paymentStatus={paymentStatus}
                termsAccepted={termsAccepted}
                isLoading={isLoading}
                customerDetails={customerDetails}
                user={user}
                firstCycleTotal={firstCycleTotal}
                isOneTimeOrder={isOneTimeOrder}
              />
            </section>
          </CardContent>
        </Card>
      </div>
    </>
  );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ImportantInformation = ({
  isGiftOrder,
  isOneTimeOrder,
  subscriptionDetails,
}: {
  isGiftOrder: boolean;
  isOneTimeOrder: boolean;
  subscriptionDetails: SubscriptionDetailsInterface;
}) => {
  return (
    <>
      {isOneTimeOrder ? (
        <h2 className="text-lg font-semibold mb-2">
          {subscriptionDetails.orderFor === SUBSCRIPTION_ORDER_TYPES.ONE_TIME_GIFT
            ? 'What to Know Before Gifting a Home Delivery'
            : 'Important Information Before You Pay'}
        </h2>
      ) : (
        <h2 className="text-lg font-semibold mb-2">
          {subscriptionDetails.orderFor !== SUBSCRIPTION_ORDER_TYPES.SELF
            ? 'What to Know Before Gifting a Home Delivery Subscription'
            : 'Important Information Before You Subscribe'}
        </h2>
      )}

      <div className="bg-gray-50 p-4 rounded-md">
        <ol className="list-decimal pl-5 space-y-2 text-sm">
          {isGiftOrder ? (
            <>
              <li>
                Your lucky recipient will enjoy weekly deliveries of our farm-fresh, hand-picked
                mulberries. We&apos;ll send them an email with the details and a chance to accept or
                decline the gift. Here&apos;s why:
                <ol className="mt-2 pl-5 space-y-2 text-sm list-[lower-alpha]">
                  <li>Deliveries are made weekly on a specific day and time.</li>
                  <li>
                    Because our mulberries are highly perishable, they need to be received promptly.
                  </li>
                  <li>
                    If the recipient feels they won&apos;t be able to consistently receive them,
                    they can decline the gift now.
                  </li>
                  <li>
                    If they do, we&apos;ll cancel the subscription and you won&apos;t be responsible
                    for any undelivered mulberries. You&apos;ll only be charged once the gift is
                    accepted and deliveries begin.
                  </li>
                </ol>
              </li>
              <li>
                If they accept the gift, we will bill your credit card once a week for that
                week&apos;s delivery.
              </li>
            </>
          ) : (
            <>
              <li>
                <span className="font-bold">Mulberries are highly perishable.</span> Please make
                sure you&apos;re able to refrigerate them after delivery, ASAP.
              </li>
              {!isOneTimeOrder && (
                <li>
                  <span className="font-bold">
                    Billing is processed weekly at the Saturday midnight cutoff.
                  </span>
                </li>
              )}
            </>
          )}
          <li>
            <span className="font-bold">
              Delivery occurs within an 8-hour window. Delivery day is based on zone.
            </span>
          </li>
          <li>
            <span className="font-bold">Delivery day and time cannot be customized.</span>
          </li>
          <li>
            Deliveries have begun! After placing your order, you&apos;ll receive a notification
            within 3 days confirming your delivery day, route zone, and estimated arrival—based on
            harvest conditions and logistics
          </li>
          <li>
            If we&apos;re unable to deliver to {isGiftOrder ? 'their' : 'your'} address, you&apos;ll
            be notified and issued a full refund promptly.
          </li>
          <li>Mulberry season is short—ending late June.</li>
        </ol>
      </div>
    </>
  );
};

const PaymentInformation = ({
  handlePaymentFormSubmit,
  paymentStatus,
  termsAccepted,
  isLoading,
  customerDetails,
  user,
  firstCycleTotal,
  isOneTimeOrder,
}: {
  handlePaymentFormSubmit: (token: { token?: string; status: string }) => void;
  paymentStatus: string;
  termsAccepted: boolean;
  isLoading: boolean;
  customerDetails: CustomerDetailsInterface | null;
  user: Users | null;
  firstCycleTotal: number;
  isOneTimeOrder: boolean;
}) => {
  return (
    <>
      <h2 className="text-lg font-semibold mb-2">Payment Information</h2>
      <div className="mb-4">
        <label htmlFor="cardholderName" className="block text-sm font-medium mb-1">
          Cardholder Name
        </label>
        <input
          type="text"
          id="cardholderName"
          className="w-full p-2 border border-gray-300 rounded-md"
          placeholder="Name as it appears on card"
          defaultValue={`${customerDetails?.firstName || user?.first_name || ''} ${customerDetails?.lastName || user?.last_name || ''}`}
        />
      </div>
      <PaymentForm
        applicationId={process.env.NEXT_PUBLIC_SQUARE_APP_ID || ''}
        locationId={process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID || ''}
        cardTokenizeResponseReceived={handlePaymentFormSubmit}
        createVerificationDetails={() => {
          const cardholderNameInput = document.getElementById('cardholderName') as HTMLInputElement;
          const cardholderName = cardholderNameInput?.value || '';
          const nameParts = cardholderName.trim().split(' ');
          const firstName = nameParts[0] || '';
          const lastName = nameParts.slice(1).join(' ') || '';
          return {
            amount: String(Math.round(firstCycleTotal * 100)),
            currencyCode: 'USD',
            intent: 'STORE',
            billingContact: {
              familyName: lastName || user?.last_name || customerDetails?.lastName || '',
              givenName: firstName || user?.first_name || customerDetails?.firstName || '',
              email: user?.email || customerDetails?.email || '',
              country: 'US',
            },
          };
        }}
      >
        <CreditCard
          buttonProps={{
            css: {
              "[data-theme='dark'] &": {
                backgroundColor: '#892b76',
                '&:hover': { backgroundColor: '#6e225e' },
              },
              backgroundColor: '#892b76',
              '&:hover': { backgroundColor: '#6e225e' },
              // Remove isApplyingDiscount from visual disabling logic
              ...((isLoading || paymentStatus === 'processing' || !termsAccepted) && {
                opacity: '0.5',
                cursor: 'not-allowed',
                '&:hover': { backgroundColor: '#892b76' },
              }),
            },
          }}
        >
          {isLoading || paymentStatus === 'processing'
            ? 'Processing...'
            : isOneTimeOrder
              ? 'Pay'
              : 'Subscribe'}
        </CreditCard>
      </PaymentForm>

      {isOneTimeOrder ? (
        <div className="mt-4 text-xs text-gray-600">
          By paying, I authorize Very Mulberry (Habitera Farms LLC) to securely charge my card via
          Square for a delivery payment.
        </div>
      ) : (
        <div className="mt-4 text-xs text-gray-600">
          By subscribing, I authorize Very Mulberry (Habitera Farms LLC) to save my card securely
          with Square and automatically charge this card until the subscription ends or I cancel
          this authorization.
        </div>
      )}
      <div className="mt-3 flex justify-center">
        <Image
          src="/Square_LogoLockup_Black.png"
          alt="Powered by Square"
          width={120}
          height={30}
          className="object-contain"
        />
      </div>
    </>
  );
};

export default SubscriptionPayment;
