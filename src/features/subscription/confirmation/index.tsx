'use client';

import { CheckCir<PERSON>, Facebook, Instagram } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/src/components/ui/popover';
import SubscriptionStepper from '@/src/features/subscription/components/SubscriptionStepper';
import SubscriptionTitle from '@/src/features/subscription/components/SubscriptionTitle';
import { clearDiscount } from '@/src/features/subscription/slices/discountValidationSlice';
import {
  clearCustomerData,
  clearRecipientCustomerData,
} from '@/src/features/subscription/slices/subscriptionCustomerSlice';
import {
  resetSubscriptionDetails,
  selectSubscriptionDetails,
} from '@/src/features/subscription/slices/subscriptionDetailsSlice';
import { resetPaymentState } from '@/src/features/subscription/slices/subscriptionPaymentSlice';
import {
  BUTTON_TEXTS,
  ROUTES,
  SHARE_MESSAGES,
  VM_FB,
  VM_INSTAGRAM,
  VM_TIKTOK,
} from '@/src/lib/constants';
import { base64DecodeJson, isGiftHomeDelivery, isOneTimeHomeDelivery } from '@/src/lib/utils';
import { useAppDispatch, useAppSelector } from '@/src/store';

interface TokenData {
  orderFor?: string;
}

export default function SubscriptionConfirmation() {
  /**
   * Start Initials
   */
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();

  const [data, setData] = useState<TokenData | null>(null);

  const subscriptionDetails = useAppSelector(selectSubscriptionDetails);

  const token = searchParams.get('t');

  const isGiftOrder = isGiftHomeDelivery(data?.orderFor);

  const isOneTimeOrder = isOneTimeHomeDelivery(data?.orderFor);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    // Clear all subscription-related data from Redux store
    dispatch(resetSubscriptionDetails());
    dispatch(resetPaymentState());
    dispatch(clearCustomerData());
    dispatch(clearRecipientCustomerData());
    dispatch(clearDiscount());

    // Add event listener for popstate (browser back/forward buttons)
    const handlePopState = () => {
      // Redirect to home page if user tries to go back
      router.replace(ROUTES.SUBSCRIPTION);
    };

    window.addEventListener('popstate', handlePopState);

    // Clean up event listener when component unmounts
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [dispatch, router]);

  useEffect(() => {
    if (token) {
      setData(base64DecodeJson(token));
    }
  }, [token]);
  /**
   * End Lifecycle Methods
   */

  return (
    <>
      <SubscriptionTitle orderFor={data?.orderFor} />

      {/* Move stepper to top of page */}
      <div className="max-w-5xl mx-auto mt-8">
        <SubscriptionStepper
          currentStep={
            5 + (subscriptionDetails.deliveryDays?.length ? 1 : 0) + (data && isGiftOrder ? 1 : 0)
          }
          customOrderFor={data?.orderFor}
        />
      </div>

      {/* Change grid to single column */}
      <div className="max-w-3xl mx-auto px-4 md:mb-6 mb-16">
        <Card>
          <CardHeader className="bg-mulberry text-white rounded-t-lg text-center pb-6">
            <div className="flex justify-center mb-4">
              <CheckCircle size={48} />
            </div>
            <CardTitle className="text-xl font-bold">
              {isOneTimeOrder ? 'Thank you for your order!' : 'Thank you for your subscription!'}
            </CardTitle>
          </CardHeader>

          <CardContent className="p-2 md:p-6 space-y-6">
            <div className=" text-gray-600 mt-4">
              <SelfSubscriptionMessage isGiftOrder={isGiftOrder} isOneTimeOrder={isOneTimeOrder} />
              <div className="text-center mt-8 pt-4 border-t border-gray-200">
                <p className="mb-3 font-medium">
                  Follow us on @VeryMulberry for mulberry updates and behind the scenes.
                </p>
                <div className="flex justify-center gap-4">
                  <a
                    href={VM_FB}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-mulberry hover:opacity-80"
                  >
                    <Facebook size={24} />
                  </a>
                  <a
                    href={VM_TIKTOK}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-mulberry hover:opacity-80"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      viewBox="0 0 24 24"
                      width="24"
                      height="24"
                    >
                      <path d="M19.589 6.686a4.793 4.793 0 0 1-3.77-4.245V2h-3.445v13.672a2.896 2.896 0 0 1-5.201 1.743l-.002-.001.002.001a2.895 2.895 0 0 1 3.183-4.51v-3.5a6.329 6.329 0 0 0-5.394 10.692 6.33 6.33 0 0 0 10.857-4.424V8.687a8.182 8.182 0 0 0 4.773 1.526V6.79a4.831 4.831 0 0 1-1.003-.104z" />
                    </svg>
                  </a>
                  <a
                    href={VM_INSTAGRAM}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-mulberry hover:opacity-80"
                  >
                    <Instagram size={24} />
                  </a>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}

const SelfSubscriptionMessage = ({
  isGiftOrder,
  isOneTimeOrder,
}: {
  isGiftOrder: boolean;
  isOneTimeOrder: boolean;
}) => {
  // Handle copying subscription link to clipboard with browser compatibility
  const handleCopyLink = () => {
    // Create the full subscription URL
    const subscriptionLink = `${window.location.origin}${ROUTES.SUBSCRIPTION}`;

    // Try to use the clipboard API with fallbacks
    try {
      // Modern browsers
      navigator.clipboard
        .writeText(subscriptionLink)
        .then(() => {
          toast.success('Link copied to clipboard!');
        })
        .catch(_err => {
          // Fallback for browsers that don't support clipboard API
          const textArea = document.createElement('textarea');
          textArea.value = subscriptionLink;
          textArea.style.position = 'fixed'; // Avoid scrolling to bottom
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();

          try {
            const successful = document.execCommand('copy');
            if (successful) {
              toast.success('Link copied to clipboard!');
            } else {
              toast.error('Failed to copy link. Please try manually selecting and copying.');
            }
          } catch (_err) {
            toast.error('Failed to copy link. Please try manually selecting and copying.');
          }

          document.body.removeChild(textArea);
        });
    } catch (_err) {
      toast.error("Your browser doesn't support clipboard operations");
    }
  };

  return (
    <div className="text-center">
      {isGiftOrder
        ? isOneTimeOrder
          ? 'Thank you for gifting a Very Mulberry home delivery order!'
          : 'Thank you for gifting a Very Mulberry home delivery subscription!'
        : isOneTimeOrder
          ? 'Thank you for order!'
          : 'Thank you for subscribing!'}{' '}
      A confirmation receipt will be sent to your email shortly.
      <br />
      <br />
      We will follow up with delivery details as we finalize our delivery routes and zones.
      <br />
      Expect 4–5 days from order to delivery scheduling.
      <br />
      <br />
      To help bring our fresh, sweet mulberries to your neighborhood, please share our service with
      your friends, family, and neighbors. We appreciate it very much.
      <Popover>
        <PopoverTrigger asChild>
          <button className="text-mulberry hover:underline font-medium cursor-pointer">
            {BUTTON_TEXTS.CLICK_TO_SHARE}
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-0">
          <div className="py-1">
            <a
              href={`mailto:?subject=${encodeURIComponent(SHARE_MESSAGES.EMAIL_SUBJECT)}&body=${encodeURIComponent(SHARE_MESSAGES.EMAIL_BODY)}`}
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              {BUTTON_TEXTS.SHARE_VIA_EMAIL}
            </a>
            <a
              href={`sms:?body=${encodeURIComponent(SHARE_MESSAGES.SMS_BODY)}`}
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              {BUTTON_TEXTS.SHARE_VIA_SMS}
            </a>
            <button
              onClick={handleCopyLink}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              {BUTTON_TEXTS.COPY_LINK}
            </button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
