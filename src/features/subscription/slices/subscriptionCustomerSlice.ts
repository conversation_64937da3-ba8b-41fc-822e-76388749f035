import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { apiPost } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { RootState } from '@/src/store';

// Define customer details interface
export interface CustomerDetailsInterface {
  email?: string; // Make email optional to allow partial updates
  firstName?: string;
  lastName?: string;
  phone?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  deliveryNotes?: string;
}

export interface RecipientDetailsInterface {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  giftMessage?: string;
  deliveryNotes?: string;
}

interface SubscriptionCustomerState {
  loading: boolean;
  error: string | null;
  squareCustomerId: string | null;
  recipientSquareCustomerId: string | null; // New field
  recipientLoading: boolean; // New field
  recipientError: string | null; // New field
  customerDetails: CustomerDetailsInterface | null;
  recipientDetails: RecipientDetailsInterface | null; // New field
  shouldRedirect: boolean; // New field
}

const initialState: SubscriptionCustomerState = {
  loading: false,
  error: null,
  squareCustomerId: null,
  recipientSquareCustomerId: null, // Initialize
  recipientLoading: false, // Initialize
  recipientError: null, // Initialize
  customerDetails: null,
  recipientDetails: null, // Initialize
  shouldRedirect: true, // Default to true
};

// Create or get customer from Square API
export const getOrCreateCustomer = createAsyncThunk(
  'subscription/getOrCreateCustomer',
  async (customerData: CustomerDetailsInterface, { rejectWithValue }) => {
    try {
      const response = await apiPost(API_ENDPOINTS.GET_OR_CREATE_CUSTOMER, customerData); // Use apiPost

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.FAILED_TO_CREATE_CUSTOMER);
      }

      const data = await response.json();
      return { customerId: data.customerId, customerDetails: customerData };
    } catch (error) {
      console.warn('Error creating customer:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_CREATE_CUSTOMER);
    }
  }
);

// Create a new async thunk for recipient customer creation
export const getOrCreateRecipientCustomer = createAsyncThunk(
  'subscriptionCustomer/getOrCreateRecipientCustomer',
  async (customerData: CustomerDetailsInterface, { rejectWithValue }) => {
    try {
      // Similar implementation as getOrCreateCustomer but for recipient
      const response = await apiPost(API_ENDPOINTS.GET_OR_CREATE_CUSTOMER, customerData); // Use apiPost

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to create recipient customer');
      }

      const data = await response.json();
      return data.customerId;
    } catch (_error) {
      return rejectWithValue('Failed to create recipient customer');
    }
  }
);

const subscriptionCustomerSlice = createSlice({
  name: 'subscriptionCustomer',
  initialState,
  reducers: {
    clearCustomerData: state => {
      state.squareCustomerId = null;
      state.customerDetails = null;
      state.error = null;
    },
    clearRecipientCustomerData: state => {
      state.recipientSquareCustomerId = null;
      state.recipientDetails = null;
      state.recipientError = null;
    },
    setCustomerDetails: (state, action: PayloadAction<CustomerDetailsInterface>) => {
      state.customerDetails = action.payload;
    },
    setRecipientDetails: (state, action: PayloadAction<RecipientDetailsInterface>) => {
      state.recipientDetails = action.payload;
    },
    disableRedirect: state => {
      state.shouldRedirect = false;
    },
    enableRedirect: state => {
      state.shouldRedirect = true;
    },
    setCustomerLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setRecipientCustomerLoading: (state, action: PayloadAction<boolean>) => {
      state.recipientLoading = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(getOrCreateCustomer.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getOrCreateCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.squareCustomerId = action.payload.customerId;
      })
      .addCase(getOrCreateCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getOrCreateRecipientCustomer.pending, state => {
        state.recipientLoading = true;
        state.recipientError = null;
      })
      .addCase(getOrCreateRecipientCustomer.fulfilled, (state, action) => {
        state.recipientLoading = false;
        state.recipientSquareCustomerId = action.payload;
      })
      .addCase(getOrCreateRecipientCustomer.rejected, (state, action) => {
        state.recipientLoading = false;
        state.recipientError = action.payload as string;
      });
  },
});

export const {
  clearCustomerData,
  setCustomerDetails,
  setRecipientDetails,
  clearRecipientCustomerData,
  disableRedirect,
  enableRedirect,
  setCustomerLoading,
  setRecipientCustomerLoading,
} = subscriptionCustomerSlice.actions;

export const selectSquareCustomerId = (state: RootState) =>
  state.subscriptionCustomer.squareCustomerId;
export const selectCustomerLoading = (state: RootState) => state.subscriptionCustomer.loading;
export const selectCustomerError = (state: RootState) => state.subscriptionCustomer.error;
export const selectCustomerDetails = (state: RootState) =>
  state.subscriptionCustomer.customerDetails;
export const selectShouldRedirect = (state: RootState) => state.subscriptionCustomer.shouldRedirect;
export const selectRecipientDetails = (state: RootState) =>
  state.subscriptionCustomer.recipientDetails;
export const selectRecipientSquareCustomerId = (state: RootState) =>
  state.subscriptionCustomer.recipientSquareCustomerId;
export const selectRecipientCustomerLoading = (state: RootState) =>
  state.subscriptionCustomer.recipientLoading;
export const selectRecipientCustomerError = (state: RootState) =>
  state.subscriptionCustomer.recipientError;

export default subscriptionCustomerSlice.reducer;
