import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { apiPost } from '@/src/lib/apiClient';
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { RootState } from '@/src/store';
import {
  AppliedDiscount, // Keep if needed
  ValidateItemDiscountParams,
  ItemDiscountValidationResult,
  // DiscountTargetType, // Marked as unused
} from '@/src/types/ItemDiscount';

interface AppliedReferralDiscountResult {
  discountCents: number;
  discountType: string;
  discountValue: number;
  finalSubtotalCents: number;
  originalSubtotalCents: number;
  policyId: string;
  referrerEmail: string;
  valid: boolean;
}

// Update state to match the enhanced ValidationResult
interface DiscountValidationState {
  loading: boolean;
  error: string | null;
  // Store the successful validation result directly
  appliedDiscount:
    | (Omit<ItemDiscountValidationResult, 'isValid' | 'message'> & { isValid: true })
    | null;
  isValid: boolean | null; // Track overall validity state
  urlDiscountCode: string | null;
  referralCode: string | null;
  appliedReferralDiscount: AppliedReferralDiscountResult | null;
}

const initialState: DiscountValidationState = {
  loading: false,
  error: null,
  appliedDiscount: null,
  isValid: null,
  urlDiscountCode: null,
  referralCode: null,
  appliedReferralDiscount: null,
};

// Thunk remains the same, expects ItemDiscountValidationResult from API
export const validateDiscountCode = createAsyncThunk<
  ItemDiscountValidationResult, // Expect the full result structure
  ValidateItemDiscountParams
>(
  'discountValidation/validateCode',
  async ({ discountCode, productVariationId }, { rejectWithValue, getState }) => {
    try {
      const reducerState = getState() as RootState;
      const email = reducerState.subscriptionCustomer.customerDetails?.email;
      const customerId = reducerState.subscriptionCustomer.squareCustomerId;

      const response = await apiPost(API_ENDPOINTS.VALIDATE_DISCOUNT, {
        discountCode: discountCode.trim(),
        productVariationId: productVariationId,
        email,
        squareCustomerId: customerId,
      });

      const data: ItemDiscountValidationResult = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.message || 'Failed to validate discount code.');
      }

      // Return the full result
      return { ...data, _originalCode: discountCode.trim() }; // Add original code for context if needed
    } catch (err: unknown) {
      // Use unknown
      let message = 'Failed to validate discount code. Please try again.';
      if (err instanceof Error) {
        message = err.message;
      } else if (typeof err === 'string') {
        message = err;
      }
      return rejectWithValue(message);
    }
  }
);

export const validateReferralCode = createAsyncThunk(
  'referralSlice/getReferralPolicy',
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState() as RootState;
      const user = state.authentication.user;
      const subscriptionDetails = state.subscriptionDetails;
      const quantity = subscriptionDetails.quantity || 1;
      const productPrice = subscriptionDetails.productPrice || 0;
      const deliveryCharge = subscriptionDetails.deliveryCharge || 0;
      const orderSubtotalCents = Math.round((productPrice * quantity + deliveryCharge) * 100);

      const response = await apiPost('/api/referral/validate-referral', {
        referralCode: state.discountValidation.referralCode,
        refereeEmail: user?.email || '',
        orderQuantity: quantity,
        orderSubtotalCents: orderSubtotalCents,
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_GET_POLICY);
      }

      const data = await response.json();
      return data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_GET_POLICY);
    }
  }
);

const discountValidationSlice = createSlice({
  name: 'discountValidation',
  initialState,
  reducers: {
    clearDiscount: state => {
      state.appliedDiscount = null;
      state.error = null;
      state.isValid = null;
      state.urlDiscountCode = null;
    },
    clearReferralDiscount: state => {
      state.appliedReferralDiscount = null;
      state.referralCode = null;
      state.error = null;
    },
    setUrlDiscountCode: (state, action: PayloadAction<string | null>) => {
      state.urlDiscountCode = action.payload;
    },
    setReferralCode: (state, action: PayloadAction<string | null>) => {
      state.referralCode = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(validateDiscountCode.pending, state => {
        state.loading = true;
        state.error = null;
        state.appliedDiscount = null;
        state.isValid = null;
      })
      .addCase(validateDiscountCode.fulfilled, (state, action) => {
        state.loading = false;
        state.isValid = action.payload.isValid;
        if (action.payload.isValid) {
          // Store the successful result structure
          state.appliedDiscount = {
            ...action.payload, // Spread all fields from the result
            isValid: true, // Ensure isValid is true
            // Handle value conversion if necessary (though API should send string/bigint string)
            value: action.payload.value,
          };
          state.error = null;
        } else {
          state.error = action.payload.message;
          state.appliedDiscount = null;
        }
        state.urlDiscountCode = null;
      })
      .addCase(validateDiscountCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.appliedDiscount = null;
        state.isValid = false;
        state.urlDiscountCode = null;
      })
      .addCase(validateReferralCode.pending, state => {
        state.loading = true;
        state.error = null;
        state.appliedReferralDiscount = null;
      })
      .addCase(validateReferralCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.appliedReferralDiscount = null;
        state.referralCode = null;
      })
      .addCase(validateReferralCode.fulfilled, (state, action) => {
        state.loading = false;
        state.isValid = action.payload.valid;
        if (action.payload.valid) {
          // Store the successful result structure
          state.appliedReferralDiscount = action.payload;
          state.error = null;
        } else {
          state.error = action.payload.message;
          state.appliedReferralDiscount = null;
        }
      });
  },
});

export const { clearDiscount, setUrlDiscountCode, setReferralCode, clearReferralDiscount } =
  discountValidationSlice.actions;

// Selectors
export const selectAppliedDiscountInfo = (state: RootState) =>
  state.discountValidation.appliedDiscount;
export const selectDiscountLoading = (state: RootState) => state.discountValidation.loading;
export const selectDiscountError = (state: RootState) => state.discountValidation.error;
export const selectIsDiscountValid = (state: RootState) => state.discountValidation.isValid;
export const selectUrlDiscountCode = (state: RootState) => state.discountValidation.urlDiscountCode;
export const referralCode = (state: RootState) => state.discountValidation.referralCode;
export const appliedReferralDiscount = (state: RootState) =>
  state.discountValidation.appliedReferralDiscount;

// Keep old selector for compatibility if strictly needed, otherwise prefer selectAppliedDiscountInfo
export const selectAppliedDiscount = (state: RootState): AppliedDiscount | null => {
  const info = state.discountValidation.appliedDiscount;
  // Check if info exists and has the expected structure for AppliedDiscount
  if (
    info &&
    info.isValid &&
    info.discountId &&
    info.discountCodeName &&
    info.targetType &&
    info.firstCycleTotalPreview !== undefined &&
    info.discountAmount !== undefined &&
    info.originalProductPrice !== undefined &&
    info.originalDeliveryFeePrice !== undefined
  ) {
    // Perform a safer cast or mapping if types differ significantly
    return info as AppliedDiscount;
  }
  return null;
};

export default discountValidationSlice.reducer;
