import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { apiPost } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES, SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';
import { isGiftHomeDelivery } from '@/src/lib/utils';
import { RootState } from '@/src/store';
import { SubscriptionOrderType } from '@/src/types/Subscriptions';

interface AddressValidationState {
  loading: boolean;
  giftLoading: boolean; // New state for gift validation
  error: string | null;
  isAddressValid: boolean | null;
  showUnavailableDialog: boolean;
  showAvailableDialog: boolean;
  showZoneRequestDialog: boolean;
  deliveryDays: string[];
  zoneId: string;
  deliveryWindow: string;
  addressStatus: 'active' | 'inactive' | 'not_found' | null;
  orderFor: SubscriptionOrderType;
  email: string;
  missingAddressId: string | null;
}

interface ValidateAddressParams {
  address: string;
  zipCode: string;
  status?: 'active' | 'inactive' | 'not_found';
  email?: string;
  orderFor: SubscriptionOrderType;
  userAgentInfo?: any;
}

interface ValidateAddressResponse {
  isAvailable: boolean;
  status: 'active' | 'inactive' | 'not_found';
  deliveryDays?: string[];
  zoneId?: string;
  deliveryWindow?: string;
  orderFor: SubscriptionOrderType;
  email?: string;
  addressId?: string; // Add addressId to the response
}

// Initial state
const initialState: AddressValidationState = {
  loading: false,
  giftLoading: false, // New state for gift validation
  error: null,
  isAddressValid: null,
  showUnavailableDialog: false,
  showAvailableDialog: false,
  showZoneRequestDialog: false,
  deliveryDays: [],
  zoneId: '',
  deliveryWindow: '',
  addressStatus: null,
  orderFor: SUBSCRIPTION_ORDER_TYPES.SELF,
  email: '',
  missingAddressId: null,
};

/**
 * Checks if a zip code is within any active delivery zone
 */
export const validateAddress = createAsyncThunk<ValidateAddressResponse, ValidateAddressParams>(
  'addressValidation/validateAddress',
  async (params: ValidateAddressParams, { rejectWithValue }) => {
    try {
      // Check if zip code is in delivery zone
      const response = await apiPost(API_ENDPOINTS.VALIDATE_DELIVERY_ADDRESS, {
        zipCode: params.zipCode,
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.FAILED_TO_VALIDATE_ADDRESS);
      }

      const data = await response.json();
      const { isAvailable, deliveryDays, zoneId, deliveryWindow, status } = data;

      // Log the address and get the ID
      const addressId = await logMissingAddress({
        ...params,
        status,
        email: params?.email || '',
        orderFor: params.orderFor,
        userAgentInfo: params.userAgentInfo,
      });

      return {
        isAvailable,
        deliveryDays,
        zoneId,
        deliveryWindow,
        status,
        orderFor: params.orderFor,
        email: params?.email || '',
        addressId,
      };
    } catch (error) {
      console.warn('Error validating address:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_VALIDATE_ADDRESS);
    }
  }
);

/**
 * Logs a missing delivery address to Firestore
 */
const logMissingAddress = async (params: ValidateAddressParams) => {
  try {
    const response = await apiPost(API_ENDPOINTS.LOG_MISSING_ADDRESS, {
      email: params.email || '',
      address: params.address,
      zipCode: params.zipCode,
      status: params.status || '',
      orderFor: params.orderFor,
      userAgentInfo: params.userAgentInfo,
    });

    const data = await response.json();

    if (data.id) {
      // Use direct state update instead of dispatching from within the thunk
      return data.id;
    }

    return response.ok;
  } catch (_error) {
    return false;
  }
};

export const updateMissingAddressLog = createAsyncThunk(
  'addressValidation/updateMissingAddressLog',
  async (
    {
      data,
    }: {
      data: any;
    },
    { rejectWithValue, getState }
  ) => {
    try {
      const reducerState = getState() as RootState;
      const addressId = reducerState.addressValidation.missingAddressId;
      const response = await apiPost(API_ENDPOINTS.UPDATE_MISSING_ADDRESS_LOG, { data, addressId });

      return response.ok;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_UPDATE_ADDRESS_LOG);
    }
  }
);

// Create the slice
const addressValidationSlice = createSlice({
  name: 'addressValidation',
  initialState,
  reducers: {
    setShowUnavailableDialog: (state, action: PayloadAction<boolean>) => {
      state.showUnavailableDialog = action.payload;
    },
    setShowAvailableDialog: (state, action: PayloadAction<boolean>) => {
      state.showAvailableDialog = action.payload;
    },
    setShowZoneRequestDialog: (state, action: PayloadAction<boolean>) => {
      state.showZoneRequestDialog = action.payload;
    },
    setMissingAddressId: (state, action: PayloadAction<string | null>) => {
      state.missingAddressId = action.payload;
    },
    resetAllDialogs: state => {
      state.showUnavailableDialog = false;
      state.showAvailableDialog = false;
      state.showZoneRequestDialog = false;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(validateAddress.pending, (state, action) => {
        // Check if this is any type of gift validation
        if (isGiftHomeDelivery(action.meta.arg.orderFor)) {
          state.giftLoading = true;
        } else {
          state.loading = true;
        }
        state.error = null;
        // Reset all dialogs when starting a new validation
        state.showUnavailableDialog = false;
        state.showAvailableDialog = false;
        state.showZoneRequestDialog = false;
      })
      .addCase(validateAddress.fulfilled, (state, action) => {
        // Reset both loading states
        state.loading = false;
        state.giftLoading = false;
        state.isAddressValid = action.payload.isAvailable;
        state.addressStatus = action.payload.status;
        state.deliveryDays = action.payload.deliveryDays || [];
        state.zoneId = action.payload.zoneId || '';
        state.deliveryWindow = action.payload.deliveryWindow || '';
        state.orderFor = action.payload.orderFor;
        state.email = action.payload.email || '';
        state.missingAddressId = action.payload.addressId || null;

        // Show appropriate dialog based on validation result and status
        if (action.payload.isAvailable && action.payload.status === 'active') {
          state.showAvailableDialog = true;
        } else if (action.payload.status === 'inactive') {
          state.showZoneRequestDialog = true;
        } else if (action.payload.status === 'not_found') {
          state.showUnavailableDialog = true;
        }
      })
      .addCase(validateAddress.rejected, (state, action) => {
        state.loading = false;
        state.giftLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const {
  setShowUnavailableDialog,
  setShowAvailableDialog,
  setShowZoneRequestDialog,
  resetAllDialogs,
} = addressValidationSlice.actions;

// Export selectors
export const selectAddressValidationLoading = (state: RootState) => state.addressValidation.loading;
export const selectAddressValidationError = (state: RootState) => state.addressValidation.error;
export const selectIsAddressValid = (state: RootState) => state.addressValidation.isAddressValid;
export const selectShowUnavailableDialog = (state: RootState) =>
  state.addressValidation.showUnavailableDialog;
export const selectShowAvailableDialog = (state: RootState) =>
  state.addressValidation.showAvailableDialog;
export const selectShowZoneRequestDialog = (state: RootState) =>
  state.addressValidation.showZoneRequestDialog;
export const selectDeliveryDays = (state: RootState) => state.addressValidation.deliveryDays;
export const selectZoneId = (state: RootState) => state.addressValidation.zoneId;
export const selectDeliveryWindow = (state: RootState) => state.addressValidation.deliveryWindow;
export const selectAddressStatus = (state: RootState) => state.addressValidation.addressStatus;
export const selectOrderFor = (state: RootState) => state.addressValidation.orderFor;
export const selectGiftAddressValidationLoading = (state: RootState) =>
  state.addressValidation.giftLoading;
export const selectEnteredEmail = (state: RootState) => state.addressValidation.email;

export default addressValidationSlice.reducer;
