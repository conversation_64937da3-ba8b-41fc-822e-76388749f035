import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';
import { RootState } from '@/src/store';
import { SubscriptionOrderType } from '@/src/types/Subscriptions';

export interface SubscriptionDetailsInterface {
  address: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  zipCode: string;
  zoneId: string;
  productName: string;
  productPrice: number;
  deliveryCharge: number;
  startDate?: string;
  deliveryDay?: string;
  deliveryDays: string[];
  deliveryFrequency: string;
  deliveryWindow: string;
  email: string;
  deliveryNotes?: string;
  // Add variation fields
  variationId?: string;
  variationName?: string;
  variationImage?: string;
  // Add discount related fields
  appliedDiscountId?: string | null; // Added for discount
  appliedDiscountType?: string | null; // Added for discount
  appliedDiscountValue?: string | number | bigint | null; // Added for discount (allow bigint)
  // Add fields for subscription details from API
  id?: string;
  square_subscription_id?: string;
  status?: string;
  total_amount: number;
  created_at?: string;
  quantity: number;
  orderFor: SubscriptionOrderType;
}

// Initial state
const initialState: SubscriptionDetailsInterface = {
  address: '',
  addressLine1: '',
  addressLine2: '',
  city: '',
  state: '',
  zipCode: '',
  zoneId: '',
  startDate: '',
  productName: '',
  productPrice: 0,
  deliveryCharge: 0,
  deliveryDay: '',
  deliveryDays: [],
  deliveryFrequency: '',
  deliveryWindow: '',
  email: '',
  deliveryNotes: '',
  variationId: '',
  variationName: '',
  variationImage: '',
  appliedDiscountId: null, // Added for discount
  appliedDiscountType: null, // Added for discount
  appliedDiscountValue: null, // Added for discount
  total_amount: 0,
  quantity: 1,
  orderFor: SUBSCRIPTION_ORDER_TYPES.ONE_TIME,
};

const subscriptionDetailsSlice = createSlice({
  name: 'subscriptionDetails',
  initialState,
  reducers: {
    setSubscriptionDetails: (
      state,
      action: PayloadAction<Partial<SubscriptionDetailsInterface>>
    ) => {
      return { ...state, ...action.payload };
    },
    setDeliveryDay: (state, action: PayloadAction<string>) => {
      state.deliveryDay = action.payload;
    },
    setDeliveryDays: (state, action: PayloadAction<string[]>) => {
      state.deliveryDays = action.payload;
    },
    resetSubscriptionDetails: () => initialState,
  },
});

// Export actions and reducer
export const { setSubscriptionDetails, setDeliveryDay, setDeliveryDays, resetSubscriptionDetails } =
  subscriptionDetailsSlice.actions;

// Add a selector for discount information
export const selectDiscountInfo = (state: RootState) => ({
  discountId: state.subscriptionDetails.appliedDiscountId,
  discountType: state.subscriptionDetails.appliedDiscountType,
  discountValue: state.subscriptionDetails.appliedDiscountValue,
});

// Selectors
export const selectSubscriptionDetails = (state: RootState) => state.subscriptionDetails;
export const selectDeliveryDay = (state: RootState) => state.subscriptionDetails.deliveryDay;
export const selectDeliveryDays = (state: RootState) => state.subscriptionDetails.deliveryDays;
export const selectOrderFor = (state: RootState) => state.subscriptionDetails.orderFor;
export const selectSubscriptionAddress = (state: RootState) => ({
  address: state.subscriptionDetails.address,
  zipCode: state.subscriptionDetails.zipCode,
  zoneId: state.subscriptionDetails.zoneId,
});

export default subscriptionDetailsSlice.reducer;
