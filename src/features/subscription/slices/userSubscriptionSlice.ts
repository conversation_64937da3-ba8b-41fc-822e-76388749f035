//40-digital-execution-platform/src/features/subscription/slices/userSubscriptionSlice.ts
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { apiPost } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getFirebaseToken } from '@/src/lib/utils';
import { RootState } from '@/src/store';

// Types
interface UserSubscriptionState {
  subscriptions: any[];
  loading: boolean;
  error: string | null;
}

const initialState: UserSubscriptionState = {
  subscriptions: [],
  loading: false,
  error: null,
};

/**
 * Fetches user subscription details from Square API
 */
export const fetchUserSubscription = createAsyncThunk(
  'userSubscription/fetchUserSubscription',
  async (email: string, { rejectWithValue }) => {
    try {
      const token = await getFirebaseToken();
      //Token needed for get?
      // We need token to validate the user in end point.
      if (!token) throw new Error('Authentication token not found.');

      const response = await apiPost(
        API_ENDPOINTS.GET_USER_SUBSCRIPTION,
        { email },
        { headers: { Authorization: `Bearer ${token}` } } // Pass token via options
      );

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to fetch subscription');
      }

      return data.subscriptions || [];
    } catch (_error) {
      return rejectWithValue('Failed to fetch subscription. Please try again later.');
    }
  }
);

/**
 * Cancels a user subscription in Square
 */
export const cancelUserSubscription = createAsyncThunk(
  'userSubscription/cancelUserSubscription',
  async (subscriptionId: string, { rejectWithValue, getState }) => {
    try {
      const state = getState() as RootState;
      const userId = state.authentication.user?.firebase_user_id;
      const token = await getFirebaseToken();
      if (!token) throw new Error('Authentication token not found.');

      const response = await apiPost(
        API_ENDPOINTS.CANCEL_SUBSCRIPTION,
        {
          subscriptionId,
          firebase_user_id: userId,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
          // Enable replay protection
          useLimitedUseToken: true,
        }
      );

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to cancel subscription');
      }

      return { subscriptionId, ...data };
    } catch (_error) {
      return rejectWithValue('Failed to cancel subscription. Please try again later.');
    }
  }
);

/**
 * Pauses a user subscription in Square
 */
export const pauseUserSubscription = createAsyncThunk(
  'userSubscription/pauseUserSubscription',
  async (subscriptionId: string, { rejectWithValue, getState }) => {
    try {
      const state = getState() as RootState;
      const userId = state.authentication.user?.firebase_user_id;
      const token = await getFirebaseToken();
      if (!token) throw new Error('Authentication token not found.');

      const response = await apiPost(
        API_ENDPOINTS.PAUSE_SUBSCRIPTION, // Corrected endpoint
        { subscriptionId, firebase_user_id: userId },
        {
          headers: { Authorization: `Bearer ${token}` },
          // Enable replay protection
          useLimitedUseToken: true,
        }
      );

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to pause subscription');
      }

      return { subscriptionId, ...data };
    } catch (_error) {
      return rejectWithValue('Failed to pause subscription. Please try again later.');
    }
  }
);

// Add the updateSubscriptionCard action
export const updateSubscriptionCard = createAsyncThunk(
  'userSubscription/updateCard',
  async (
    { subscriptionId, sourceId }: { subscriptionId: string; sourceId: string },
    { rejectWithValue, getState }
  ) => {
    try {
      const state = getState() as RootState;
      const userId = state.authentication.user?.firebase_user_id;
      const token = await getFirebaseToken();
      if (!token) throw new Error('Authentication token not found.');

      const response = await apiPost(
        API_ENDPOINTS.UPDATE_SUBSCRIPTION, // Corrected endpoint
        { subscriptionId, sourceId, firebase_user_id: userId },
        {
          headers: { Authorization: `Bearer ${token}` },
          // Enable replay protection
          useLimitedUseToken: true,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.FAILED_TO_UPDATE_PAYMENT_METHOD);
      }

      return await response.json();
    } catch (error) {
      console.warn('Error updating subscription card:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_UPDATE_PAYMENT_METHOD);
    }
  }
);

const userSubscriptionSlice = createSlice({
  name: 'userSubscription',
  initialState,
  reducers: {
    clearSubscription: state => {
      state.subscriptions = [];
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchUserSubscription.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.subscriptions = action.payload;
      })
      .addCase(fetchUserSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(cancelUserSubscription.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(cancelUserSubscription.fulfilled, (state, action) => {
        state.loading = false;
        // Update the canceled subscription in the array
        state.subscriptions = state.subscriptions.map(subscription =>
          subscription.id === action.payload.subscriptionId
            ? { ...subscription, ...action.payload.subscription }
            : subscription
        );
      })
      .addCase(cancelUserSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(pauseUserSubscription.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(pauseUserSubscription.fulfilled, (state, action) => {
        state.loading = false;
        // Update the paused subscription in the array
        state.subscriptions = state.subscriptions.map(subscription =>
          subscription.id === action.payload.subscriptionId
            ? { ...subscription, ...action.payload.subscription }
            : subscription
        );
      })
      .addCase(pauseUserSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateSubscriptionCard.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubscriptionCard.fulfilled, state => {
        state.loading = false;
      })
      .addCase(updateSubscriptionCard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { clearSubscription } = userSubscriptionSlice.actions;
export default userSubscriptionSlice.reducer;
