import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { isAfter, parseISO, startOfToday } from 'date-fns';

import { apiPost } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { convertToPacificTime } from '@/src/lib/timezone';
import { isGiftHomeDelivery, isOneTimeHomeDelivery } from '@/src/lib/utils';
import { RootState } from '@/src/store';
import { DiscountTargetType } from '@/src/types/ItemDiscount';

import { updateMissingAddressLog } from './addressValidationSlice';

// Types
interface SubscriptionPaymentState {
  loading: boolean;
  error: string | null;
  subscriptionId: string | null;
  orderId: string | null; // Add orderId for one-time orders
}

const initialState: SubscriptionPaymentState = {
  loading: false,
  error: null,
  subscriptionId: null,
  orderId: null, // Initialize orderId
};

/**
 * Creates a subscription or one-time order using Square API
 */
export const createSubscription = createAsyncThunk(
  'subscription/createSubscription',
  async (
    {
      sourceId,
      userId,
      firstName,
      lastName,
      discountId,
      targetType,
    }: {
      sourceId: string;
      userId: string;
      firstName: string;
      lastName: string;
      discountId?: string;
      targetType?: DiscountTargetType;
    },
    { rejectWithValue, getState, dispatch }
  ) => {
    try {
      const reducerState = getState() as RootState;
      const subscriptionDetails = reducerState.subscriptionDetails;

      const isGiftOrder = isGiftHomeDelivery(subscriptionDetails.orderFor);

      const isOneTimeOrder = isOneTimeHomeDelivery(subscriptionDetails.orderFor);

      // Extract common details from Redux state
      const zoneId = subscriptionDetails.zoneId;
      const variationId = subscriptionDetails.variationId;
      const deliveryDate = subscriptionDetails.startDate as string;
      const zipCode = subscriptionDetails.zipCode;
      const address = subscriptionDetails.address;
      const deliveryWindow = subscriptionDetails.deliveryWindow;
      const addressLine1 = subscriptionDetails.addressLine1;
      const addressLine2 = isGiftOrder
        ? reducerState.subscriptionCustomer.recipientDetails?.addressLine2
        : reducerState.subscriptionCustomer.customerDetails?.addressLine2;
      const city = subscriptionDetails.city;
      const state = subscriptionDetails.state;
      const quantity = subscriptionDetails.quantity || 1;
      const email = reducerState.subscriptionCustomer.customerDetails?.email;
      const phone = reducerState.subscriptionCustomer.customerDetails?.phone;
      const orderFor = subscriptionDetails.orderFor;
      const deliveryNotes = isGiftOrder
        ? reducerState.subscriptionCustomer.recipientDetails?.deliveryNotes || ''
        : reducerState.subscriptionCustomer.customerDetails?.deliveryNotes || '';
      const recipientFirstName = reducerState.subscriptionCustomer.recipientDetails?.firstName;
      const recipientLastName = reducerState.subscriptionCustomer.recipientDetails?.lastName;
      const recipientEmail = reducerState.subscriptionCustomer.recipientDetails?.email;
      const recipientPhone = reducerState.subscriptionCustomer.recipientDetails?.phone;
      const recipientGiftMessage = reducerState.subscriptionCustomer.recipientDetails?.giftMessage;
      const recipientSquareCustomerId =
        reducerState.subscriptionCustomer.recipientSquareCustomerId || '';
      const customerId = reducerState.subscriptionCustomer.squareCustomerId;
      const userAppliedReferralInfo = reducerState.discountValidation.appliedReferralDiscount;
      const referralCode = reducerState.discountValidation.referralCode;

      // Validate start date is in the future only if deliveryDate is provided
      if (deliveryDate) {
        const today = convertToPacificTime(startOfToday());
        const parsedStartDate = parseISO(deliveryDate);
        if (!isAfter(parsedStartDate, today)) {
          return rejectWithValue('Start date must be in the future');
        }
      }

      // Common payload for both one-time and subscription orders
      const commonPayload = {
        variationId,
        deliveryDate,
        zoneId,
        address,
        customerId,
        userId,
        email,
        phone,
        zipCode,
        sourceId,
        deliveryWindow,
        addressLine1,
        addressLine2,
        city,
        state,
        firstName,
        lastName,
        deliveryNotes,
        quantity,
        discountId,
        orderFor,
        recipientFirstName,
        recipientLastName,
        recipientEmail,
        recipientPhone,
        recipientGiftMessage,
        recipientSquareCustomerId,
        targetType,
        ...(referralCode ? { referralCode } : {}),
        ...(userAppliedReferralInfo ? { referralPolicyId: userAppliedReferralInfo.policyId } : {}),
      };

      // Choose API endpoint based on order type
      const endpoint = isOneTimeOrder
        ? API_ENDPOINTS.CREATE_ONE_TIME_ORDER
        : API_ENDPOINTS.CREATE_SUBSCRIPTION;

      const response = await apiPost(endpoint, commonPayload, {
        useLimitedUseToken: true,
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.FAILED_TO_CREATE_SUBSCRIPTION);
      }

      const resp = await response.json();

      dispatch(
        updateMissingAddressLog({
          data: {
            order: isOneTimeOrder
              ? { square_order_id: resp.squareOrderId, order_id: resp.orderId }
              : {
                  square_subscription_id: resp.squareSubscriptionId,
                  subscription_id: resp.subscriptionId,
                },
          },
        })
      );

      return {
        ...resp,
        isOneTimeOrder, // Include flag to identify response type
      };
    } catch (error) {
      console.warn('Error creating subscription/order:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_CREATE_SUBSCRIPTION);
    }
  }
);

const subscriptionPaymentSlice = createSlice({
  name: 'subscriptionPayment',
  initialState,
  reducers: {
    resetPaymentState: state => {
      state.loading = false;
      state.error = null;
      state.subscriptionId = null;
      state.orderId = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(createSubscription.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.isOneTimeOrder) {
          state.orderId = action.payload.orderId;
        } else {
          state.subscriptionId = action.payload.subscriptionId;
        }
      })
      .addCase(createSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { resetPaymentState, setLoading } = subscriptionPaymentSlice.actions;

// Selectors
export const selectSubscriptionLoading = (state: RootState) => state.subscriptionPayment.loading;
export const selectSubscriptionError = (state: RootState) => state.subscriptionPayment.error;
export const selectSubscriptionId = (state: RootState) => state.subscriptionPayment.subscriptionId;
export const selectOrderId = (state: RootState) => state.subscriptionPayment.orderId;

export default subscriptionPaymentSlice.reducer;
