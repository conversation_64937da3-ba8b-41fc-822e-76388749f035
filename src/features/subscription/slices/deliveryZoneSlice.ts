import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { apiGet } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { RootState } from '@/src/store';

// Types
interface DeliveryZone {
  delivery_days: any;
  delivery_window: string;
  city_name: string;
  zone_name: string;
  id: string;
  name: string;
  deliveryDays: string[];
  zip_codes: string[];
  isActive: boolean;
  startDate: string | null;
  endDate: string | null;
  start_date: string | null;
  end_date: string | null;
}

interface DeliveryZoneState {
  loading: boolean;
  error: string | null;
  zone: DeliveryZone | null;
  zones: DeliveryZone[];
}

const initialState: DeliveryZoneState = {
  loading: false,
  error: null,
  zone: null,
  zones: [],
};

/**
 * Fetches delivery zone details by ID
 */
export const fetchDeliveryZone = createAsyncThunk<DeliveryZone, string>(
  'deliveryZone/fetchDeliveryZone',
  async (zoneId: string, { rejectWithValue }) => {
    try {
      const response = await apiGet(`${API_ENDPOINTS.GET_DELIVERY_ZONE}?zoneId=${zoneId}`); // Use apiGet

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.GENERIC_ERROR);
      }

      return await response.json();
    } catch (error) {
      console.warn('Error fetching delivery zone:', error);
      return rejectWithValue(ERROR_MESSAGES.GENERIC_ERROR);
    }
  }
);

/**
 * Fetches all active delivery zones
 */
export const fetchAllDeliveryZones = createAsyncThunk<DeliveryZone[], void>(
  'deliveryZone/fetchAllDeliveryZones',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiGet(API_ENDPOINTS.GET_DELIVERY_ZONES);

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.GENERIC_ERROR);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching delivery zones:', error);
      return rejectWithValue(ERROR_MESSAGES.GENERIC_ERROR);
    }
  }
);

const deliveryZoneSlice = createSlice({
  name: 'deliveryZone',
  initialState,
  reducers: {
    resetDeliveryZone: state => {
      state.loading = false;
      state.error = null;
      state.zone = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchDeliveryZone.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDeliveryZone.fulfilled, (state, action: PayloadAction<DeliveryZone>) => {
        state.loading = false;
        state.zone = action.payload;
      })
      .addCase(fetchDeliveryZone.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAllDeliveryZones.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllDeliveryZones.fulfilled, (state, action: PayloadAction<DeliveryZone[]>) => {
        state.loading = false;
        state.zones = action.payload;
      })
      .addCase(fetchAllDeliveryZones.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { resetDeliveryZone } = deliveryZoneSlice.actions;

// Export selectors
export const selectDeliveryZoneLoading = (state: RootState) => state.deliveryZone.loading;
export const selectDeliveryZoneError = (state: RootState) => state.deliveryZone.error;
export const selectDeliveryZone = (state: RootState) => state.deliveryZone.zone;
export const selectDeliveryDays = (state: RootState) => state.deliveryZone.zone?.deliveryDays || [];
export const selectAllDeliveryZones = (state: RootState) => state.deliveryZone.zones;

export default deliveryZoneSlice.reducer;
