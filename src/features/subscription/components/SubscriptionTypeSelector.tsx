'use client';

import { useEffect, useState } from 'react';

import { Card } from '@/src/components/ui/card';
import { Label } from '@/src/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/src/components/ui/radio-group';
import { SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';
import { isOneTimeHomeDelivery } from '@/src/lib/utils';
import { useAppDispatch, useAppSelector } from '@/src/store';

import { selectOrderFor, setSubscriptionDetails } from '../slices/subscriptionDetailsSlice';

export default function SubscriptionTypeSelector() {
  /**
   * Start initials
   */
  const dispatch = useAppDispatch();

  const [selectedType, setSelectedType] = useState('weekly');

  const selectedOrderFor = useAppSelector(selectOrderFor);
  /**
   * End initials
   */

  useEffect(() => {
    if (!isOneTimeHomeDelivery(selectedOrderFor)) {
      setSelectedType('weekly');
    } else {
      setSelectedType('onetime');
    }
  }, []);

  /**
   * Start custom methods
   */
  const handleChange = (value: string) => {
    setSelectedType(value);

    let orderFor = selectedOrderFor;
    if (value === 'onetime' && selectedOrderFor === SUBSCRIPTION_ORDER_TYPES.SELF) {
      orderFor = SUBSCRIPTION_ORDER_TYPES.ONE_TIME;
    } else if (value === 'onetime' && selectedOrderFor === SUBSCRIPTION_ORDER_TYPES.SINGLE_GIFT) {
      orderFor = SUBSCRIPTION_ORDER_TYPES.ONE_TIME_GIFT;
    } else if (
      value === 'weekly' &&
      (selectedOrderFor === SUBSCRIPTION_ORDER_TYPES.SINGLE_GIFT ||
        selectedOrderFor === SUBSCRIPTION_ORDER_TYPES.ONE_TIME_GIFT)
    ) {
      orderFor = SUBSCRIPTION_ORDER_TYPES.SINGLE_GIFT;
    } else if (
      value === 'weekly' &&
      (selectedOrderFor === SUBSCRIPTION_ORDER_TYPES.SELF ||
        selectedOrderFor === SUBSCRIPTION_ORDER_TYPES.ONE_TIME)
    ) {
      orderFor = SUBSCRIPTION_ORDER_TYPES.SELF;
    }

    dispatch(
      setSubscriptionDetails({
        orderFor,
      })
    );
  };
  /**
   * End custom methods
   */

  return (
    <div className="space-y-4">
      <RadioGroup
        value={selectedType}
        onValueChange={handleChange}
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        <Card
          className={`relative p-4 cursor-pointer border-2 ${selectedType === 'weekly' ? 'border-mulberry' : 'border-gray-200'}`}
          onClick={() => handleChange('weekly')}
        >
          <RadioGroupItem value="weekly" id="weekly" className="absolute top-4 right-4" />
          <div className="pl-0 pt-0">
            <Label htmlFor="weekly" className="text-base font-medium cursor-pointer">
              Weekly Delivery Subscription
            </Label>
          </div>
        </Card>
        <Card
          className={`relative p-4 cursor-pointer border-2 ${selectedType === 'onetime' ? 'border-mulberry' : 'border-gray-200'}`}
          onClick={() => handleChange('onetime')}
        >
          <RadioGroupItem value="onetime" id="one-time" className="absolute top-4 right-4" />
          <div className="pl-0 pt-0">
            <Label htmlFor="one-time" className="text-base font-medium cursor-pointer">
              One-Time Delivery
            </Label>
          </div>
        </Card>
      </RadioGroup>
    </div>
  );
}
