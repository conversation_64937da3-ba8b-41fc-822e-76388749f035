import { <PERSON><PERSON>ir<PERSON>, ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@/src/components/ui/button';
import { Card } from '@/src/components/ui/card';
import {
  setShowAvailableDialog,
  setShowUnavailableDialog,
} from '@/src/features/subscription/slices/addressValidationSlice';
import { selectSubscriptionDetails } from '@/src/features/subscription/slices/subscriptionDetailsSlice';
import { ROUTES } from '@/src/lib/constants';
import { isGiftHomeDelivery, isOneTimeHomeDelivery } from '@/src/lib/utils';
import { RootState, useAppDispatch } from '@/src/store';

import { disableRedirect } from '../slices/subscriptionCustomerSlice';

interface SubscriptionStepperProps {
  currentStep: number;
  customOrderFor?: string | undefined;
}

interface SubscriptionDetails {
  address?: string;
  deliveryDays?: string[];
  quantity?: number;
  productName?: string;
  startDate?: string | Date;
  email?: string;
  total_amount?: number;
  [key: string]: any; // Allow for additional properties
}

interface Step {
  id: number;
  title: string;
  isCompleted?: (details: SubscriptionDetails) => boolean;
}

const steps = (
  subscriptionDetails: SubscriptionDetails,
  isGiftOrder: boolean,
  isOneTimeOrder: boolean
): Step[] => {
  const hasDeliveryDays = (subscriptionDetails.deliveryDays ?? []).length > 0;

  return [
    { id: 1, title: 'Verify Delivery Address' },
    { id: 2, title: 'Select Product' },
    ...(hasDeliveryDays ? [{ id: 3, title: 'Delivery Start Date' }] : []),
    {
      id: getStepId(3, hasDeliveryDays),
      title: isGiftOrder ? 'Your Details' : 'Delivery Details',
    },
    ...(isGiftOrder
      ? [
          {
            id: getStepId(3, hasDeliveryDays, isGiftOrder),
            title: 'Gift Details',
          },
        ]
      : []),
    {
      id: getStepId(4, hasDeliveryDays, isGiftOrder),
      title: 'Verify Order',
    },
    {
      id: getStepId(5, hasDeliveryDays, isGiftOrder),
      title: isOneTimeOrder ? 'Confirm Delivery' : 'Confirm Subscription',
    },
  ];
};

const getStepId = (baseId: number, hasDeliveryDays: boolean, isGiftOrder?: boolean): number => {
  if (hasDeliveryDays && isGiftOrder === undefined) {
    return baseId + 1;
  }
  if (hasDeliveryDays && isGiftOrder) {
    return baseId + 2;
  }
  if (hasDeliveryDays || isGiftOrder) {
    return baseId + 1;
  }
  return baseId;
};

export default function SubscriptionStepper({
  currentStep,
  customOrderFor,
}: SubscriptionStepperProps) {
  /**
   * Start Initials
   */
  const router = useRouter();
  const dispatch = useAppDispatch();

  const subscriptionDetails = useSelector((state: RootState) => selectSubscriptionDetails(state));

  const isOneTimeOrder =
    isOneTimeHomeDelivery(customOrderFor) || isOneTimeHomeDelivery(subscriptionDetails.orderFor);
  const isGiftOrder =
    isGiftHomeDelivery(subscriptionDetails.orderFor) || isGiftHomeDelivery(customOrderFor);

  const hasDeliveryDays = subscriptionDetails.deliveryDays?.length > 0;

  // Get steps based on current subscription details
  const currentSteps = steps(subscriptionDetails, isGiftOrder, isOneTimeOrder);

  // Adjust currentStep to account for the first step being already completed
  const adjustedCurrentStep = currentStep + 1;

  // Check if we're on the confirmation page
  const isConfirmationPage = currentStep === (hasDeliveryDays ? 1 : 0) + (isGiftOrder ? 1 : 0) + 5;
  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  // Function to check if a step is completed
  const isStepCompleted = (step: Step): boolean => {
    if (step.isCompleted) {
      return step.isCompleted({ ...subscriptionDetails, customOrderFor });
    }
    return step.id < adjustedCurrentStep;
  };

  // Function to navigate to specific step
  const goToStep = (stepId: number) => {
    // Only allow navigation to completed steps
    if (
      stepId >= adjustedCurrentStep &&
      !isStepCompleted(currentSteps.find(step => step.id === stepId)!)
    )
      return;

    // Map step IDs directly to their corresponding routes
    switch (stepId) {
      case 1: // Weekly Delivery Day
        // Reset address validation dialogs before navigating
        dispatch(setShowAvailableDialog(false));
        dispatch(setShowUnavailableDialog(false));
        router.push(ROUTES.SUBSCRIPTION);
        break;
      case 2: // Select Product
        router.push(ROUTES.SUBSCRIPTION_PRODUCT);
        break;
      case 3: // Delivery Start Date (if available) or Profile (if no delivery days)
        if (hasDeliveryDays) {
          router.push(`${ROUTES.SUBSCRIPTION_DELIVERY_DAY}`);
        } else {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_USER_INFO);
        }
        break;
      case 4: // My Very Mulberry Profile (if delivery days) or Verify Order (if no delivery days)
        if (hasDeliveryDays) {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_USER_INFO);
        } else if (isGiftOrder) {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_GIFT_DETAILS);
        } else {
          router.push(ROUTES.SUBSCRIPTION_PAYMENT);
        }
        break;
      case 5: // Gift Details or Verify Order
        if (isGiftOrder && hasDeliveryDays) {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_GIFT_DETAILS);
        } else {
          router.push(ROUTES.SUBSCRIPTION_PAYMENT);
        }
        break;
      case 6: // Verify Order
        router.push(ROUTES.SUBSCRIPTION_PAYMENT);
        break;
      default:
        // If we can't determine the route, do nothing
        break;
    }
  };

  // Function to handle back button click
  const mobileGoToStep = () => {
    if (!currentStep) {
      // Fallback to browser back if no previous step is found
      router.back();
      return;
    }

    // Navigate to the previous step
    switch (currentStep) {
      case 1:
        dispatch(setShowAvailableDialog(false));
        dispatch(setShowUnavailableDialog(false));
        router.push(ROUTES.SUBSCRIPTION);
        break;
      case 2:
        dispatch(setShowAvailableDialog(false));
        dispatch(setShowUnavailableDialog(false));
        router.push(ROUTES.SUBSCRIPTION);
        break;
      case 3:
        router.push(ROUTES.SUBSCRIPTION_PRODUCT);
        break;
      case 4:
        if (hasDeliveryDays && !isGiftOrder) {
          router.push(ROUTES.SUBSCRIPTION_DELIVERY_DAY);
        } else if (!hasDeliveryDays && isGiftOrder) {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_USER_INFO);
        } else if (hasDeliveryDays && isGiftOrder) {
          router.push(ROUTES.SUBSCRIPTION_DELIVERY_DAY);
        } else {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_USER_INFO);
        }
        break;
      case 5:
        if (hasDeliveryDays && isGiftOrder) {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_USER_INFO);
        } else if (hasDeliveryDays && !isGiftOrder) {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_USER_INFO);
        } else if (!hasDeliveryDays && isGiftOrder) {
          dispatch(disableRedirect());
          router.push(ROUTES.SUBSCRIPTION_GIFT_DETAILS);
        }
        break;
      case 6:
        dispatch(disableRedirect());
        router.push(ROUTES.SUBSCRIPTION_GIFT_DETAILS);
        break;
      default:
        // Fallback to browser back as a last resort
        router.back();
    }
  };
  /**
   * End Custom Methods
   */
  return (
    <Card className="md:p-4 p-2 border-0 shadow-none  bg-gray-50 md:bg-transparent rounded-lg mx-2 mb-4 md:mb-0">
      {/* Horizontal stepper for desktop */}
      <div className="hidden md:flex justify-between items-center mb-6 w-full max-w-3xl mx-auto">
        {currentSteps.map((step, index) => (
          <React.Fragment key={step.id}>
            {/* Step with circle and label - responsive fixed width */}
            <div
              className="flex flex-col items-center md:w-20 lg:w-28 xl:w-32"
              onClick={() => isStepCompleted(step) && !isConfirmationPage && goToStep(step.id)}
              style={
                isStepCompleted(step) && !isConfirmationPage ? { cursor: 'pointer' } : undefined
              }
            >
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full mb-2 ${
                  step.id < currentStep
                    ? 'bg-mulberry text-white'
                    : step.id === currentStep
                      ? 'bg-mulberry text-white'
                      : 'bg-gray-300 text-gray-500'
                }`}
              >
                {step.id < currentStep ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span className="text-sm">{step.id}</span>
                )}
              </div>
              <div className="h-10 flex items-center">
                <span className="text-xs text-center font-medium break-words w-full">
                  {step.title}
                </span>
              </div>
            </div>

            {/* Connector line between steps */}
            {index < currentSteps.length - 1 && (
              <div
                className={`h-0.5 flex-grow mx-[-3rem] ${
                  step.id < adjustedCurrentStep ? 'bg-mulberry' : 'bg-gray-300'
                }`}
                style={{
                  position: 'relative',
                  top: '-22px', // Position it to align with the circles
                  zIndex: -1, // Place behind the circles
                }}
              />
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Vertical stepper for mobile */}
      <div className="space-y-6 md:hidden">
        <div className="flex justify-between mb-0">
          <h3 className="font-medium text-mulberry">
            Step {currentStep} of {currentSteps.length}
          </h3>
          {currentStep < currentSteps.length && (
            <Button
              variant="outline"
              size="sm"
              className=" text-mulberry hover:bg-mulberryLightest flex items-center gap-1"
              onClick={mobileGoToStep}
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
          )}
        </div>

        {/* Display the title of the current step */}
        <h4 className="font-bold text-base text-mulberry !mt-0">
          {currentSteps[currentStep - 1]?.title}
        </h4>
      </div>
    </Card>
  );
}
