import { <PERSON><PERSON>ir<PERSON>, PauseCircle, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { CreditCard, PaymentForm } from 'react-square-web-payments-sdk';
import { toast } from 'sonner';
import {
  SubscriptionAction as SquareSubscriptionAction,
  Subscription as SquareSubscription,
  Order as SquareOrder,
  Invoice as SquareInvoice,
  Card as SquareCard,
} from 'square';

import { Badge } from '@/src/components/ui/badge';
import { Button } from '@/src/components/ui/button';
import { Card, CardHeader, CardContent } from '@/src/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/src/components/ui/dialog';
import {
  cancelUserSubscription,
  pauseUserSubscription,
  updateSubscriptionCard,
  fetchUserSubscription,
} from '@/src/features/subscription/slices/userSubscriptionSlice';
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { formatDate, isSubscriptionCancellationAllowed } from '@/src/lib/utils';
import { AppDispatch, RootState, useAppSelector } from '@/src/store';
import { CatalogDetails } from '@/src/types/Subscription';

interface SubscriptionCardProps {
  subscriptions?: (SquareSubscription & {
    orderDetails: SquareOrder;
    cardDetails: SquareCard;
    catalogDetails: CatalogDetails;
    invoices: SquareInvoice[];
  })[];
  isHomePage?: boolean;
}

const SubscriptionsCard: React.FC<SubscriptionCardProps> = ({
  subscriptions,
  isHomePage = false,
}) => {
  /**
   * Start Initials
   */
  // Replace the single isManageOpen state with a state that tracks by subscription ID
  const [openManageIds, setOpenManageIds] = useState<Set<string>>(new Set());
  const user = useAppSelector((state: RootState) => state.authentication.user);

  // Add state for dialog and loading
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<SquareSubscription | null>(null);

  // Add state for pause dialog and loading
  const [isPauseDialogOpen, setIsPauseDialogOpen] = useState(false);
  const [isPausing, setIsPausing] = useState(false);

  // Add state for update card dialog
  const [isUpdateCardDialogOpen, setIsUpdateCardDialogOpen] = useState(false);

  const dispatch = useDispatch<AppDispatch>();

  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  const handleCancelSubscription = async () => {
    if (!selectedSubscription?.id) return;

    if (!isSubscriptionCancellationAllowed()) {
      toast.error(ERROR_MESSAGES.SUBSCRIPTION_CANCELLATION_NOT_ALLOWED);
      return;
    }

    try {
      setIsCancelling(true);
      await dispatch(cancelUserSubscription(selectedSubscription?.id)).unwrap();

      // Fetch updated subscription data
      await dispatch(fetchUserSubscription(user?.email as string)).unwrap();

      toast.success(SUCCESS_MESSAGES.SUBSCRIPTION_CANCELLED);
      setIsDialogOpen(false);
    } catch (error) {
      toast.error(typeof error === 'string' ? error : ERROR_MESSAGES.FAILED_TO_CANCEL_SUBSCRIPTION);
    } finally {
      setIsCancelling(false);
    }
  };

  // Add pause handler
  const handlePauseSubscription = async () => {
    if (!selectedSubscription?.id) return;

    try {
      setIsPausing(true);
      await dispatch(pauseUserSubscription(selectedSubscription?.id)).unwrap();

      // Fetch updated subscription data
      await dispatch(fetchUserSubscription(user?.email as string)).unwrap();

      toast.success(SUCCESS_MESSAGES.SUBSCRIPTION_PAUSED);
      setIsPauseDialogOpen(false);
    } catch (error) {
      // Display the exact error message from the API
      toast.error(typeof error === 'string' ? error : ERROR_MESSAGES.FAILED_TO_PAUSE_SUBSCRIPTION);
    } finally {
      setIsPausing(false);
    }
  };

  // Add update card handler
  const handleUpdateCard = async (token: { token?: string; status: string }) => {
    if (!selectedSubscription?.id) return;

    if (!token?.token) {
      toast.error('Payment processing failed. Please try again.');
      return;
    }

    try {
      await dispatch(
        updateSubscriptionCard({
          subscriptionId: selectedSubscription.id,
          sourceId: token.token,
        })
      ).unwrap();

      // Fetch updated subscription data
      await dispatch(fetchUserSubscription(user?.email as string)).unwrap();

      toast.success(SUCCESS_MESSAGES.PAYMENT_METHOD_UPDATED);
      setIsUpdateCardDialogOpen(false);
    } catch (error) {
      toast.error(
        typeof error === 'string' ? error : ERROR_MESSAGES.FAILED_TO_UPDATE_PAYMENT_METHOD
      );
    }
  };

  const isUserCanceled = (subscription: SquareSubscription): boolean => {
    if (!subscription?.canceledDate) return false;

    const now = getPacificTimeNow();
    const canceledDate = new Date(subscription?.canceledDate);
    const startDate = subscription?.startDate ? new Date(subscription?.startDate) : null;
    const chargedThroughDate = subscription?.chargedThroughDate
      ? new Date(subscription?.chargedThroughDate)
      : null;

    // User canceled before it started
    if (startDate && canceledDate.getTime() === startDate.getTime()) return true;

    // User canceled after 1 charge, subscription will end after billing period
    if (chargedThroughDate && canceledDate.getTime() === chargedThroughDate.getTime()) {
      return true;
    }

    // User canceled and cancel already processed
    if (canceledDate <= now) return true;

    // Otherwise, it's likely a system-initiated cancel or a pending cancel not yet processed
    return false;
  };

  // Add this function to check if a subscription has a scheduled pause
  const hasScheduledPause = (subscription: SquareSubscription): boolean => {
    const SubscriptionActions = subscription?.actions;

    if (!SubscriptionActions) return false;

    return SubscriptionActions.some(
      (action: SquareSubscriptionAction) =>
        action.type === 'PAUSE' &&
        action.effectiveDate &&
        new Date(action.effectiveDate) > getPacificTimeNow()
    );
  };
  /**
   * End Custom Methods
   */

  return subscriptions && subscriptions.length > 0 ? (
    <div className="space-y-4 ">
      {subscriptions.map(subscription => {
        // const isPending = subscription.status === 'PENDING';
        const isSubscriptionPaused = subscription.status === 'PAUSED';
        const isCanceled = isUserCanceled(subscription);

        return (
          <Card key={subscription.id} className="border p-4 rounded-lg shadow-sm overflow-hidden">
            <CardHeader className="p-0 pb-2">
              <div
                className={`flex items-center justify-between ${isHomePage && 'border-b pb-2 border-gray-100'}`}
              >
                {/* For active status message */}
                {/* {subscription.status === 'ACTIVE' && (
                  <div className="flex items-start gap-2">
                    <div className="text-sm text-green-800">
                      <p className="font-medium">
                        Fresh mulberries are on the move — every week, straight to your door!
                      </p>
                    </div>
                  </div>
                )} */}

                {isHomePage && <h2 className="text-base font-bold">My Subscription</h2>}
                {/* {!isHomePage && isPending && !isCanceled && (
                  <div className="flex items-start gap-2">
                    <div className="text-sm text-amber-800">
                      <p className="font-medium">
                        Your mulberry delivery is getting ready. First delivery on{' '}
                        {formatDate(subscription.chargedThroughDate ?? subscription.startDate)}.
                      </p>
                    </div>
                  </div>
                )} */}

                {/* {!isHomePage && isCanceled && (
                  <div className=" flex items-start gap-2">
                    <div className="text-sm text-orange-800">
                      <p className="font-medium">
                        You’ll get deliveries until{' '}
                        {formatDate(subscription.chargedThroughDate ?? subscription.startDate)} No
                        more after that.
                      </p>
                    </div>
                  </div>
                )} */}

                {/* Add paused notification banner */}
                {/* {!isHomePage && isSubscriptionPaused && (
                  <div className=" flex items-start gap-2">
                    <div className="text-sm text-amber-800">
                      <p className="font-medium">
                        No Mulberry Delivery right now — tap Resume when you’re craving again!
                      </p>
                    </div>
                  </div>
                )} */}

                <Badge
                  variant="secondary"
                  className={`${
                    isCanceled
                      ? 'bg-orange-100 text-orange-800'
                      : isSubscriptionPaused
                        ? 'bg-amber-100 text-amber-800'
                        : subscription.status === 'ACTIVE'
                          ? 'bg-green-100 text-green-800'
                          : subscription.status === 'PENDING'
                            ? 'bg-amber-100 text-amber-800'
                            : 'bg-gray-100 text-gray-800'
                  } ${isHomePage ? '' : 'ml-auto'}`}
                >
                  {isCanceled ? 'CANCELED' : isSubscriptionPaused ? 'PAUSED' : subscription.status}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="p-0">
              {/* Add a notification banner for scheduled pause */}
              {/* {!isCanceled && !isSubscriptionPaused && hasScheduledPause(subscription) && (
                <div className=" flex items-start gap-2">
                  <div className="text-sm text-amber-800">
                    <p className="font-medium">Your subscription is scheduled to pause</p>
                    <p className="mt-1">
                      Your next delivery will be skipped. The subscription will automatically resume
                      after one billing cycle.
                    </p>
                  </div>
                </div>
              )} */}

              <div className="space-y-1 flex justify-between items-start">
                <div className="flex flex-col mt-1">
                  {subscription.orderDetails && subscription.orderDetails.lineItems && (
                    <>
                      {subscription.orderDetails.lineItems.map((item, index: number) => {
                        // Check if this is a delivery fee item
                        const priceInDollars = item?.basePriceMoney?.amount
                          ? Number(item?.basePriceMoney?.amount) / 100
                          : 0;
                        return (
                          <div key={index} className="flex flex-col justify-between text-sm">
                            <p
                              className={` text-gray-600 ${item.name?.includes('Delivery Fee') ? 'font-normal' : 'font-semibold'}`}
                            >
                              {item.name?.includes('Delivery Fee') ? 'Weekly' : ''} {item.name} -{' '}
                              {item.name?.includes('Delivery Fee') ? '' : item.variationName}
                              {/* {item.quantity} */}
                              <strong className="text-black"> ${priceInDollars.toFixed(2)}</strong>
                            </p>
                            {/* {item.name?.includes('Delivery Fee') || (
                              <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                            )} */}
                          </div>
                        );
                      })}
                    </>
                  )}
                </div>
                <div>
                  {subscription?.catalogDetails?.type === 'SUBSCRIPTION_PLAN_VARIATION' && (
                    <div className="flex items-center gap-2 text-sm">
                      <span className="font-semibold text-gray-600 ">
                        Plan:{' '}
                        {subscription.catalogDetails.subscriptionPlanVariationData?.name ||
                          'Weekly'}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span className="">Start date: {formatDate(subscription.startDate)}</span>
                  </div>
                  {/* {subscription.chargedThroughDate && (
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <span>Next billing: {formatDate(subscription.chargedThroughDate)}</span>
                    </div>
                  )} */}
                </div>
              </div>

              {subscription.orderDetails && subscription.orderDetails.lineItems && (
                <div className="">
                  <div className="space-y-2">
                    <div className="w-full">
                      {/* Custom Accordion */}
                      <div className="w-full">
                        <div className="flex justify-end mt-3">
                          <Button
                            onClick={() => {
                              // Toggle this specific subscription in the Set
                              setOpenManageIds(prev => {
                                const newSet = new Set(prev);
                                if (subscription.id && newSet.has(subscription.id)) {
                                  newSet.delete(subscription.id);
                                } else if (subscription.id) {
                                  newSet.add(subscription.id);
                                }
                                return newSet;
                              });
                            }}
                            className="w-full md:w-auto bg-mulberry hover:bg-mulberryHover text-white"
                          >
                            Manage
                          </Button>
                        </div>

                        {subscription.id && openManageIds.has(subscription.id) && (
                          <div className="overflow-hidden text-sm">
                            <div className="pb-4 pt-0">
                              {/* Payment Method Information */}
                              <div className="mt-3 pt-3 border-t border-gray-100">
                                <div className="flex justify-between items-center">
                                  <h3 className="text-sm font-semibold mb-2 text-gray-600">
                                    Payment Method
                                  </h3>
                                  {!isCanceled && (
                                    <Button
                                      variant="link"
                                      size="sm"
                                      className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                                      onClick={() => {
                                        setSelectedSubscription(subscription);
                                        setIsUpdateCardDialogOpen(true);
                                      }}
                                    >
                                      <RefreshCw className="h-2 w-2" />
                                      Update Payment Method
                                    </Button>
                                  )}
                                </div>

                                <div className="flex justify-between items-center text-sm text-muted-foreground">
                                  <span>Card on file</span>
                                  <span>
                                    {subscription?.cardDetails?.cardBrand} ending{' '}
                                    {subscription?.cardDetails?.last4}
                                  </span>
                                </div>
                              </div>

                              <div className="mt-3 pt-3 border-t border-gray-100">
                                <h3 className="text-sm font-semibold mb-2 text-gray-600">
                                  Delivery Details
                                </h3>
                                <div className="pt-1 flex flex-col text-gray-500 text-sm">
                                  <span className="font-medium">
                                    {subscription?.orderDetails?.fulfillments?.[0]?.shipmentDetails
                                      ?.recipient?.displayName || 'Recipient'}
                                  </span>

                                  <div>
                                    {
                                      subscription?.orderDetails?.fulfillments?.[0]?.shipmentDetails
                                        ?.recipient?.address?.addressLine1
                                    }
                                    ,{' '}
                                    {
                                      subscription?.orderDetails?.fulfillments?.[0]?.shipmentDetails
                                        ?.recipient?.address?.locality
                                    }
                                    ,{' '}
                                    {
                                      subscription?.orderDetails?.fulfillments?.[0]?.shipmentDetails
                                        ?.recipient?.address?.administrativeDistrictLevel1
                                    }{' '}
                                    {
                                      subscription?.orderDetails?.fulfillments?.[0]?.shipmentDetails
                                        ?.recipient?.address?.postalCode
                                    }
                                  </div>
                                </div>
                              </div>

                              {/* Activity */}
                              <div className="mt-3 pt-3 border-t border-gray-100">
                                <h3 className="text-sm font-semibold mb-2 text-gray-600">
                                  Recent Invoices
                                </h3>
                                {subscription.invoices && subscription.invoices.length > 0 ? (
                                  <div className="space-y-3">
                                    {/* Sort invoices by date (newest first) and show up to 3 */}
                                    {[...subscription.invoices]
                                      .sort(
                                        (a, b) =>
                                          new Date(b.createdAt || 0).getTime() -
                                          new Date(a.createdAt || 0).getTime()
                                      )
                                      .slice(0, 3)
                                      .map((invoice, index) => (
                                        <div key={invoice.id} className="space-y-1">
                                          <div className="flex items-center text-sm text-gray-500">
                                            <span className="font-medium">
                                              Invoice #
                                              {invoice.invoiceNumber ||
                                                (invoice.id ?? '').substring(0, 6)}{' '}
                                              •{' '}
                                              {invoice.createdAt
                                                ? new Date(invoice.createdAt).toLocaleDateString(
                                                    'en-GB'
                                                  )
                                                : 'N/A'}{' '}
                                              •{' '}
                                              <span
                                                className={
                                                  invoice.status === 'PAID'
                                                    ? 'text-green-600'
                                                    : 'text-amber-600'
                                                }
                                              >
                                                {invoice.status}
                                              </span>
                                            </span>
                                          </div>
                                          {index <
                                            Math.min(subscription?.invoices?.length ?? 0, 3) -
                                              1 && (
                                            <div className="border-b border-gray-100 my-2"></div>
                                          )}
                                        </div>
                                      ))}

                                    {subscription.invoices.length > 3 && (
                                      <div className="text-center mt-2">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="text-xs text-muted-foreground hover:text-foreground"
                                          onClick={() => {
                                            /* Implement view all invoices functionality */
                                          }}
                                        >
                                          View all {subscription.invoices.length} invoices
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  <p className="text-sm text-muted-foreground">
                                    No invoice information available
                                  </p>
                                )}
                              </div>
                              {!isCanceled && (
                                <div className="mt-4 pt-3 border-t border-gray-100">
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      className="flex-1 hidden border-amber-300 bg-amber-50 text-amber-600 hover:bg-amber-100 hover:border-amber-400 font-medium"
                                      onClick={() => {
                                        setSelectedSubscription(subscription);
                                        setIsPauseDialogOpen(true);
                                      }}
                                      disabled={
                                        isPausing ||
                                        isSubscriptionPaused ||
                                        hasScheduledPause(subscription) ||
                                        subscription.status !== 'ACTIVE'
                                      }
                                      title={
                                        subscription.status !== 'ACTIVE'
                                          ? 'Only active subscriptions can be paused'
                                          : hasScheduledPause(subscription)
                                            ? 'This subscription is already scheduled to be paused'
                                            : ''
                                      }
                                    >
                                      <PauseCircle className="h-4 w-4 mr-2" />
                                      {isSubscriptionPaused
                                        ? 'Paused'
                                        : hasScheduledPause(subscription)
                                          ? 'Pause Scheduled'
                                          : 'Skip Next Delivery'}
                                    </Button>
                                    <Button
                                      variant="outline"
                                      className="   border-red-300 bg-red-50 text-red-600 hover:bg-red-100 hover:border-red-400 font-medium"
                                      onClick={() => {
                                        setSelectedSubscription(subscription);
                                        setIsDialogOpen(true);
                                      }}
                                      disabled={
                                        isCancelling || !isSubscriptionCancellationAllowed()
                                      }
                                      title={
                                        !isSubscriptionCancellationAllowed()
                                          ? 'Cancellation is not allowed after Saturday midnight'
                                          : ''
                                      }
                                    >
                                      <XCircle className="h-4 w-4 mr-2" />
                                      Cancel Subscription
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}

      {/* Confirmation Dialog - moved outside the map function */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel your subscription? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)} className="sm:flex-1">
              Keep Subscription
            </Button>
            <Button
              variant="secondary"
              onClick={handleCancelSubscription}
              disabled={isCancelling}
              className="sm:flex-1"
            >
              {isCancelling ? (
                <>
                  <span className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Cancelling...
                </>
              ) : (
                'Yes, Cancel Subscription'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Pause Confirmation Dialog */}
      <Dialog open={isPauseDialogOpen} onOpenChange={setIsPauseDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Skip Next Delivery</DialogTitle>
            <DialogDescription>
              Are you sure you want to skip your next delivery? You won&apos;t be charged for the
              next billing cycle and subscription will resume after this.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsPauseDialogOpen(false)}
              className="sm:flex-1"
            >
              Keep Active
            </Button>
            <Button
              variant="secondary"
              onClick={handlePauseSubscription}
              disabled={isPausing}
              className="sm:flex-1"
            >
              {isPausing ? (
                <>
                  <span className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                  Pausing...
                </>
              ) : (
                'Yes, Pause Subscription'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Update Card Dialog */}
      <Dialog open={isUpdateCardDialogOpen} onOpenChange={setIsUpdateCardDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Payment Method</DialogTitle>
            <DialogDescription>
              Enter your new card details to update your payment method.
            </DialogDescription>
          </DialogHeader>
          <div className="mb-4">
            <label htmlFor="updateCardholderName" className="block text-sm font-medium mb-1">
              Cardholder Name
            </label>
            <input
              type="text"
              id="cardholderName"
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Name as it appears on card"
              defaultValue={`${user?.first_name} ${user?.last_name}`}
            />
          </div>
          <PaymentForm
            applicationId={process.env.NEXT_PUBLIC_SQUARE_APP_ID || ''}
            locationId={process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID || ''}
            cardTokenizeResponseReceived={handleUpdateCard}
            createVerificationDetails={() => {
              // Get cardholder name from input field
              const cardholderNameInput = document.getElementById(
                'cardholderName'
              ) as HTMLInputElement;
              const cardholderName = cardholderNameInput?.value || '';

              // Split the cardholder name into first and last name
              const nameParts = cardholderName.trim().split(' ');
              const firstName = nameParts[0] || '';
              const lastName = nameParts.slice(1).join(' ') || '';

              return {
                billingContact: {
                  familyName: lastName ?? user?.last_name,
                  givenName: user?.first_name,
                  email: firstName || user?.email,
                },
                intent: 'STORE',
              };
            }}
          >
            <CreditCard
              buttonProps={{
                css: {
                  "[data-theme='dark'] &": {
                    backgroundColor: '#892b76',
                    '&:hover': {
                      backgroundColor: '#892b76',
                    },
                  },
                  backgroundColor: '#892b76',
                  '&:hover': {
                    backgroundColor: '#892b76',
                  },
                },
              }}
            >
              Update
            </CreditCard>
          </PaymentForm>
          <div className="mt-4 text-xs text-gray-600">
            By subscribing, I authorize Very Mulberry (Habitera Farms LLC) to save my card securely
            with Square and automatically charge this card until the subscription ends or I cancel
            this authorization.
          </div>
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setIsUpdateCardDialogOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  ) : (
    <Card className="mt-6 border rounded-lg shadow-sm overflow-hidden">
      <CardHeader className="pb-2">
        <h2 className="text-base font-bold ">My Subscriptions</h2>
      </CardHeader>
      <CardContent className="px-5">
        <div className="space-y-1">
          <p className="text-mulberry text-sm font-semibold">
            Hey {user?.first_name || 'there'}, you don&apos;t have any Very Mulberry subscriptions
          </p>
          <p className="text-gray-600 text-sm">
            See if you can get our fresh mulberries delivered to your doorstep on the day they were
            picked.
          </p>
        </div>
        <div className="pt-4">
          <Link href="/subscription">
            <Button className="bg-black hover:bg-mulberry text-white px-3 rounded">
              Subscribe Now
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubscriptionsCard;
