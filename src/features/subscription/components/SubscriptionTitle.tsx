'use client';

import React from 'react';

import { isGiftHomeDelivery } from '@/src/lib/utils';
import { useAppSelector } from '@/src/store';

import { selectSubscriptionDetails } from '../slices/subscriptionDetailsSlice';

/**
 * Reusable title component for subscription pages
 * Displays the main title and subtitle with consistent styling
 */
export default function SubscriptionTitle({ orderFor }: { orderFor?: string | undefined }) {
  const subscriptionDetails = useAppSelector(selectSubscriptionDetails);

  const isGiftOrder =
    isGiftHomeDelivery(subscriptionDetails.orderFor) || isGiftHomeDelivery(orderFor);

  return (
    <div className="mb-4 w-full">
      <h2 className="text-mulberry text-xl lg:text-3xl font-semibold text-center">
        Very Mulberry Home Delivery{isGiftOrder ? ' - Gift' : ''}
      </h2>
    </div>
  );
}
