import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React from 'react';

import { Button } from '@/src/components/ui/button';
import { ROUTES } from '@/src/lib/constants';

import SupportSection from './SupportSection';

interface DeliveryZoneUnavailableDialogProps {
  handleResetForm: () => void;
}

const DeliveryZoneUnavailableDialog: React.FC<DeliveryZoneUnavailableDialogProps> = ({
  handleResetForm,
}) => {
  const router = useRouter();

  return (
    <div className="h-full bg-white p-8 rounded-xl shadow-sm border border-gray-100">
      <h2 className="text-xl md:text-3xl text-mulberry font-bold text-center mb-3">
        Sorry, we’re not delivering to your area
      </h2>
      <p className="mb-4 text-center text-gray-700">
        It looks like your address is outside our current delivery zones. But good news: you can
        still get your hands on our delicious mulberries when you&apos;re near the Bay Area!
      </p>

      <div className="mb-4 text-center">
        <p className="text-center font-bold text-gray-700 mb-1">Join us for a U-Pick Experience</p>
        <p className="text-center text-gray-700">
          Come visit Very Mulberry in Brentwood, CA, where you can pick your own fresh mulberries
          straight from the tree. It&apos;s a one-of-a-kind farm experience for family and friends!
        </p>
        <Button
          onClick={() => router.push(`${ROUTES.SCHEDULE_TABS}u-pick`)}
          className="mt-2 py-3 px-4 bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 font-medium"
        >
          Book Your U-Pick
        </Button>
      </div>
      <div className="mb-4 text-center">
        <p className="text-center font-bold text-gray-700 mb-1">
          Or find us at a Bay Area Farmers&apos; Market
        </p>
        <p className="text-center text-gray-700">
          We also bring pre-picked mulberries to select Bay Area farmers&apos; markets each week.
        </p>

        <Link
          href={`${ROUTES.SCHEDULE_TABS}farmers-market`}
          className="font-semibold text-mulberry underline"
        >
          See Farmer&apos;s Market Schedule
        </Link>
      </div>

      <div className="space-y-4 max-w-sm mx-auto">
        <Button
          onClick={handleResetForm}
          className="w-full py-3 px-4 bg-white border border-mulberry text-mulberry hover:bg-gray-100 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 font-medium"
        >
          Try another address
        </Button>

        <SupportSection />
      </div>
    </div>
  );
};

export default DeliveryZoneUnavailableDialog;
