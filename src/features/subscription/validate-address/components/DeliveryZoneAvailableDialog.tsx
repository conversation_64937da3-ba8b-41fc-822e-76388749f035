import { CheckCircle } from 'lucide-react';
import React from 'react';

import { Button } from '@/src/components/ui/button';
import { formatDeliveryDays } from '@/src/lib/utils';

interface DeliveryZoneAvailableDialogProps {
  deliveryDays: string[];
  handleContinue: () => void;
}

const DeliveryZoneAvailableDialog: React.FC<DeliveryZoneAvailableDialogProps> = ({
  deliveryDays,
  handleContinue,
}) => {
  return (
    <div className="h-full bg-white p-8 rounded-xl shadow-sm border border-gray-100">
      <div className="flex items-center rounded-lg justify-center mb-3 text-white bg-mulberry p-2">
        <CheckCircle className="mr-2 font-semibold" size={26} />
        <p className="text-2xl font-semibold">We deliver to your home!</p>
      </div>

      <div className="p-2 max-w-lg mx-auto text-xl">
        {deliveryDays.length > 0 ? (
          <>
            <p className="mb-3 text-center">We deliver to your neighborhood on</p>

            <p className="text-3xl font-bold text-mulberry mb-3 text-center">
              {formatDeliveryDays(deliveryDays)}
            </p>

            <p className="mb-4">
              and mulberries will be lovingly delivered to your doorstep every{' '}
              {formatDeliveryDays(deliveryDays)} during our season.
            </p>
          </>
        ) : (
          <p className="mb-3 text-center">
            We deliver mulberries to your neighborhood and mulberries will be lovingly delivered to
            your doorstep during our season.
          </p>
        )}

        <Button
          onClick={handleContinue}
          className="w-full text-xl mt-4 bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive"
        >
          Start my home delivery order
        </Button>
      </div>
    </div>
  );
};

export default DeliveryZoneAvailableDialog;
