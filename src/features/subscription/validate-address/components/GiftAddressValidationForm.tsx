'use client';

import { MailI<PERSON>, MapPin } from 'lucide-react';
import { useState } from 'react';
import ReactGoogleAutocomplete from 'react-google-autocomplete';
import { UseFormReturn } from 'react-hook-form';

import { Button } from '@/src/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/src/components/ui/form';
import { Input } from '@/src/components/ui/input';
import { selectGiftAddressValidationLoading } from '@/src/features/subscription/slices/addressValidationSlice';
import { BUTTON_TEXTS, SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';
import { useAppSelector } from '@/src/store';
import { SubscriptionOrderType } from '@/src/types/Subscriptions';

// Define AddressFormValues type
type AddressFormValues = {
  address: string;
  email?: string; // Add email field as optional
};

interface AddressData {
  fullAddress: string;
  addressLine1: string;
  city: string;
  state: string;
  zipCode: string;
  placeData: any;
}

interface Place {
  formatted_address: string;
  [key: string]: any; // To handle additional properties from the Google Places API
}

interface AddressValidationFormProps {
  form: UseFormReturn<AddressFormValues>;
  onSubmit: (
    data: AddressFormValues,
    addressData: AddressData | null,
    orderFor: SubscriptionOrderType
  ) => Promise<void>;
}

export default function GiftAddressValidationForm({ form, onSubmit }: AddressValidationFormProps) {
  /**
   * Start Initials
   */
  const [selectedAddressData, setSelectedAddressData] = useState<AddressData | null>(null);
  const isLoading = useAppSelector(selectGiftAddressValidationLoading);
  /**
   * End Initials
   */

  /**
   * Start custom methods
   */
  const handleSubmit = async (data: AddressFormValues) => {
    await onSubmit(data, selectedAddressData, SUBSCRIPTION_ORDER_TYPES.ONE_TIME_GIFT);
  };

  const handleAddressSelect = (place: Place): void => {
    // Extract address components
    let addressLine1 = '';
    let city = '';
    let state = '';
    let zipCode = '';

    for (const component of place.address_components) {
      const types = component.types;

      if (types.includes('street_number') || types.includes('route')) {
        addressLine1 += addressLine1 ? ' ' + component.long_name : component.long_name;
      } else if (types.includes('locality')) {
        city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        state = component.short_name;
      } else if (types.includes('postal_code')) {
        zipCode = component.short_name;
      }
    }

    // If addressLine1 is empty, use the full address as fallback
    if (!addressLine1) {
      addressLine1 = place.formatted_address || '';
    }
    setSelectedAddressData({
      fullAddress: place.formatted_address || '',
      addressLine1,
      city,
      state,
      zipCode,
      placeData: place,
    });
  };
  /**
   * End custom methods
   */

  return (
    <div
      className="h-full bg-white md:p-8 p-4 rounded-xl shadow-sm border border-gray-100"
      id="gift-validate-address-form"
    >
      <h2 className="text-xl md:text-3xl text-mulberry font-bold text-center mb-3">
        Send a Gift of Home Delivery
      </h2>
      <p className="text-center text-gray-700 mb-4">to Family, Friends, and Neighbors</p>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 max-w-sm mx-auto">
          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-mulberry/50" />
                    <ReactGoogleAutocomplete
                      apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}
                      onPlaceSelected={place => {
                        const formattedAddress = place.formatted_address;
                        field.onChange(formattedAddress);
                        handleAddressSelect(place);
                      }}
                      defaultValue={field.value}
                      placeholder="123 Main St, Sunnyvale, CA"
                      options={{
                        types: ['address'],
                        componentRestrictions: { country: 'us' },
                      }}
                      style={{
                        fontSize: '0.9rem',
                        border: '1px solid #e2e8f0', // Same as Input border
                      }}
                      className="placeholder-gray-500 outline-none w-full bg-white py-3.5 pl-10 rounded-xl shadow-sm"
                      autoComplete="off"
                      autoCorrect="off"
                      spellCheck="false"
                      data-1p-ignore="true"
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="relative">
                    <MailIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-mulberry/50" />
                    <Input
                      placeholder="Your Email Address (optional)"
                      {...field}
                      className="pl-10 bg-white py-6 rounded-xl shadow-sm"
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <p className="text-xs text-left text-gray-500 mb-6">
            In the event your zone is not available, we will reach out to you by email the moment it
            is enabled.
          </p>

          <Button
            type="submit"
            className="w-full text-wrap md:py-5 py-7 bg-mulberry text-sm hover:bg-mulberryHover rounded-xl shadow-sm hover:shadow-lg transition-all duration-300"
            disabled={isLoading}
          >
            {isLoading ? BUTTON_TEXTS.CHECKING : 'Check Recipient Address for GIFT Home Delivery'}
          </Button>
          <p className="text-xs text-center text-gray-500 mt-6">
            Provide the recipient&apos;s name, email address, delivery address, and a personalized
            gift message.
          </p>
        </form>
      </Form>
    </div>
  );
}
