'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { UAParser } from 'ua-parser-js';
import { z } from 'zod';

import {
  setShowAvailableDialog,
  resetAllDialogs,
  selectShowUnavailableDialog,
  selectShowAvailableDialog,
  selectShowZoneRequestDialog,
  selectDeliveryDays,
  selectZoneId,
  selectDeliveryWindow,
  selectOrderFor,
  validateAddress,
  selectEnteredEmail,
} from '@/src/features/subscription/slices/addressValidationSlice';
import {
  resetSubscriptionDetails,
  setSubscriptionDetails,
} from '@/src/features/subscription/slices/subscriptionDetailsSlice';
import {
  ROUTES,
  VALIDATION_ERROR_MESSAGES,
  SUBSCRIPTION_ORDER_TYPES,
  ERROR_MESSAGES,
} from '@/src/lib/constants';
import { useAppDispatch, useAppSelector } from '@/src/store';
import { SubscriptionOrderType } from '@/src/types/Subscriptions';

import {
  clearCustomerData,
  clearRecipientCustomerData,
  setCustomerDetails,
} from '../slices/subscriptionCustomerSlice';
import { resetPaymentState } from '../slices/subscriptionPaymentSlice';

import AddressValidationForm from './components/AddressValidationForm';
import DeliveryInformation from './components/DeliveryInformation';
import DeliveryZoneAvailableDialog from './components/DeliveryZoneAvailableDialog';
import DeliveryZoneRequestDialog from './components/DeliveryZoneRequestDialog';
import DeliveryZoneUnavailableDialog from './components/DeliveryZoneUnavailableDialog';
import GiftAddressValidationForm from './components/GiftAddressValidationForm';
import GiftDeliveryZoneUnavailableDialog from './components/GiftDeliveryZoneUnavailableDialog';

interface AddressData {
  fullAddress: string;
  addressLine1: string;
  city: string;
  state: string;
  zipCode: string;
  placeData: any;
}

// Zod Schema for Form Validation
const addressSchema = z.object({
  address: z.string().min(5, VALIDATION_ERROR_MESSAGES.ADDRESS_MIN),
  email: z
    .string()
    .email({ message: 'Please enter a valid email address' })
    .optional()
    .or(z.literal('')),
});

type AddressFormValues = z.infer<typeof addressSchema>;

export default function ValidateAddress() {
  /**
   * Start Initials
   */
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();

  const [selectedAddressData, setSelectedAddressData] = useState<AddressData | null>(null);

  const showUnavailableDialog = useAppSelector(selectShowUnavailableDialog);
  const showAvailableDialog = useAppSelector(selectShowAvailableDialog);
  const showZoneRequestDialog = useAppSelector(selectShowZoneRequestDialog);
  const deliveryDays = useAppSelector(selectDeliveryDays);
  const zoneId = useAppSelector(selectZoneId);
  const deliveryWindow = useAppSelector(selectDeliveryWindow);
  const orderFor = useAppSelector(selectOrderFor);
  const enteredEmail = useAppSelector(selectEnteredEmail);
  const { user } = useAppSelector((state: any) => state.authentication);

  // Form initialization with Zod validation
  const form = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      address: '',
      email: user?.email || '',
    },
  });
  const giftForm = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      address: '',
      email: user?.email || '',
    },
  });
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Add this useEffect to handle automatic redirect when no delivery days
  useEffect(() => {
    if (showAvailableDialog && deliveryDays.length === 0) {
      handleContinue();
    }
  }, [showAvailableDialog, deliveryDays]);

  // Reset available dialog when component mounts
  useEffect(() => {
    dispatch(setShowAvailableDialog(false));
  }, []);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const handleContinue = () => {
    dispatch(resetSubscriptionDetails());
    dispatch(resetPaymentState());
    dispatch(clearCustomerData());
    dispatch(clearRecipientCustomerData());
    dispatch(setShowAvailableDialog(false));

    if (!selectedAddressData) return;

    dispatch(setCustomerDetails({ ...{ email: enteredEmail || '' } }));

    // Store subscription details in Redux
    dispatch(
      setSubscriptionDetails({
        address: selectedAddressData.fullAddress,
        addressLine1: selectedAddressData.addressLine1,
        city: selectedAddressData.city,
        state: selectedAddressData.state,
        zipCode: selectedAddressData.zipCode,
        zoneId: zoneId,
        deliveryDays,
        deliveryWindow,
        orderFor: orderFor,
      })
    );

    // Navigate to subscription page
    router.push(ROUTES.SUBSCRIPTION_PRODUCT);
  };

  // Update the handleResetForm function to be more thorough
  const handleResetForm = () => {
    // Reset all dialogs in the Redux store
    dispatch(resetAllDialogs());

    // Reset both form values
    form.reset({ address: '' });
    giftForm.reset({ address: '' });

    // Reset the selectedAddressData state
    setSelectedAddressData(null);
  };

  const handleAddressSubmit = async (
    data: AddressFormValues,
    addressData: AddressData | null,
    orderFor: SubscriptionOrderType
  ) => {
    try {
      // Validate that we have a zip code before proceeding
      if (!addressData?.zipCode) {
        toast.error(VALIDATION_ERROR_MESSAGES.ZIP_CODE_REQUIRED);
        handleResetForm();
        return;
      }

      setSelectedAddressData(addressData);

      const userAgentInfo = getUserAgentInfo();

      // Create validation payload
      const validationPayload: {
        address: string;
        zipCode: string;
        email?: string;
        orderFor: SubscriptionOrderType;
        userAgentInfo: any;
      } = {
        address: data.address,
        zipCode: addressData.zipCode,
        orderFor: orderFor,
        userAgentInfo,
      };

      // Only include email if it's provided
      if (data.email) {
        validationPayload.email = data.email;
      }

      await dispatch(validateAddress(validationPayload))
        .unwrap()
        .then(res => {
          if (res.status !== 'active') {
            addQueryParam(res.status);
          }
        });
    } catch (error) {
      console.error('Error validating address:', error);
      toast.error(ERROR_MESSAGES.GENERIC_ERROR);
    }
  };

  const addQueryParam = (val: string) => {
    const currentQuery = searchParams.toString(); // Get current query params as string
    const newParams = new URLSearchParams(currentQuery); // Convert to URLSearchParams
    newParams.set('address', val); // Add the new query param

    // Update the URL with new params
    window.history.pushState(null, '', `${window.location.pathname}?${newParams.toString()}`);
  };

  const getUserAgentInfo = () => {
    const parser = new UAParser();
    const uaResult = parser.getResult();

    return {
      browser: {
        name: uaResult.browser.name,
        version: uaResult.browser.version,
      },
      device: {
        model: uaResult.device.model || '',
        type: uaResult.device.type || '',
        vendor: uaResult.device.vendor || '',
      },
      os: {
        name: uaResult.os.name || '',
        version: uaResult.os.version || '',
      },
      userAgent: navigator.userAgent,
      currentUrl: window.location.href, // Includes full URL with query parameters
    };
  };
  /**
   * End Custom Methods
   */

  return (
    <div className="container mx-auto px-4 lg:px-0 relative overflow-hidden" id="validate-address">
      <div className="flex flex-col md:grid md:grid-cols-2 gap-6 md:gap-8 md:items-start">
        {/* Column 1: Address Form or Success/Error Message */}
        <div className="h-full order-2 lg:order-1">
          {showAvailableDialog ? (
            <DeliveryZoneAvailableDialog
              deliveryDays={deliveryDays}
              handleContinue={handleContinue}
            />
          ) : showZoneRequestDialog || showUnavailableDialog ? (
            orderFor === SUBSCRIPTION_ORDER_TYPES.SELF ||
            orderFor === SUBSCRIPTION_ORDER_TYPES.ONE_TIME ? (
              showZoneRequestDialog ? (
                <DeliveryZoneRequestDialog handleResetForm={handleResetForm} />
              ) : (
                <DeliveryZoneUnavailableDialog handleResetForm={handleResetForm} />
              )
            ) : (
              <GiftDeliveryZoneUnavailableDialog handleResetForm={handleResetForm} />
            )
          ) : (
            <div>
              <AddressValidationForm form={form} onSubmit={handleAddressSubmit} />
              <p className="text-center my-1 font-bold">Or</p>
              <GiftAddressValidationForm form={giftForm} onSubmit={handleAddressSubmit} />
            </div>
          )}
        </div>

        {/* Column 2: Delivery Information */}
        <DeliveryInformation />
      </div>
    </div>
  );
}
