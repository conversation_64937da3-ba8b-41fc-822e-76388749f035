'use client';

import { format, startOfToday, isAfter, parseISO } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Calendar } from '@/src/components/ui/calendar';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/src/components/ui/card';
import { BUTTON_TEXTS, ERROR_MESSAGES, ROUTES } from '@/src/lib/constants';
import { convertToPacificTime } from '@/src/lib/timezone';
import { formatDeliveryDays } from '@/src/lib/utils';
import { AppDispatch } from '@/src/store';

import SubscriptionStepper from '../components/SubscriptionStepper';
import SubscriptionTitle from '../components/SubscriptionTitle';
import { selectZoneId } from '../slices/addressValidationSlice';
import {
  fetchDeliveryZone,
  selectDeliveryDays,
  selectDeliveryZone,
  selectDeliveryZoneError,
  selectDeliveryZoneLoading,
} from '../slices/deliveryZoneSlice';
import {
  selectSubscriptionDetails,
  setSubscriptionDetails,
} from '../slices/subscriptionDetailsSlice';

/**
 * SelectFirstDeliveryDate Component
 *
 * Allows customers to select their first delivery date based on the available
 * delivery days for their zone. Only dates that match the delivery days
 * from the delivery zone can be selected.
 */
export default function SelectFirstDeliveryDate() {
  /**
   * Start Initials
   */
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  // Get subscription details from Redux store
  const subscriptionDetails = useSelector(selectSubscriptionDetails);

  // Get delivery zone data from Redux
  const loading = useSelector(selectDeliveryZoneLoading);
  const error = useSelector(selectDeliveryZoneError);
  const zone = useSelector(selectDeliveryZone);
  const zoneId = useSelector(selectZoneId);
  const deliveryDays = useSelector(selectDeliveryDays);

  // Initialize with saved date from Redux if available, otherwise undefined
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    subscriptionDetails.startDate ? parseISO(subscriptionDetails.startDate) : undefined
  );
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Check if required parameters are missing from Redux store and redirect if needed
  useEffect(() => {
    if (!subscriptionDetails.variationId) {
      router.push(ROUTES.SUBSCRIPTION_PRODUCT);
    } else if (!zoneId) {
      router.push(ROUTES.SUBSCRIPTION);
    }
  }, [subscriptionDetails, zoneId]);

  // Fetch delivery zone data
  useEffect(() => {
    if (zoneId) {
      dispatch(fetchDeliveryZone(zoneId));
    }
  }, [zoneId]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Set the first available delivery date after zone data is loaded,
   * but only if no date is already selected
   */
  useEffect(() => {
    // Only find first available date if no date is currently selected
    if (zone && deliveryDays.length > 0 && !selectedDate) {
      // Find the first available date
      const today = convertToPacificTime(startOfToday());
      let foundDate = false;

      // Check up to 60 days in the future to find a valid delivery date
      for (let i = 0; i < 60 && !foundDate; i++) {
        const nextDate = new Date(today);
        nextDate.setDate(today.getDate() + i);

        // Check if this date is valid according to our rules
        if (!isDateDisabled(nextDate)) {
          setSelectedDate(nextDate);
          foundDate = true;
        }
      }
    }
  }, [zone, deliveryDays, selectedDate]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  // Handle date selection - this is the only date the customer can select
  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
  };

  // Handle continue button click
  const handleContinue = () => {
    if (!selectedDate) {
      toast.error(ERROR_MESSAGES.SELECT_START_DATE_FOR_YOUR_SUBSCRIPTION);
      return;
    }

    // Format the selected date
    const formattedDate = format(selectedDate, 'yyyy-MM-dd');

    dispatch(
      setSubscriptionDetails({
        startDate: formattedDate,
      })
    );

    // Make sure router is available before using it
    router.push(ROUTES.SUBSCRIPTION_USER_INFO);
  };

  // Check if a date should be disabled in the calendar
  const isDateDisabled = (date: Date) => {
    // Disable dates in the past
    const today = convertToPacificTime(startOfToday());
    if (!isAfter(date, today)) return true;

    // Check if date is after the zone's end date (if specified)
    if (zone?.endDate) {
      const endDate = parseISO(zone.endDate);
      if (isAfter(date, endDate)) return true;
    }

    // Get the day name (lowercase) for the date
    const dayName = format(date, 'EEEE').toLowerCase();

    // Check if the day name is in the delivery days array
    const isDayAvailable = deliveryDays.map(day => day.toLowerCase()).includes(dayName);
    if (!isDayAvailable) return true;

    // Check if it's Saturday or later and the date is in the next week
    const currentDay = format(today, 'EEEE').toLowerCase();
    const isWeekend = currentDay === 'saturday' || currentDay === 'sunday';

    // Calculate the start of next week (Sunday)
    const nextWeekStart = new Date(today);
    nextWeekStart.setDate(today.getDate() + ((7 - today.getDay()) % 7));

    // Calculate the end of next week (Saturday)
    const nextWeekEnd = new Date(nextWeekStart);
    nextWeekEnd.setDate(nextWeekStart.getDate() + 6);

    // If it's Saturday or Sunday, disable dates in the next week
    if (isWeekend && isAfter(date, nextWeekStart) && !isAfter(date, nextWeekEnd)) {
      return true;
    }

    return false;
  };
  /**
   * End Custom Methods
   */

  if (loading) return <Spinner />;

  if (error) {
    return (
      <Card className="max-w-md mx-auto mt-8">
        <CardHeader>
          <CardTitle className="text-red-600">Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <SubscriptionTitle />

      {/* Move stepper to top of page */}
      <div className="max-w-5xl mx-auto mt-8">
        <SubscriptionStepper currentStep={3} />
      </div>

      {/* Change grid to single column */}
      <div className="max-w-3xl mx-auto md:mb-6 mb-16">
        <Card>
          <CardHeader>
            <CardTitle>Select Your First Delivery Date</CardTitle>
            <CardDescription>
              {deliveryDays.length > 0 ? (
                <>
                  Your area receives deliveries each {formatDeliveryDays(deliveryDays)}.
                  {zone?.startDate && zone?.endDate && (
                    <div className="mt-2 text-sm text-gray-500">
                      Delivery available from {format(parseISO(zone.startDate), 'MMM d, yyyy')} to{' '}
                      {format(parseISO(zone.endDate), 'MMM d, yyyy')}
                    </div>
                  )}
                  <div className="mt-2 text-sm font-medium">
                    All future delivery dates will be automatically scheduled based on your start
                    date.
                  </div>
                </>
              ) : (
                <>No delivery days available for your area.</>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="max-w-xl mx-auto">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              disabled={isDateDisabled}
              className="rounded-md border w-full mx-auto"
            />
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleContinue}
              disabled={!selectedDate}
              className="w-full bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive"
            >
              {BUTTON_TEXTS.CONTINUE}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </>
  );
}
