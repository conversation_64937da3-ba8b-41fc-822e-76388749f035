'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { CustomPhoneInput } from '@/src/components/phone-input';
import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/src/components/ui/form';
import { Input } from '@/src/components/ui/input';
import { Textarea } from '@/src/components/ui/textarea';
import { ROUTES, VALIDATION_ERROR_MESSAGES } from '@/src/lib/constants';
import { isGiftHomeDelivery } from '@/src/lib/utils';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

import SubscriptionStepper from '../components/SubscriptionStepper';
import SubscriptionTitle from '../components/SubscriptionTitle';
import { updateMissingAddressLog } from '../slices/addressValidationSlice';
import {
  getOrCreateCustomer,
  selectCustomerLoading,
  selectCustomerError,
  selectSquareCustomerId,
  setCustomerDetails,
  selectShouldRedirect,
  enableRedirect,
  selectCustomerDetails,
  setCustomerLoading, // Add this selector
} from '../slices/subscriptionCustomerSlice';
import { selectSubscriptionDetails } from '../slices/subscriptionDetailsSlice';

function SubscriptionUserInfo() {
  /**
   * Start Initials
   */
  const router = useRouter();
  const dispatch = useAppDispatch();

  // Get customer state from Redux
  const squareCustomerId = useAppSelector(selectSquareCustomerId);
  const customerLoading = useAppSelector(selectCustomerLoading);
  const customerError = useAppSelector(selectCustomerError);
  const { user } = useAppSelector((state: RootState) => state.authentication);
  const shouldRedirect = useAppSelector(selectShouldRedirect);
  const customerDetails = useAppSelector(selectCustomerDetails);

  const subscriptionDetails = useAppSelector(selectSubscriptionDetails);

  const isGiftOrder = isGiftHomeDelivery(subscriptionDetails.orderFor);

  // Initialize form with react-hook-form and zod validation
  // Define form schema with zod
  const userInfoSchema = z
    .object({
      email: z.string().email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID),
      confirmEmail: z.string().email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID),
      firstName: z.string().min(2, VALIDATION_ERROR_MESSAGES.FIRST_NAME_MIN),
      lastName: z.string().min(2, VALIDATION_ERROR_MESSAGES.LAST_NAME_MIN),
      phone: !isGiftOrder
        ? z.string().min(1, VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER)
        : z.string().optional(),
      address: !isGiftOrder
        ? z.string().min(5, VALIDATION_ERROR_MESSAGES.ADDRESS_IS_REQUIRED)
        : z.string().optional(),
      addressLine2: z.string().optional(),
      deliveryNotes: z.string().optional(),
    })
    .refine(data => data.email === data.confirmEmail, {
      message: 'Email addresses must match',
      path: ['confirmEmail'],
    });

  type UserInfoFormValues = z.infer<typeof userInfoSchema>;

  const form = useForm<UserInfoFormValues>({
    resolver: zodResolver(userInfoSchema),
    defaultValues: {
      // First try to use customer details from Redux
      email: customerDetails?.email || (user && user.email) || '',
      confirmEmail: '',
      firstName: customerDetails?.firstName || (user && user.first_name) || '',
      lastName: customerDetails?.lastName || (user && user.last_name) || '',
      ...(!isGiftOrder && {
        phone: customerDetails?.phone || '',
        addressLine2: customerDetails?.addressLine2 || '',
        deliveryNotes: customerDetails?.deliveryNotes || '',

        // For address fields, still use subscription details
        address: subscriptionDetails?.address,
      }),
    },
  });
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */

  // Check if product details are missing and redirect if needed
  useEffect(() => {
    if (!subscriptionDetails.variationId) {
      router.push(ROUTES.SUBSCRIPTION_PRODUCT);
    } else if (!subscriptionDetails.zoneId) {
      router.push(ROUTES.SUBSCRIPTION);
    }
  }, [subscriptionDetails]);

  // Redirect to payment page if customer ID is already created
  useEffect(() => {
    // Only redirect if we have a customer ID AND shouldRedirect is true
    if (squareCustomerId && shouldRedirect && !isGiftOrder) {
      router.push(ROUTES.SUBSCRIPTION_PAYMENT);
      dispatch(setCustomerLoading(false));
    } else if (squareCustomerId && shouldRedirect && isGiftOrder) {
      dispatch(enableRedirect());
      router.push(ROUTES.SUBSCRIPTION_GIFT_DETAILS);
      dispatch(setCustomerLoading(false));
    }
  }, [squareCustomerId, shouldRedirect, isGiftOrder]);

  // Handle customer error
  useEffect(() => {
    if (customerError) {
      dispatch(setCustomerLoading(false));
      toast.error(`Customer error: ${customerError}`);
    }
  }, [customerError]);

  // Add an effect to update form values when customer details change
  useEffect(() => {
    if (customerDetails?.firstName) {
      form.setValue('email', customerDetails.email || '');
      form.setValue('firstName', customerDetails.firstName || '');
      form.setValue('lastName', customerDetails.lastName || '');
      form.setValue('phone', customerDetails.phone || '');
    } else if (user) {
      // Fall back to user data if no customer details
      form.setValue('email', user.email || '');
      form.setValue('firstName', user.first_name || '');
      form.setValue('lastName', user.last_name || '');
      form.setValue('phone', user.phone || '');
    }
  }, [customerDetails, user, form]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const onSubmit = (data: UserInfoFormValues) => {
    const customerData = {
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      ...(!isGiftOrder && {
        phone: data.phone || '',
        addressLine2: data.addressLine2 || '',
        addressLine1: subscriptionDetails?.addressLine1 || '',
        city: subscriptionDetails?.city || '',
        state: subscriptionDetails?.state || '',
        zipCode: subscriptionDetails?.zipCode || '',
        deliveryNotes: data.deliveryNotes || '',
      }),
    };
    // Store user info in Redux store for payment page
    dispatch(setCustomerDetails(customerData));

    dispatch(
      updateMissingAddressLog({
        data: {
          customer: customerData,
        },
      })
    );

    // Re-enable redirect before creating/getting customer
    dispatch(enableRedirect());

    // Create customer in Square with all details
    dispatch(
      getOrCreateCustomer({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        ...(!isGiftOrder && {
          phone: data.phone || '',
          addressLine2: data.addressLine2 || '',
          addressLine1: subscriptionDetails?.addressLine1 || '',
          city: subscriptionDetails?.city || '',
          state: subscriptionDetails?.state || '',
          zipCode: subscriptionDetails?.zipCode || '',
        }),
      })
    );
    dispatch(setCustomerLoading(true));
  };
  /**
   * End Custom Methods
   */

  if (customerLoading) {
    return <Spinner />;
  }

  return (
    <>
      <SubscriptionTitle />

      {/* Move stepper to top of page */}
      <div className="max-w-5xl mx-auto mt-8">
        <SubscriptionStepper currentStep={subscriptionDetails.deliveryDays?.length ? 4 : 3} />
      </div>

      {/* Change grid to single column */}
      <div className="max-w-3xl mx-auto md:mb-6 mb-16">
        <Card>
          <CardContent className="p-2 md:p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email *</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Email *</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Only show these fields if NOT a gift order */}
                {!isGiftOrder && (
                  <>
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number *</FormLabel>
                          <FormControl>
                            <CustomPhoneInput
                              value={field.value || ''}
                              onChange={value => field.onChange(value)}
                              className="w-full"
                              country="us"
                              disabled={false}
                              disableDropdown={true}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Address *</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={subscriptionDetails?.address || ''}
                              disabled
                              className="bg-gray-100"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="addressLine2"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Apartment/Suite</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Apt, Suite, Floor, etc." />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="deliveryNotes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Delivery Notes (optional — include anything the driver should know: gate
                            codes, access tips, etc.)
                          </FormLabel>
                          <FormControl>
                            <Textarea {...field} className="min-h-[50px]" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                <Button
                  type="submit"
                  className="w-full bg-mulberry text-white hover:bg-mulberryHover"
                >
                  {!isGiftOrder ? 'Continue to Payment' : 'Continue'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </>
  );
}

export default SubscriptionUserInfo;
