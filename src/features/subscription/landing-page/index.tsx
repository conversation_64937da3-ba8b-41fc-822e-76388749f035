'use client';

import { useSearchParams } from 'next/navigation';
import React, { useRef, useEffect } from 'react';

import HeroSection from '@/src/features/subscription/landing-page/components/HeroSection';
import HowItWorks from '@/src/features/subscription/landing-page/components/HowItWork';
import WhySubscribe from '@/src/features/subscription/landing-page/components/WhySubscribe';
import {
  setReferralCode,
  setUrlDiscountCode,
} from '@/src/features/subscription/slices/discountValidationSlice';
import ValidateAddress from '@/src/features/subscription/validate-address';
import { useAppDispatch } from '@/src/store';

import PromotionBanner from './components/PromotionBanner';

export default function SubscriptionLandingPage() {
  /**
   * Start Initials
   */
  const validateAddressRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    // Prefer lowercase 'code' over uppercase 'CODE'
    const lower = searchParams.get('code');
    const upper = searchParams.get('CODE');
    const code = lower ?? upper;
    const referralCode = searchParams.get('referral');

    // Clear previous intent, then set new code if present
    dispatch(setUrlDiscountCode(code));
    dispatch(setReferralCode(referralCode));
  }, [searchParams, dispatch]);
  /**
   * End Lifecycle Methods
   */

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />
      <PromotionBanner />
      {/* Why Subscribe Section */}
      <WhySubscribe />
      {/* How It Works Section */}
      <HowItWorks />
      <div ref={validateAddressRef} className="relative pb-9">
        <div className="absolute inset-0 bg-gradient-to-b from-white to-mulberry/5"></div>
        <div className="absolute top-0 right-0 w-96 h-96  bg-mulberry/5 rounded-full -mr-48 blur-3xl"></div>
        <div className="absolute bottom-0   left-0 w-96 h-96 bg-mulberry/5 rounded-full -ml-48 blur-3xl"></div>
        <ValidateAddress />
      </div>
    </div>
  );
}
