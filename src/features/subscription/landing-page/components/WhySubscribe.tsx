import Image from 'next/image';

const benefits = [
  {
    image: '/FarmFresh.svg',
    title: 'Farm-Fresh & Hand-Picked',
    description:
      'Enjoy premium mulberries harvested at peak ripeness and delivered directly to your Bay Area home.',
  },
  {
    image: '/LimitedTime.svg',
    title: 'Limited-Time Access',
    description:
      'Our Very Mulberries are only available for a short season—ending late June. Secure your share before we sell out!',
  },
  {
    image: '/LimitedDelivery.svg',
    title: 'Limited Delivery Slots',
    description: 'Secure your spot now, as delivery slots are first come, first served.',
  },
  {
    image: '/FarmTable.svg',
    title: 'Farm-to-Table Experience',
    description:
      'Come to the farm to enjoy mulberries during U-Pick—and for those at home, choose a home delivery to savor the flavor of the season.',
  },
];

const WhySubscribe = () => {
  return (
    <section id="why" className="py-10 px-4 lg:px-0 lg:py-20  bg-white">
      <div className="container mx-auto p-0 lg:px-4">
        <div className="max-w-3xl mx-auto text-center lg:mb-10 mb-6">
          <h2 className="text-mulberry lg:text-3xl text-2xl font-semibold mb-6">
            Why Home Delivery?
          </h2>
          <p className="text-base lg:text-xl font-medium  text-gray-600">
            No more rushing to pick before the season ends—enjoy fresh mulberries at home with a
            delivery. Perfect for smoothies, baking, snacking, sharing and freezing.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-100  transition-all duration-300 flex flex-col items-center text-center h-full"
            >
              <div className="mb-4 p-3  rounded-full">
                <Image src={benefit.image} alt={benefit.title} width={60} height={60} />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-mulberry">{benefit.title}</h3>
              <p className="text-gray-600 flex-grow">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhySubscribe;
