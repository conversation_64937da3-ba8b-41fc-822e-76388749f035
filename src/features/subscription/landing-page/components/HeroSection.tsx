'use client';

import Image from 'next/image';

import { Button } from '@/src/components/ui/button';

const HeroSection = () => {
  return (
    <div className="relative w-full flex items-center overflow-hidden py-0 lg:py-6 md:py-0">
      <div className="container mx-auto px-4 sm:px-6 z-10 w-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-12 items-center">
          <div className="order-1 lg:order-1">
            <p className="text-black font-medium mb-4 md:mb-3 text-sm md:text-base">
              Picked fresh at our farm in Brentwood, CA - Delivered in{' '}
              <b>limited zones in the Bay Area</b>
            </p>
            <h1 className="font-bold text-2xl sm:text-3xl md:text-4xl lg:text-5xl mb-4 md:mb-6 leading-tight">
              <span className="text-mulberry ">Fresh, Sweet Very Mulberries</span>
              <br />
              Delivered to Your Door!
            </h1>
            <p className="text-gray-700 text-base md:text-lg mb-6 md:mb-8 max-w-lg">
              Introducing Very Mulberry Home Delivery. For just a few weeks each year, our fields in
              Brentwood, California come alive with the rarest, juiciest, nutrient-dense
              mulberries—hand-picked at their peak and bursting with flavor and nutrition
            </p>

            <div className="flex flex-col sm:flex-row gap-3 md:gap-4">
              <Button
                className="bg-mulberry hover:bg-mulberryHover text-white rounded-full px-4 py-2 md:px-6 md:py-6 text-base md:text-lg w-full sm:w-auto"
                onClick={() => {
                  const isMobile = window.innerWidth < 768;
                  const selector = isMobile ? '#validate-address-form' : '#validate-address';
                  const element = document.querySelector(selector);
                  if (element) {
                    const y = element.getBoundingClientRect().top + window.pageYOffset - 100;
                    window.scrollTo({ top: y, behavior: 'smooth' });
                  }
                }}
              >
                Check Delivery Availability
              </Button>
              <Button
                className="bg-mulberry hover:bg-mulberryHover text-white rounded-full px-4 py-2 md:px-6 md:py-6 text-base md:text-lg w-full sm:w-auto"
                onClick={() => {
                  const element = document.querySelector('#gift-validate-address-form');
                  if (element) {
                    const y = element.getBoundingClientRect().top + window.pageYOffset - 100;
                    window.scrollTo({ top: y, behavior: 'smooth' });
                  }
                }}
              >
                Send Gift
              </Button>
            </div>
          </div>
          <div className="relative order-1 lg:order-2  lg:mb-0">
            <div className="rounded-xl lg:w-[80%] overflow-hidden shadow-lg mx-auto max-w-md lg:max-w-none">
              <Image
                width={1000}
                height={1000}
                src="/mulberry-bag.png"
                alt="Very Mulberry Delivery Package"
                className="w-full h-auto object-cover"
              />
              <div className="absolute top-4 right-4 bg-mulberry backdrop-blur-sm rounded-full px-4 py-2 md:px-4 md:py-2 shadow-lg animate-bounce">
                <p className="text-white font-semibold text-sm md:text-base">Limited Season!</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
