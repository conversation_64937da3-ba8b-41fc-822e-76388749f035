'use client';

import { Calendar, ChevronRight } from 'lucide-react';
import React from 'react';

import { Button } from '@/src/components/ui/button';

export default function PromotionBanner() {
  return (
    <section className="container mx-auto mt-6">
      <div className="px-6 bg-mulberry text-white py-8 rounded-t-xl">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          <div className="flex items-center gap-4">
            <div className="bg-white p-3 rounded-full">
              <Calendar size={32} className="text-mulberry" />
            </div>
            <div>
              <h2 className="text-2xl md:text-3xl font-bold">HOME Deliveries Have BEGUN!</h2>
              <p className="text-lg font-medium">
                Don&apos;t miss out on farm-fresh mulberries delivered directly to your door
              </p>
              <p className="text-sm font-medium">
                After ordering, you&apos;ll receive a notification within 3 days with your delivery
                day, route zone, and estimated arrival time—based on harvest conditions and
                logistics
              </p>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              size="lg"
              className="bg-white text-mulberry hover:bg-mulberryLight"
              onClick={() => {
                const isMobile = window.innerWidth < 768;
                const selector = isMobile ? '#validate-address-form' : '#validate-address';
                const element = document.querySelector(selector);
                if (element) {
                  const y = element.getBoundingClientRect().top + window.pageYOffset - 100;
                  window.scrollTo({ top: y, behavior: 'smooth' });
                }
              }}
            >
              Check Delivery Availability
              <ChevronRight size={16} className="ml-1" />
            </Button>
          </div>
        </div>
      </div>
      <div className="bg-purple-50 py-2 text-center rounded-b-xl">
        <p className="text-mulberry font-medium">
          Limited Season! Mulberry season is short and ends in late June
        </p>
      </div>
    </section>
  );
}
