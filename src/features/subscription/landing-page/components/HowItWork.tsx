import Image from 'next/image';

import { <PERSON>, CardContent, CardHeader } from '@/src/components/ui/card';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/src/components/ui/carousel';

const steps = [
  {
    Image: '/1.jpg',
    title: 'Validate Your Address',
    description:
      'To start, enter and validate your home delivery address, then select the mulberry packs you’d like to order.',
  },
  {
    Image: '/2.png',
    title: 'Delivered on a Set Day Based on Your Zone',
    description:
      'Your mulberries will be delivered on a set day of the week based on your delivery zone. Once we validate your address and confirm eligibility, we’ll assign your delivery day accordingly. Please note that we’re not able to accommodate custom delivery day requests this season.',
  },
  {
    Image: '/3.png',
    title: 'Fresh-Picked in the Morning',
    description:
      'The morning of your delivery, our harvest team will hand-pick your mulberries, carefully pack them into clamshells, label each order, and fill up our Very Mulberry delivery van.',
  },
  {
    Image: '/4.png',
    title: 'Delivered in an 8-hour Window',
    description:
      'Deliveries occur on your assigned day within a broad time window based on your zone.',
  },
  {
    Image: '/5.png',
    title: 'Refrigerate Upon Arrival',
    description:
      'Please make sure the Very Mulberries can be left at or near your front door in a safe place and please refrigerate them as soon as possible. Mulberries are very delicate.',
  },
];

const HowItWorks = () => {
  return (
    <section id="how" className="lg:pt-0 lg:pb-10 py-10 ">
      <div className="container mx-auto px-1">
        <div className="max-w-3xl mx-auto text-center mb-8">
          <h2 className="text-mulberry lg:text-3xl text-2xl font-semibold mb-6">
            How Home Delivery Work
          </h2>
          <p className="text-base md:text-xl text-gray-600">
            Our simple process ensures you get the freshest Very Mulberries with minimal hassle.
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          {/* Mobile Carousel - Only visible on small screens */}
          <div className="md:hidden relative">
            <Carousel
              className="w-full max-w-xs mx-auto"
              opts={{
                align: 'center',
                loop: true,
              }}
            >
              <CarouselContent>
                {steps.map((step, index) => (
                  <CarouselItem key={index} className="basis-full">
                    <Card className="h-full relative">
                      <span className="absolute top-2 left-2 bg-mulberry text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold z-10">
                        {index + 1}
                      </span>
                      <CardHeader className="p-0">
                        <img
                          src={step.Image}
                          alt={`Step ${index + 1}`}
                          className="w-full h-full object-contain rounded-t-xl"
                        />
                      </CardHeader>
                      <CardContent className="pt-4">
                        <h3 className="font-semibold">{step.title}</h3>
                        <p className="text-gray-500 mt-2">{step.description}</p>
                      </CardContent>
                    </Card>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 z-10" />
              <CarouselNext className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 z-10" />
            </Carousel>
          </div>

          {/* Desktop Carousel - Only visible on large screens */}
          <div className="hidden md:block">
            <div className="w-full">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {steps.map((step, index) => (
                  <div key={index} className="">
                    <Card className="h-full relative">
                      <span className="absolute top-2 left-2 bg-mulberry text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold z-10">
                        {index + 1}
                      </span>
                      <CardHeader className="p-0">
                        <Image
                          src={step.Image}
                          alt={`Step ${index + 1}`}
                          width={300}
                          height={300}
                          className="w-full h-auto object-contain rounded-t-xl"
                        />
                      </CardHeader>
                      <CardContent className="p-3">
                        <h3 className="font-semibold">{step.title}</h3>
                        <p className="text-gray-500 mt-2">{step.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
