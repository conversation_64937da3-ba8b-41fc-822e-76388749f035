'use client';

// import { MinusIcon, PlusIcon } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardFooter } from '@/src/components/ui/card';
import SubscriptionTitle from '@/src/features/subscription/components/SubscriptionTitle';
import {
  selectSubscriptionDetails,
  setSubscriptionDetails,
} from '@/src/features/subscription/slices/subscriptionDetailsSlice';
import {
  fetchSubscriptionProduct,
  selectSubscriptionProduct,
  selectSubscriptionProductLoading,
  selectSubscriptionProductError,
} from '@/src/features/subscription/slices/subscriptionProductSlice';
import { BUTTON_TEXTS, ROUTES } from '@/src/lib/constants';
import { useAppDispatch, useAppSelector } from '@/src/store';

import SubscriptionStepper from '../components/SubscriptionStepper';
import { updateMissingAddressLog } from '../slices/addressValidationSlice';

interface SelectVariationInterface {
  id: string;
  name: string;
  price?: number;
  image?: string;
}

export default function ProductSelection() {
  /**
   * Start Initials
   */
  const router = useRouter();
  const dispatch = useAppDispatch();

  const subscriptionDetails = useAppSelector(selectSubscriptionDetails);
  const product = useAppSelector(selectSubscriptionProduct);
  const isLoading = useAppSelector(selectSubscriptionProductLoading);
  const error = useAppSelector(selectSubscriptionProductError);

  // Track the selected variation with image included
  const [selectedVariation, setSelectedVariation] = useState<SelectVariationInterface | null>(null);

  // Add quantity state
  const [quantity, _setQuantity] = useState(1);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    if (product && product.variations?.length) {
      // If we have a saved variation ID, find the matching variation
      if (subscriptionDetails?.variationId) {
        const matchingVariation = product.variations.find(
          v => v.id === subscriptionDetails.variationId
        );

        if (matchingVariation) {
          // Use the image from Redux if available, otherwise use from variation
          const variationImage =
            subscriptionDetails.variationImage || matchingVariation.image || product.image;
          setSelectedVariation({
            id: matchingVariation.id,
            name: matchingVariation.name,
            price: matchingVariation.price || product.price,
            image: variationImage,
          });
        } else {
          // Default to first variation if no match
          const firstVariation = product.variations[0];
          setSelectedVariation({
            id: firstVariation.id,
            name: firstVariation.name,
            price: firstVariation.price || product.price,
            image: firstVariation.image || product.image,
          });
        }
      } else {
        // Default to first variation if no saved variation
        const firstVariation = product.variations[0];
        setSelectedVariation({
          id: firstVariation.id,
          name: firstVariation.name,
          price: firstVariation.price || product.price,
          image: firstVariation.image || product.image,
        });
      }
    }
  }, [product, subscriptionDetails?.variationId, subscriptionDetails?.variationImage]);

  useEffect(() => {
    // Add a check to prevent duplicate API calls
    if (!product && !isLoading) {
      dispatch(fetchSubscriptionProduct());
    }
  }, [product, isLoading]);

  // Add this effect to check for address data
  useEffect(() => {
    // Redirect if address data is missing
    if (!subscriptionDetails.zoneId || !subscriptionDetails.address) {
      router.push(ROUTES.SUBSCRIPTION);
    }
  }, [subscriptionDetails, router]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */

  // Calculate total using selected variation and quantity
  const subtotal = (selectedVariation?.price ?? 0) * quantity;
  const total = subtotal + (product?.deliveryCharge ?? 0);

  const handleContinue = () => {
    if (!selectedVariation) {
      toast.error('Please select a pack size');
      return;
    }

    if (product) {
      const productData = {
        variationId: selectedVariation.id,
        variationName: selectedVariation.name,
        variationImage: selectedVariation.image, // Use image from selectedVariation
        productName: product.name,
        productPrice: selectedVariation.price,
        deliveryCharge: product.deliveryCharge,
        quantity: quantity, // Add quantity to the details
        total_amount: total,
      };

      dispatch(
        updateMissingAddressLog({
          data: {
            product: productData,
          },
        })
      );

      // Store subscription details in Redux with selected variation, image, and quantity
      dispatch(setSubscriptionDetails(productData));
    }

    // Check if delivery days are available
    const hasDeliveryDays = subscriptionDetails.deliveryDays?.length > 0;

    // Skip delivery date selection if no delivery days are available
    if (hasDeliveryDays) {
      router.push(ROUTES.SUBSCRIPTION_DELIVERY_DAY);
    } else {
      // Skip to user info page if no delivery days
      router.push(ROUTES.SUBSCRIPTION_USER_INFO);
    }
  };

  const handleVariationSelect = (variation: SelectVariationInterface) => {
    if (product && product.variations?.length) {
      setSelectedVariation({
        id: variation.id,
        name: variation.name,
        price: variation.price || product?.price,
        image: variation.image || product.image,
      });
    }
  };

  // Get the max quantity from product or default to 5
  // const maxQty = Number(product?.maxProductQty);

  // Add these increment/decrement functions
  // const increaseQuantity = () => {
  //   if (quantity < maxQty) {
  //     setQuantity(quantity + 1);
  //   }
  // };

  // const decreaseQuantity = () => {
  //   if (quantity > 1) {
  //     setQuantity(quantity - 1);
  //   }
  // };

  /**
   * End Custom Methods
   */

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="text-center p-8">
        <h2 className="text-xl font-bold text-red-600">Error</h2>
        <p className="mt-2">{error || 'Product information unavailable'}</p>
        <Button
          onClick={() => dispatch(fetchSubscriptionProduct())}
          className="mt-4 bg-mulberry hover:bg-mulberryHover"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <>
      <SubscriptionTitle />

      {/* Move stepper to top of page */}
      <div className="max-w-5xl mx-auto mt-4">
        <SubscriptionStepper currentStep={2} />
      </div>

      {/* Change grid to single column */}
      <div className="max-w-3xl mx-auto md:mb-6 mb-16">
        <Card>
          <CardContent className="p-2 md:p-6 space-y-6 pt-1">
            {/* Product Image and Details */}
            <div className="flex flex-col items-center">
              <div className="relative w-full md:h-72 h-48 mb-2 overflow-hidden">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  style={{
                    objectFit: 'cover',
                  }}
                  className="rounded-md transition-all duration-300 ease-in-out w-full h-full"
                  sizes="100vw"
                  placeholder="blur"
                  blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2YyZTllOSIvPjwvc3ZnPg=="
                  loading="eager"
                  priority
                />
              </div>
              <h2 className="text-2xl font-bold">{product.name}</h2>
              <p className="text-gray-600 text-center mt-1">{product.description}</p>
            </div>

            {/* Pack Selection */}
            <div className="flex flex-col space-y-2 !mt-3">
              <span className="font-semibold text-lg">Select Pack Size</span>
              <div className="grid grid-cols-2 md:grid-cols-4 md:gap-8 gap-4">
                {product.variations?.map(variation => {
                  return (
                    <Button
                      key={variation.id}
                      variant={selectedVariation?.id === variation.id ? 'default' : 'outline'}
                      onClick={() => handleVariationSelect(variation)}
                      className={`w-full gap-0 p-0 pb-1 flex flex-col items-center justify-start h-auto min-h-[120px] overflow-hidden ${
                        selectedVariation?.id === variation.id
                          ? 'bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive'
                          : ''
                      }`}
                    >
                      <div className="w-full h-[5rem] relative mb-1">
                        <Image
                          src={variation.image || product.image}
                          alt={variation.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <span className="text-base truncate w-full font-semibold text-center">
                        {variation.name}
                      </span>
                      <span className="t w-full text-center">
                        ${(variation.price || product.price).toFixed()}
                      </span>
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Quantity Selector */}
            {/* <div className="border rounded-md p-4">
              <div className="flex justify-between items-center">
                <span className="font-medium">Number of Bundles</span>
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={decreaseQuantity}
                    disabled={quantity <= 1}
                    className="h-8 w-8"
                  >
                    <MinusIcon className="h-4 w-4" />
                  </Button>
                  <span className="text-lg w-6 text-center">{quantity}</span>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={increaseQuantity}
                    disabled={quantity >= maxQty}
                    className="h-8 w-8"
                  >
                    <PlusIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {quantity >= maxQty && (
                <p className="text-red-500 text-sm mt-2">Maximum quantity of {maxQty} reached</p>
              )}
            </div> */}

            {/* Price Summary */}
            {/* <div className="space-y-2">
              <span className="font-semibold text-lg">Order Summary</span>
              <div className="flex justify-between">
                <span>
                  {product.name} ({selectedVariation?.name})
                </span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Charge</span>
                <span>${product.deliveryCharge.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-bold pt-2 border-t text-lg">
                <span>Total</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div> */}
          </CardContent>
          <CardFooter className="flex flex-col p-2">
            <Button
              onClick={handleContinue}
              className="w-full bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive"
            >
              {BUTTON_TEXTS.CONTINUE}
            </Button>
            <p className="text-xs text-left text-gray-500 mt-4">
              Have a promo code? Apply it on the payment page.
            </p>
          </CardFooter>
        </Card>
      </div>
    </>
  );
}
