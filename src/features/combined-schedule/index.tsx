'use client';

import {
  format,
  parseISO,
  isValid,
  startOfToday,
  addDays,
  isBefore,
  isAfter,
  parse,
} from 'date-fns';
import { Clock as ClockIcon, MapPin, MapPinHouse, MapPinned, Ticket } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

import ErrorAlert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import { Badge } from '@/src/components/ui/badge';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader } from '@/src/components/ui/card';
import {
  CLOSE_UPICK_CARD_TEXT,
  ENDPOINTS,
  EVENT_STATUS,
  FARM_ADDRESS,
  FARM_ADDRESS_LINK,
  ROUTES,
  SCHEDULE_TITLE,
  SOLD_OUT_CARD_TEXT,
  TICKET_TAILOR_ENPOINTS,
} from '@/src/lib/constants';
import { convertToPacificTime, getPacificTimeNow } from '@/src/lib/timezone';
import { formatTimeRange } from '@/src/lib/utils';
import { useAppSelector, RootState } from '@/src/store';
import { FarmerMarket } from '@/src/types/FarmerMarket';
import { TicketTailorEvent } from '@/src/types/TicketTailor';

interface CombinedEvent {
  date: string;
  type: 'FarmersMarket' | 'ticketTailor' | 'delivery';
  title: string;
  location: string;
  time: string;
  id: string;
  latitude?: number | string;
  longitude?: number | string;
  checkoutUrl?: string;
  timeSlots?: string[];
  venue?: { name: string; postal_code: string };
  event_series_id: string;
  zip_codes?: string[];
  isSoldOut?: boolean;
}

interface GroupedEvents {
  [key: string]: CombinedEvent[];
}

const DAYS_TO_SHOW = 90; // Show next 30 days

const CombinedSchedule = () => {
  /**
   * Start Initials
   */
  const {
    error: marketError,
    farmerMarkets,
    loading: marketLoader,
  } = useAppSelector((state: RootState) => state.farmerMarket);

  const {
    error: ticketTailorError,
    eventSchedule,
    loading: eventLoader,
  } = useAppSelector((state: RootState) => state.ticketTailor);

  const {
    error: deliveryZoneError,
    zones,
    loading: zoneLoader,
  } = useAppSelector((state: RootState) => state.deliveryZone);

  const router = useRouter();

  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  const error = marketError || ticketTailorError || deliveryZoneError;

  if (error) return <ErrorAlert title="Error" error={error} />;

  if (marketLoader || eventLoader || zoneLoader) return <Spinner></Spinner>;

  // Generate dates for the next DAYS_TO_SHOW days
  const dates = Array.from({ length: DAYS_TO_SHOW }, (_, i) => {
    const date = addDays(convertToPacificTime(startOfToday()), i);
    return format(date, 'yyyy-MM-dd');
  });

  // Current date and time for filtering
  const now = getPacificTimeNow();
  const today = format(now, 'yyyy-MM-dd');

  const handleFarmerMarketLocationClick = (location: string) => {
    const encodedAddress = encodeURIComponent(location);
    const googleMapsUrl = `${ENDPOINTS.GOOGLE_MAPS_ADDRESS_URL}${encodedAddress}`;
    window.open(googleMapsUrl, '_blank');
  };

  const handleUpickLocationClick = (venue: string) => {
    if (venue) {
      const mapsUrl = `${venue}`;
      window.open(mapsUrl, '_blank');
    }
  };

  const handleBookNow = (event: CombinedEvent) => {
    if (!event.event_series_id) return;

    const eventSeriesId = event.event_series_id;
    const eventIdWithoutPrefix = eventSeriesId.substring(3);

    // Extract date from the event
    const eventDate = event.date;

    window.location.href = `${TICKET_TAILOR_ENPOINTS.TICKET_TAILOR_EVENT_URL}${process.env.NEXT_PUBLIC_TICKET_TAILOR_EVENT}/${eventIdWithoutPrefix}/select-date/${eventDate}`;
  };

  const isEventTimePassed = (event: CombinedEvent) => {
    if (!event.time) return false;

    const now = getPacificTimeNow();
    const eventDate = parseISO(event.date);

    // Extract end time from the time range (format: "start - end")
    const endTimeStr = event.time.split(' - ')[1];
    if (!endTimeStr) return false;

    try {
      // Parse the end time
      const endTime = parse(endTimeStr, 'h:mm a', eventDate);

      // Check if current time is past the end time
      return isAfter(now, endTime);
    } catch (_error) {
      return false;
    }
  };

  const combinedEvents: CombinedEvent[] = [
    // Add farmer markets (filter out passed events)
    ...dates.flatMap(date => {
      // Skip past dates
      if (date < today) return [];

      const dayOfWeek = format(parseISO(date), 'EEE').toLowerCase();
      return farmerMarkets.flatMap((market: FarmerMarket) =>
        market.schedules
          .filter(
            schedule =>
              schedule.day === dayOfWeek &&
              !schedule.is_closed &&
              isValid(parseISO(market.season_start_date)) &&
              isValid(parseISO(market.season_end_date)) &&
              !isBefore(parseISO(date), parseISO(market.season_start_date)) &&
              !isAfter(parseISO(date), parseISO(market.season_end_date))
            // Removed the time slot passed check:
            // !(date === today && isTimeSlotPassed(schedule.start_time))
          )
          .map(schedule => ({
            date,
            type: SCHEDULE_TITLE.FARMERS_MARKET,
            title: market.name,
            location: market.location
              ? `${market.location.street}, ${market.location.city}, ${market.location.state}${market.location.zip_code ? `, ${market.location.zip_code}` : ''}`
              : '',
            // Show original time format from database
            time: formatTimeRange(schedule.start_time, schedule.end_time),
            id: `${market.id}-${date}`,
            latitude: market.location?.latitude,
            longitude: market.location?.longitude,
            event_series_id: '',
          }))
      );
    }),

    // Add Ticket Tailor events (filter out passed events and hidden events)
    ...(eventSchedule?.data || [])

      .filter((event: TicketTailorEvent) =>
        event.hidden === 'true' ||
        event.unavailable === 'true' ||
        event.status !== EVENT_STATUS.PUBLISHED
          ? false
          : true
      ) // Filter out hidden or unavailable events
      .map((event: TicketTailorEvent) => {
        // Get raw date and time values
        const eventDate = event.start?.date || '';

        // Use original time values from database
        const startTime = event.start?.time || '';
        const endTime = event.end?.time || '';

        // Check if event is sold out based on ticket types
        const isSoldOut =
          event.ticket_types?.every(ticket => ticket.status === 'sold_out') || false;

        return {
          date: eventDate,
          type: 'ticketTailor' as const,
          title: event.name?.toString() || 'Event',
          location: event.venue?.name || '',
          // Format time using utility function
          time: startTime && endTime ? formatTimeRange(startTime, endTime) : '',
          id: event.id,
          checkoutUrl: event.checkout_url,
          venue: event.venue,
          event_series_id: event.event_series_id || '',
          isSoldOut, // Add the sold out flag to the event object
        };
      }),

    // Add delivery zone events
    ...dates.flatMap(date => {
      // Skip past dates
      if (date < today) return [];

      const dayOfWeek = format(parseISO(date), 'EEEE').toLowerCase();

      return zones.flatMap(zone => {
        // Skip zones without required fields
        if (!zone.start_date || !zone.delivery_days || !zone.delivery_window) return [];

        // Check if zone is active for this date
        const isInSeason =
          isValid(parseISO(zone.start_date)) &&
          (!zone.end_date || isValid(parseISO(zone.end_date))) &&
          !isBefore(parseISO(date), parseISO(zone.start_date)) &&
          (!zone.end_date || !isAfter(parseISO(date), parseISO(zone.end_date)));

        // Check if delivery happens on this day of week
        const isDeliveryDay = zone.delivery_days.some(
          (day: string) => day.toLowerCase() === dayOfWeek
        );

        if (isInSeason && isDeliveryDay) {
          return [
            {
              date,
              type: SCHEDULE_TITLE.HOME_DELIVERY,
              title: `${zone.zone_name || ''}`,
              time: zone.delivery_window || '',
              id: `${zone.id}-${date}`,
              zip_codes: zone.zip_codes,
              event_series_id: '',
            },
          ];
        }

        return [];
      });
    }),
  ];

  // Sort events by date and time
  const sortedEvents = combinedEvents.sort((a, b) => {
    try {
      const dateA = parseISO(a.date);
      const dateB = parseISO(b.date);

      if (!isValid(dateA) || !isValid(dateB)) return 0;

      // First compare dates
      const dateCompare = dateA.getTime() - dateB.getTime();
      if (dateCompare !== 0) return dateCompare;

      // If same date, prioritize farmer markets over ticket tailor events
      if (a.type !== b.type) {
        return a.type === SCHEDULE_TITLE.FARMERS_MARKET ? -1 : 1;
      }

      // If same type, compare start times
      const [startTimeA] = (a.time || '').split(' - ');
      const [startTimeB] = (b.time || '').split(' - ');

      // Convert times to 24-hour format for comparison
      const timeA = parse(startTimeA, 'h:mm a', getPacificTimeNow());
      const timeB = parse(startTimeB, 'h:mm a', getPacificTimeNow());

      if (!isValid(timeA) || !isValid(timeB)) return 0;

      return timeA.getTime() - timeB.getTime();
    } catch (_error) {
      return 0;
    }
  });

  // Group events by date
  const groupedEvents = sortedEvents.reduce<GroupedEvents>((groups, event) => {
    if (!event.date) return groups;

    if (!groups[event.date]) {
      groups[event.date] = [];
    }

    // Add event to the group with its time slot
    groups[event.date].push({
      ...event,
      timeSlots: [event.time],
    });

    return groups;
  }, {});

  // Find the last event date from all events (both market and ticketTailor)
  const findLastEventDate = () => {
    let lastEventDate = convertToPacificTime(startOfToday());

    // Check all combined events for their dates
    combinedEvents.forEach(event => {
      if (event.date) {
        const eventDate = parseISO(event.date);
        if (isValid(eventDate) && isAfter(eventDate, lastEventDate)) {
          lastEventDate = eventDate;
        }
      }
    });

    return lastEventDate;
  };

  const lastEventDate = findLastEventDate();

  // Generate all dates from today to the last event date
  const allDatesInRange: Record<string, CombinedEvent[]> = {};
  let currentDate = convertToPacificTime(startOfToday());

  // Find the earliest start date and latest end date of any visible U-Pick event
  const uPickDateRange = (eventSchedule?.data || [])
    .filter(
      (event: TicketTailorEvent) =>
        event.hidden !== 'true' && event.unavailable !== 'true' && event.start?.date
    )
    .reduce(
      (range: { earliest: Date | null; latest: Date | null }, event: TicketTailorEvent) => {
        if (!event.start?.date) return range;

        const eventStartDate = parseISO(event.start.date);
        const eventEndDate = event.end?.date ? parseISO(event.end.date) : null;

        return {
          earliest:
            !range.earliest || isBefore(eventStartDate, range.earliest)
              ? eventStartDate
              : range.earliest,
          latest:
            !range.latest || (eventEndDate && isAfter(eventEndDate, range.latest))
              ? eventEndDate
              : range.latest,
        };
      },
      { earliest: null, latest: null }
    );

  const earliestUPickStartDate = uPickDateRange.earliest;
  const latestUPickEndDate = uPickDateRange.latest;

  while (!isAfter(currentDate, lastEventDate)) {
    const dateKey = format(currentDate, 'yyyy-MM-dd');

    // Include the date if:
    // 1. It has events, OR
    // 2. It's within the U-Pick season (after start date and before end date)
    const hasEvents = groupedEvents[dateKey]?.length > 0;
    const isWithinUPickSeason =
      earliestUPickStartDate &&
      !isBefore(currentDate, earliestUPickStartDate) &&
      (!latestUPickEndDate || !isAfter(currentDate, latestUPickEndDate));

    if (hasEvents || isWithinUPickSeason) {
      allDatesInRange[dateKey] = groupedEvents[dateKey] || [];
    }

    currentDate = addDays(currentDate, 1);
  }

  return (
    <div className="min-h-0 max-w-5xl mb-6 m-auto">
      {Object.entries(allDatesInRange).map(([date, events]) => {
        // Skip invalid dates
        if (!date || !isValid(parseISO(date))) return null;

        const currentDate = parseISO(date);

        // Skip dates that have no events and are not within U-Pick season
        const hasEvents = events.length > 0;
        const isWithinUPickSeason =
          earliestUPickStartDate &&
          !isBefore(currentDate, earliestUPickStartDate) &&
          (!latestUPickEndDate || !isAfter(currentDate, latestUPickEndDate));

        if (!hasEvents && !isWithinUPickSeason) return null;

        // Skip dates before the earliest U-Pick start date
        if (earliestUPickStartDate && isBefore(parseISO(date), earliestUPickStartDate)) {
          if (events.length === 0) return null; // Only show if there are other events
        }

        // Group events by title for this date
        const eventsByTitle = (events as CombinedEvent[]).reduce(
          (acc, event) => {
            if (!acc[event.title]) {
              acc[event.title] = [];
            }
            acc[event.title].push(event);
            return acc;
          },
          {} as Record<string, CombinedEvent[]>
        );

        // Check if there are any U-Pick (ticketTailor) events for this date
        const hasUPickEvents = (events as CombinedEvent[]).some(
          event => event.type === SCHEDULE_TITLE.TICKET_TAILOR
        );

        return (
          <div key={date}>
            {/* Date separator with visual divider */}
            <div className="relative flex items-center my-6">
              <div className="flex-grow border-t border-gray-300" />
              <div className="mx-4 bg-white px-4 py-1 text-gray-800 font-semibold rounded-full shadow-md border border-gray-200">
                {format(parseISO(date), 'EEEE, MMMM d, yyyy')}
              </div>
              <div className="flex-grow border-t border-gray-300" />
            </div>

            <div className="">
              {/* Show all events for this date */}
              {Object.entries(eventsByTitle).map(([title, titleEvents]) => {
                // Group is NOT sold out if ANY event has ANY ticket type available
                const isSoldOut = !titleEvents?.some(titleEvent => titleEvent.isSoldOut === false);
                // Check if ALL time slots for this title have passed
                const allTimeSlotsPassed = titleEvents.every(event => isEventTimePassed(event));
                // Check if this is a Ticket Tailor event
                const isTicketTailorEvent = titleEvents[0]?.type === SCHEDULE_TITLE.TICKET_TAILOR;

                // Show closed card regardless of sold-out status
                if (allTimeSlotsPassed) {
                  return (
                    <Card
                      key={title}
                      className="relative p-4 overflow-hidden border border-gray-200 mb-4"
                    >
                      <div className="absolute inset-0 bg-gray-100/70 backdrop-blur-[1px] flex items-center justify-center z-10">
                        <div className="bg-mulberry text-white px-6 py-2 rounded-full shadow-md flex items-center ">
                          <span className="font-bold">{CLOSE_UPICK_CARD_TEXT.HEADING}</span>
                        </div>
                      </div>

                      <CardContent className="p-0 opacity-60">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold">{title}</h3>
                          </div>
                        </div>
                        <div className="flex flex-col gap-1">
                          {/* Location Section */}
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600 text-sm">
                              {SOLD_OUT_CARD_TEXT.ADDRESS}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                } else if (isSoldOut && isTicketTailorEvent) {
                  // Show sold out card only for Ticket Tailor events that are sold out
                  return (
                    <Card
                      key={title}
                      className="relative p-4 overflow-hidden border border-gray-200 mb-4"
                    >
                      <div className="absolute inset-0 bg-gray-100/70 backdrop-blur-[1px] flex items-center justify-center z-10">
                        <div className="bg-gray-500 text-white px-6 py-2 rounded-full shadow-md flex items-center ">
                          <span className="font-bold">SOLD OUT</span>
                        </div>
                      </div>

                      <CardContent className="p-0 opacity-60">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold">{title}</h3>
                          </div>
                        </div>
                        <div className="flex flex-col gap-1">
                          {/* Location Section */}
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600 text-sm">
                              {SOLD_OUT_CARD_TEXT.ADDRESS}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                } else {
                  // Keep existing normal card logic
                  return (
                    <Card key={title} className="p-4 mb-4">
                      <CardHeader className="p-0">
                        <div className="flex justify-between items-center w-full">
                          <div className="flex flex-col gap-1">
                            <h3 className="text-lg font-semibold flex justify-between items-center">
                              {title}
                              {titleEvents[0].type === SCHEDULE_TITLE.HOME_DELIVERY &&
                              titleEvents[0].zip_codes?.length ? (
                                <span className="text-gray-800 font-medium text-xs sm:text-sm ml-2">
                                  (Includes portions of {titleEvents[0].zip_codes.join(', ')})
                                </span>
                              ) : null}
                            </h3>
                            {/* Event Type Badge */}
                            {titleEvents[0] && (
                              <Badge
                                variant="secondary"
                                className={`font-normal w-fit mb-2 max-md:mt-1 ${
                                  titleEvents[0].type === SCHEDULE_TITLE.FARMERS_MARKET
                                    ? 'bg-amber-100 text-amber-800 border border-amber-200'
                                    : titleEvents[0].type === SCHEDULE_TITLE.HOME_DELIVERY
                                      ? 'bg-mulberryLight/50 text-mulberry hover:bg-mulberryLight border-mulberry/50'
                                      : 'bg-blue-100 text-blue-800 border border-blue-200'
                                }`}
                              >
                                {titleEvents[0].type === SCHEDULE_TITLE.FARMERS_MARKET
                                  ? "Farmers' Market"
                                  : titleEvents[0].type === SCHEDULE_TITLE.HOME_DELIVERY
                                    ? 'Home Delivery'
                                    : SCHEDULE_TITLE.U_PICK}
                              </Badge>
                            )}
                          </div>

                          {titleEvents[0].type === SCHEDULE_TITLE.FARMERS_MARKET &&
                          titleEvents[0].latitude &&
                          titleEvents[0].longitude ? (
                            <button
                              onClick={() =>
                                handleFarmerMarketLocationClick(titleEvents[0].location)
                              }
                              className="px-3 py-1 hidden md:flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                            >
                              <MapPinned className="h-3 w-3" />
                              <span>Open Map</span>
                            </button>
                          ) : titleEvents[0].type === SCHEDULE_TITLE.TICKET_TAILOR ? (
                            <Button
                              onClick={() => handleBookNow(titleEvents[0])}
                              className="px-3 py-1 hidden md:flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                              size="sm"
                            >
                              <Ticket className="h-3 w-3" />
                              <span>Book Now</span>
                            </Button>
                          ) : titleEvents[0].type === SCHEDULE_TITLE.HOME_DELIVERY ? (
                            <Button
                              onClick={() => router.push(ROUTES.SUBSCRIPTION)}
                              className="px-3 py-1 hidden md:flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                              size="sm"
                            >
                              <MapPinHouse className="h-3 w-3" />
                              <span>Explore Home Delivery</span>
                            </Button>
                          ) : null}
                        </div>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="flex flex-col gap-1">
                          {/* Event content */}
                          <div className="flex flex-col gap-1">
                            {/* Time Section */}
                            <div className="flex items-center gap-2">
                              <ClockIcon className="h-4 w-4" />
                              <span className="font-medium text-sm uppercase">
                                {(() => {
                                  // Get all time slots for this event
                                  const allTimeSlots = titleEvents.flatMap(
                                    event => event.timeSlots || [event.time]
                                  );

                                  // Parse time slots to get start and end times
                                  const parsedSlots = allTimeSlots.map(slot => {
                                    const [start, end] = slot.split(' - ');
                                    return { start, end };
                                  });

                                  // Sort by start time using proper time comparison
                                  parsedSlots.sort((a, b) => {
                                    // Convert to Date objects for proper comparison
                                    const timeA = parse(a.start, 'h:mm a', getPacificTimeNow());
                                    const timeB = parse(b.start, 'h:mm a', getPacificTimeNow());

                                    if (!isValid(timeA) || !isValid(timeB)) return 0;
                                    return timeA.getTime() - timeB.getTime();
                                  });

                                  // Group consecutive slots
                                  const timeGroups = [];
                                  let currentGroup: { start: string; end: string }[] = [];

                                  for (let i = 0; i < parsedSlots.length; i++) {
                                    if (currentGroup.length === 0) {
                                      currentGroup.push(parsedSlots[i]);
                                    } else {
                                      const prevSlot = currentGroup[currentGroup.length - 1];
                                      // Check if this slot starts exactly when the previous ends
                                      if (prevSlot.end === parsedSlots[i].start) {
                                        currentGroup.push(parsedSlots[i]);
                                      } else {
                                        // This is a break, so finish current group and start a new one
                                        timeGroups.push([...currentGroup]);
                                        currentGroup = [parsedSlots[i]];
                                      }
                                    }
                                  }

                                  // Add the last group if it exists
                                  if (currentGroup.length > 0) {
                                    timeGroups.push(currentGroup);
                                  }

                                  // Format and render each group
                                  return timeGroups.map((group, groupIndex) => (
                                    <React.Fragment key={groupIndex}>
                                      {`${group[0].start} - ${group[group.length - 1].end}`}
                                      {groupIndex < timeGroups.length - 1 ? ', ' : ''}
                                    </React.Fragment>
                                  ));
                                })()}
                              </span>
                            </div>

                            {/* Location and Action Button Section */}
                            <div
                              className={`flex items-center text-gray-600  ${
                                titleEvents[0].type === SCHEDULE_TITLE.TICKET_TAILOR
                                  ? 'cursor-pointer group'
                                  : ''
                              }`}
                              onClick={() =>
                                titleEvents[0].type === SCHEDULE_TITLE.TICKET_TAILOR &&
                                handleUpickLocationClick(FARM_ADDRESS_LINK)
                              }
                            >
                              {titleEvents[0].type !== SCHEDULE_TITLE.HOME_DELIVERY && (
                                <>
                                  <MapPin className="w-4 h-4 mr-2" />
                                  <span className="text-sm group-hover:text-mulberry">
                                    {FARM_ADDRESS}
                                  </span>
                                </>
                              )}
                            </div>

                            {/* Map button for delivery zones */}
                            {titleEvents[0].type === SCHEDULE_TITLE.HOME_DELIVERY &&
                            titleEvents[0].zip_codes?.length ? (
                              <div className="flex items-center gap-2">
                                <MapPin className="text-gray-500 h-4 w-4" />
                                <Badge
                                  variant="outline"
                                  className="bg-mulberry rounded-full font-medium text-white hover:bg-mulberryHover border-none text-xs sm:text-sm"
                                >
                                  Open Map
                                </Badge>
                              </div>
                            ) : null}

                            {titleEvents[0] &&
                            titleEvents[0].type === SCHEDULE_TITLE.FARMERS_MARKET &&
                            titleEvents[0].latitude &&
                            titleEvents[0].longitude ? (
                              <button
                                onClick={() =>
                                  handleFarmerMarketLocationClick(titleEvents[0].location)
                                }
                                className="px-3 py-1 font-medium md:hidden mt-2 flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                              >
                                <MapPinned className="h-3 w-3" />
                                <span>Open Map</span>
                              </button>
                            ) : titleEvents[0].type === SCHEDULE_TITLE.TICKET_TAILOR ? (
                              <Button
                                onClick={() => handleBookNow(titleEvents[0])}
                                className="px-3 py-1 md:hidden mt-2 flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                                size="sm"
                              >
                                <Ticket className="h-3 w-3" />
                                <span>Book Now</span>
                              </Button>
                            ) : titleEvents[0].type === SCHEDULE_TITLE.HOME_DELIVERY ? (
                              <Button
                                onClick={() => router.push(ROUTES.SUBSCRIPTION)}
                                className="px-3 py-1 md:hidden mt-2 flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                                size="sm"
                              >
                                <MapPinHouse className="h-3 w-3" />
                                <span>Explore Home Delivery</span>
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                }
              })}

              {/* Display "U-PICK CLOSED" card if we're in U-Pick season but no events for this date */}
              {(() => {
                // Find the earliest visible U-Pick event
                const visibleEvents = (eventSchedule?.data || []).filter(
                  (event: TicketTailorEvent) =>
                    event.hidden !== 'true' && event.unavailable !== 'true'
                );

                // If no visible events, don't show the closed card
                if (visibleEvents.length === 0) return null;

                const currentDate = parseISO(date);

                // Don't show if current date is before the earliest start date
                if (!earliestUPickStartDate || isBefore(currentDate, earliestUPickStartDate))
                  return null;

                // Don't show if current date is after the latest end date
                if (latestUPickEndDate && isAfter(currentDate, latestUPickEndDate)) return null;

                // If we have U-Pick events on this date, don't show
                if (hasUPickEvents) return null;

                // Show the closed card
                return (
                  <Card className="relative p-4 overflow-hidden border border-gray-200 mb-4">
                    <div className="absolute inset-0 bg-gray-100/70 backdrop-blur-[1px] flex items-center justify-center z-10">
                      <div className="bg-mulberry text-white px-6 py-2 rounded-full shadow-md flex items-center ">
                        <span className="font-bold">{CLOSE_UPICK_CARD_TEXT.HEADING}</span>
                      </div>
                    </div>

                    <CardContent className="p-0 opacity-60">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                        <div>
                          <h3 className="text-lg font-semibold">{CLOSE_UPICK_CARD_TEXT.NAME}</h3>
                        </div>
                      </div>
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center text-gray-600">
                          <MapPin className="w-4 h-4 mr-2" />
                          <span className="text-sm">{FARM_ADDRESS}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })()}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default CombinedSchedule;
