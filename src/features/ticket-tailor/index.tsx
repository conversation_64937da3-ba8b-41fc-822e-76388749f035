'use client';

import { parseISO, isValid } from 'date-fns';
import { useRouter, usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import ErrorAlert from '@/src/components/error-alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/src/components/ui/alert-dialog';
import { Skeleton } from '@/src/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs';
import withAuth from '@/src/features/auth/hoc/withAuth';
import { ROUTES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

import { fetchUserTickets } from '../u-pick/slices/guestsSlice';

import OrderCard from './OrderCard';

const TicketListSkeleton = () => (
  <div className="space-y-4 mb-6">
    {[1, 2, 3, 4, 5].map(i => (
      <div key={i} className="border rounded-md overflow-hidden">
        <div className="flex justify-between items-center p-2 sm:p-4 bg-mulberryLightest">
          <div className="space-y-2">
            <Skeleton className="h-4 w-40 bg-mulberryLight/30" />
            <Skeleton className="h-5 w-48 bg-mulberryLight/30" />
          </div>
          <Skeleton className="h-9 w-9 sm:h-12 sm:w-12 rounded-full bg-mulberryLight/30" />
        </div>
      </div>
    ))}
  </div>
);

const Ticket = (): JSX.Element => {
  const router = useRouter();
  const pathname = usePathname();

  const {
    user,
    loading: authLoading,
    isAuthenticated,
  } = useAppSelector((state: RootState) => state.authentication);

  const { activeTickets, error, loading } = useAppSelector((state: RootState) => state.guests);

  const dispatch = useAppDispatch();
  const [showAlert, setShowAlert] = useState(false);

  // Add a dedicated handler function for closing the alert
  const handleCloseAlert = () => {
    setShowAlert(false);
  };

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push(ROUTES.LOGIN + '?redirect=' + ROUTES.RESERVATIONS);
      return;
    }

    // For initial load (no data yet), show loading state
    if (isAuthenticated && user && user.id && activeTickets.length === 0) {
      dispatch(
        fetchUserTickets({
          userId: user.id,
        })
      ).catch(err => {
        console.error('Error loading ticket data:', err);
      });
    }
  }, [dispatch, user, isAuthenticated, authLoading, router, activeTickets.length]);

  /**
   * Start Custom Methods
   */

  // Filter out past events and sort remaining by date
  const now = getPacificTimeNow();
  const sortedActiveTickets = [...activeTickets]
    .filter(orderGroup => {
      const orderId = Object.keys(orderGroup)[0];
      const event = orderGroup[orderId]?.[0];

      // Skip if no event date
      if (!event?.event_details?.start_date?.date) return false;

      const eventDate = parseISO(event.event_details.start_date.date);
      if (!isValid(eventDate)) return false;

      // Keep only future dates or today
      return eventDate.setHours(0, 0, 0, 0) >= now.setHours(0, 0, 0, 0);
    })
    .sort((orderGroupA, orderGroupB) => {
      const orderIdA = Object.keys(orderGroupA)[0];
      const orderIdB = Object.keys(orderGroupB)[0];

      const eventA = orderGroupA[orderIdA]?.[0];
      const eventB = orderGroupB[orderIdB]?.[0];

      // Get event dates from event_details.start_date
      const dateA = eventA?.event_details?.start_date?.date
        ? parseISO(eventA.event_details.start_date.date)
        : getPacificTimeNow();
      const dateB = eventB?.event_details?.start_date?.date
        ? parseISO(eventB.event_details.start_date.date)
        : getPacificTimeNow();

      if (!isValid(dateA) || !isValid(dateB)) return 0;

      // Sort by date (ascending)
      return dateA.getTime() - dateB.getTime();
    });

  // Filter for past events and sort by date (most recent first)
  const sortedCompletedTickets = [...activeTickets]
    .filter(orderGroup => {
      const orderId = Object.keys(orderGroup)[0];
      const event = orderGroup[orderId]?.[0];

      // Skip if no event date
      if (!event?.event_details?.start_date?.date) return false;

      const eventDate = parseISO(event.event_details.start_date.date);
      if (!isValid(eventDate)) return false;

      // Keep only past dates (before today)
      return eventDate.setHours(0, 0, 0, 0) < now.setHours(0, 0, 0, 0);
    })
    .sort((orderGroupA, orderGroupB) => {
      const orderIdA = Object.keys(orderGroupA)[0];
      const orderIdB = Object.keys(orderGroupB)[0];

      const eventA = orderGroupA[orderIdA]?.[0];
      const eventB = orderGroupB[orderIdB]?.[0];

      // Get event dates from event_details.start_date
      const dateA = eventA?.event_details?.start_date?.date
        ? parseISO(eventA.event_details.start_date.date)
        : new Date(0);
      const dateB = eventB?.event_details?.start_date?.date
        ? parseISO(eventB.event_details.start_date.date)
        : new Date(0);

      if (!isValid(dateA) || !isValid(dateB)) return 0;

      // Sort by date (descending - most recent first)
      return dateB.getTime() - dateA.getTime();
    });

  if (error) {
    return <ErrorAlert error={error} />;
  }

  return (
    <div
      className={`${
        pathname === '/reservations'
          ? 'w-full lg:w-[60%] ml-0 mr-auto'
          : 'w-full md:max-w-2xl mx-auto p-4'
      }  space-y-8`}
    >
      <Tabs defaultValue="active" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger
            value="active"
            className="data-[state=active]:bg-mulberry data-[state=active]:text-white data-[state=inactive]:text-gray-800"
          >
            Active
          </TabsTrigger>
          <TabsTrigger
            value="completed"
            className="data-[state=active]:bg-mulberry data-[state=active]:text-white data-[state=inactive]:text-gray-800"
          >
            Completed
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="space-y-4 mb-6">
            {loading ? (
              <TicketListSkeleton />
            ) : sortedActiveTickets.length > 0 ? (
              sortedActiveTickets.map((orderGroup, index) => {
                const orderId = Object.keys(orderGroup)[0];
                return (
                  <OrderCard
                    setShowAlert={setShowAlert}
                    key={orderId || index}
                    order={orderGroup}
                    isFirstCard={index === 0} // Pass true for the first card only
                  />
                );
              })
            ) : null}
          </div>
        </TabsContent>

        <TabsContent value="completed">
          <div className="space-y-4 mb-6">
            {loading ? (
              <TicketListSkeleton />
            ) : sortedCompletedTickets.length > 0 ? (
              sortedCompletedTickets.map((orderGroup, index) => {
                const orderId = Object.keys(orderGroup)[0];
                return (
                  <OrderCard
                    setShowAlert={setShowAlert}
                    key={orderId || index}
                    order={orderGroup}
                    isFirstCard={index === 0} // Pass true for the first card only
                  />
                );
              })
            ) : (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500 font-medium">No completed tickets found.</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Alert Dialog */}
      <AlertDialog open={showAlert} onOpenChange={handleCloseAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <span className="text-red-500 text-xl">⛔</span> Check-In Not Available
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-4 text-sm">
              <p>You are the group organizer and do not have a ticket to attend.</p>
              <p>To ensure a smooth check-in for your group:</p>
              <ol className="list-decimal pl-5 space-y-1">
                <li>Print the Parking Pass.</li>
                <li>Print & Share All Tickets – Guests need their tickets for check-in.</li>
                <li>Guest Check-In – Each adult can check in individually.</li>
                <li>
                  Children Must Be Checked-In by an Adult – A parent or another attending adult must
                  check them in.
                </li>
              </ol>
              <p>Thank you for organizing the visit!</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive"
              onClick={handleCloseAlert}
            >
              Understood
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default withAuth(Ticket);
