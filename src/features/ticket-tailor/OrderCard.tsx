import { <PERSON><PERSON><PERSON><PERSON><PERSON>ider } from '@react-pdf/renderer';
import { CircleArrowDown, ChevronRight, CircleArrowRight } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { Button } from '@/src/components/ui/button';
import { ROUTES, TICKET_TYPES } from '@/src/lib/constants';
import { formatTime, getTicketType } from '@/src/lib/utils';
import { RootState, useAppSelector, useAppDispatch } from '@/src/store';
import { Tickets } from '@/src/types/Tickets';
import { TicketTailorOrder } from '@/src/types/TicketTailor';

import ParkingPassPDF from '../u-pick/parking/ParkingPassPDF';

import { toggleOrderExpansion } from './slice';

const isUserOrderBooker = (order: any, orderId: string, userId?: string) => {
  if (!order || !orderId || !userId) return false;

  // Find the booker's firebase_user_id in the order data
  const bookerFirebaseId = order[orderId]?.[0]?.order?.booker?.firebase_user_id;

  // Compare with current user's firebase_user_id
  return userId === bookerFirebaseId;
};

const OrderCard = ({
  order,
  setShowAlert,
  isFirstCard = false,
}: {
  order: any;
  setShowAlert?: (show: boolean) => void;
  isFirstCard?: boolean;
}) => {
  const orderId = order && Object.keys(order)[0];
  const eventDetails = order?.[orderId]?.[0];
  const router = useRouter();

  const searchParams = useSearchParams();
  const urlOrderId = searchParams.get('orderId');

  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state: RootState) => state.authentication);
  const expandedOrders = useAppSelector((state: RootState) => state.ticketTailor.expandedOrders);

  // Track user-collapsed orders
  const [userCollapsedOrders, setUserCollapsedOrders] = useState<string[]>([]);

  // Move isBooker declaration before the useEffect that uses it
  const isBooker = React.useMemo(() => {
    return isUserOrderBooker(order, orderId, user?.firebase_user_id);
  }, [order, orderId, user?.firebase_user_id]);

  // Auto-expand logic based on URL query param
  useEffect(() => {
    // Don't auto-expand if user manually collapsed this order
    if (userCollapsedOrders.includes(orderId)) return;

    // Expand if this card's orderId matches URL param or if it's the first card and no param exists
    if ((urlOrderId && urlOrderId === orderId) || (!urlOrderId && isFirstCard && isBooker)) {
      if (!expandedOrders.includes(orderId) && isBooker) {
        dispatch(toggleOrderExpansion(orderId));
      }
    }
  }, [
    orderId,
    searchParams,
    isFirstCard,
    isBooker,
    urlOrderId,
    expandedOrders,
    userCollapsedOrders,
  ]);

  // Add this function to navigate to user's ticket
  const navigateToUserTicket = () => {
    // Find the first event ticket (not parking or clamshell) for the current user
    // First try matching by first name, last name, and email
    let eventTicket = order?.[orderId]?.find((ticket: Tickets) => {
      const ticketType = getTicketType(ticket.ticket_type || '');
      return (
        ticketType !== TICKET_TYPES.CLAM_SHELL &&
        ticketType !== TICKET_TYPES.PARKING &&
        ticket?.user_details?.first_name?.toLowerCase() === user?.first_name?.toLowerCase() &&
        ticket?.user_details?.last_name?.toLowerCase() === user?.last_name?.toLowerCase() &&
        ticket?.user_details?.email?.toLowerCase() === user?.email?.toLowerCase()
      );
    });

    // If no match found, try matching just by email
    if (!eventTicket) {
      eventTicket = order?.[orderId]?.find((ticket: Tickets) => {
        const ticketType = getTicketType(ticket.ticket_type || '');
        return (
          ticketType !== TICKET_TYPES.CLAM_SHELL &&
          ticketType !== TICKET_TYPES.PARKING &&
          ticket?.user_details?.email?.toLowerCase() === user?.email?.toLowerCase()
        );
      });
    }

    if (eventTicket?.id) {
      router.push(`/reservations/${eventTicket.id}`);
    } else if (isBooker && setShowAlert) {
      // Show alert dialog when booker doesn't have a ticket
      setShowAlert(true);
    }
  };

  // Use Redux state instead of local state
  const isExpanded = expandedOrders.includes(orderId);

  const [eventTickets, setEventTickets] = useState(0);
  const [parkingPasses, setParkingPasses] = useState(0);
  const [clamshells, setClamshells] = useState(0);

  useEffect(() => {
    // Early return if no order data exists
    if (!order?.[orderId]) return;

    const items = order[orderId]?.[0]?.order?.item;

    // Count different ticket types in a single pass for better performance
    const counts = items?.reduce(
      (acc: { event: number; parking: number; clamshell: number }, item: any) => {
        const type = getTicketType(item.description || '');

        // Categorize tickets based on their type
        if (type === TICKET_TYPES.PARKING) {
          acc.parking += item.quantity;
        } else if (type === TICKET_TYPES.CLAM_SHELL) {
          acc.clamshell += item.quantity;
        } else if (type !== TICKET_TYPES.CLAM_SHELL && type !== TICKET_TYPES.PARKING) {
          // Regular event tickets (not add-ons)
          acc.event += item.quantity;
        }

        return acc;
      },
      { event: 0, parking: 0, clamshell: 0 }
    ) || { event: 0, parking: 0, clamshell: 0 };

    // Update state with counted values
    setEventTickets(counts.event);
    setParkingPasses(counts.parking);
    setClamshells(counts.clamshell);
  }, [order, orderId]);

  const toggleExpand = () => {
    // Allow anyone to expand the card, but still track user actions
    // If currently expanded, add to user-collapsed list
    if (expandedOrders.includes(orderId)) {
      setUserCollapsedOrders(prev => [...prev, orderId]);
    } else {
      // If expanding, remove from user-collapsed list
      setUserCollapsedOrders(prev => prev.filter(id => id !== orderId));
    }
    dispatch(toggleOrderExpansion(orderId));
  };

  // Memoized ticket summary to avoid recalculation on every render
  const ticketSummary = React.useMemo(() => {
    const parts = [];

    // Add event tickets to summary if any exist
    if (eventTickets > 0) {
      parts.push(`${eventTickets} event ${eventTickets === 1 ? 'ticket' : 'tickets'}`);
    }

    // Add parking passes to summary if any exist
    if (parkingPasses > 0) {
      parts.push(`${parkingPasses} parking ${parkingPasses === 1 ? 'pass' : 'passes'}`);
    }

    // Add clamshells to summary if any exist
    if (clamshells > 0) {
      parts.push(
        `${clamshells} ${clamshells === 1 ? TICKET_TYPES.CLAM_SHELL : 'clamshells'} prepaid`
      );
    }

    // Join all parts with commas
    return parts.join(', ');
  }, [eventTickets, parkingPasses, clamshells]);

  // const navigateToWaivers = ({ orderId }: { orderId: string }) => {
  //   router.push(`/reservations/waivers/${orderId}`);
  // };

  return (
    <div className="border rounded-md overflow-hidden mb-6">
      <div
        className={`flex justify-between items-center p-2 sm:p-4 
          cursor-pointer hover:bg-mulberryLighter
          bg-mulberryLightest transition-coslors duration-200`}
        onClick={toggleExpand}
      >
        <div>
          <h3 className="text-gray-500 text-xs sm:text-sm">{eventDetails.event_details.name}</h3>
          <h2 className="font-semibold text-sm sm:text-base">
            {eventDetails.event_details.start_date.formatted} -{' '}
            {formatTime(eventDetails.event_details.end_date.time)}
          </h2>
          <span className="inline-flex items-center rounded-md border border-mulberry px-2 py-0.5 text-xs font-medium bg-white text-mulberry shadow-sm mb-1">
            {isBooker ? (
              <>#{orderId}</>
            ) : (
              <>
                #
                {
                  // First try matching by first name, last name, and email
                  order?.[orderId]?.find(
                    (ticket: Tickets) =>
                      ticket?.user_details?.first_name?.toLowerCase() ===
                        user?.first_name?.toLowerCase() &&
                      ticket?.user_details?.last_name?.toLowerCase() ===
                        user?.last_name?.toLowerCase() &&
                      ticket?.user_details?.email?.toLowerCase() === user?.email?.toLowerCase()
                  )?.barcode ||
                    // If no match found, try matching just by email
                    order?.[orderId]?.find(
                      (ticket: Tickets) =>
                        ticket?.user_details?.email?.toLowerCase() === user?.email?.toLowerCase()
                    )?.barcode
                }
              </>
            )}
          </span>
        </div>
        <div className="transition-transform duration-300">
          {isExpanded ? (
            <CircleArrowDown
              size={36}
              className="sm:w-12 sm:h-12"
              color="#892b76"
              strokeWidth={1.5}
            />
          ) : (
            <CircleArrowRight
              size={36}
              className="sm:w-12 sm:h-12 cursor-pointer"
              color="#892b76"
              strokeWidth={1.5}
            />
          )}
        </div>
      </div>

      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out relative ${
          isExpanded ? 'h-auto opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        {/* Vertical line connecting the circles - positioned relative to first and last step */}
        <div
          className="absolute left-6 sm:left-8 w-0.5 bg-mulberry z-40"
          style={{
            top: 'calc(2.5rem - 4px)' /* Position after first step circle */,
            height: 'calc(100% - 6rem)' /* End before last step circle */,
          }}
        ></div>

        {/* Replace all static steps with dynamically numbered steps */}
        {(() => {
          let stepCounter = 1;
          return (
            <>
              {isBooker && (
                <div className="p-3 sm:p-4 border-t flex items-center justify-between relative z-40">
                  <div className="flex">
                    <span className="flex items-center justify-center bg-mulberry text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 min-w-6 min-h-6 sm:min-w-8 sm:min-h-8 mr-2 sm:mr-3 text-xs sm:text-base font-semibold">
                      {stepCounter++}
                    </span>
                    <div>
                      <h4 className="font-semibold mb-1 sm:mb-2 text-sm sm:text-base">
                        Event Booking
                      </h4>
                      <p className="text-gray-600 text-xs sm:text-sm">Order #{orderId}</p>
                      <p className="text-gray-600 text-xs sm:text-sm">{ticketSummary}</p>
                    </div>
                  </div>
                  {/* <ChevronRight ... /> */}
                </div>
              )}

              {/* <div className="p-3 sm:p-4 border-t flex items-center justify-between relative ">
                <div className="flex">
                  <span className="flex items-center justify-center relative z-50 bg-mulberry text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 min-w-6 min-h-6 sm:min-w-8 sm:min-h-8 mr-2 sm:mr-3 text-xs sm:text-base font-semibold">
                    {stepCounter++}
                  </span>
                  <div>
                    <h4 className="font-semibold mb-1 sm:mb-2 text-sm sm:text-base">
                      Farm Waivers
                    </h4>
                    <p className="text-gray-600 text-xs sm:text-sm">
                      {
                        order[orderId]?.filter((ticket: Tickets) => {
                          const ticketType = getTicketType(ticket.ticket_type || '');
                          return (
                            ticket.hasValidWaiver &&
                            ticketType !== TICKET_TYPES.CLAM_SHELL &&
                            ticketType !== TICKET_TYPES.PARKING
                          );
                        }).length
                      }{' '}
                      of{' '}
                      {
                        order[orderId]?.filter((ticket: Tickets) => {
                          const ticketType = getTicketType(ticket.ticket_type || '');
                          return (
                            ticketType !== TICKET_TYPES.CLAM_SHELL &&
                            ticketType !== TICKET_TYPES.PARKING
                          );
                        }).length
                      }{' '}
                      required waivers signed
                    </p>
                  </div>
                </div>
                <ChevronRight
                  size={30}
                  className="cursor-pointer sm:w-[50px] sm:h-[50px] w-[30px] h-[30px] flex-shrink-0"
                  color="#892b76"
                  strokeWidth={1.5}
                  onClick={() => navigateToWaivers({ orderId })}
                />
              </div> */}

              <div className="p-3 sm:p-4 border-t flex items-center justify-between relative ">
                <div className="flex">
                  <span className="flex items-center justify-center bg-mulberry relative z-50 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 min-w-6 min-h-6 sm:min-w-8 sm:min-h-8 mr-2 sm:mr-3 text-xs sm:text-base font-semibold">
                    {stepCounter++}
                  </span>
                  <div>
                    <h4 className="font-semibold mb-1 sm:mb-2 text-sm sm:text-base">
                      Farm Visitor Guidelines and Instructions
                    </h4>
                    <p className="text-gray-600 text-xs sm:text-sm">
                      Please review and acknowledge before your farm visit.
                    </p>
                  </div>
                </div>
                <ChevronRight
                  size={30}
                  className="cursor-pointer sm:w-[50px] sm:h-[50px] w-[30px] h-[30px] flex-shrink-0"
                  color="#892b76"
                  strokeWidth={1.5}
                  onClick={() => router.push(ROUTES.FARM_VISIT_INSTRUCTIONS)}
                />
              </div>

              {parkingPasses > 0 && isBooker && (
                <div className="p-3 sm:p-4 border-t flex items-center justify-between relative">
                  <div className="flex flex-1 mr-2 sm:mr-4">
                    <span className="flex items-center justify-center bg-mulberry relative z-50 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 min-w-6 min-h-6 sm:min-w-8 sm:min-h-8 mr-2 sm:mr-3 text-xs sm:text-base font-semibold">
                      {stepCounter++}
                    </span>
                    <div>
                      <h4 className="font-semibold mb-1 sm:mb-2 text-sm sm:text-base">
                        Reserved VIP Prepaid Parking
                      </h4>
                      <p className="text-gray-600 text-xs sm:text-sm">
                        Have your Reserved VIP Prepaid Parking pass available as you enter the
                        Reserved VIP Prepaid Parking area.
                      </p>
                    </div>
                  </div>
                  <BlobProvider
                    document={
                      <ParkingPassPDF
                        orders={order?.[orderId]?.filter((order: TicketTailorOrder) => {
                          const ticketType = getTicketType(order.ticket_type || '');
                          return ticketType === TICKET_TYPES.PARKING;
                        })}
                      />
                    }
                  >
                    {({ url }) => (
                      <Button
                        className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-xs sm:text-sm w-[120px] sm:w-[150px] min-w-[120px] sm:min-w-[150px] h-8 sm:h-10 px-2 sm:px-4"
                        onClick={() => url && window.open(url, '_blank')}
                        disabled={!url}
                      >
                        View
                      </Button>
                    )}
                  </BlobProvider>
                </div>
              )}

              <div className="p-3 sm:p-4 border-t flex items-center justify-between relative">
                <div className="flex flex-1 mr-2 sm:mr-4">
                  <span className="flex items-center justify-center bg-mulberry relative z-50 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 min-w-6 min-h-6 sm:min-w-8 sm:min-h-8 mr-2 sm:mr-3 text-xs sm:text-base font-semibold">
                    {stepCounter++}
                  </span>
                  <div>
                    <h4 className="font-semibold mb-1 sm:mb-2 text-sm sm:text-base">
                      Check-in at Farm
                    </h4>
                    <p className="text-gray-600 text-xs sm:text-sm">
                      Check in at farm gate individually or as a group.
                    </p>
                  </div>
                </div>
                <Button
                  className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-xs sm:text-sm w-[120px] sm:w-[150px] min-w-[120px] sm:min-w-[150px] h-8 sm:h-10 px-2 sm:px-4"
                  onClick={navigateToUserTicket}
                >
                  Check In
                </Button>
              </div>

              {clamshells > 0 && (
                <div className="p-3 sm:p-4 border-t flex items-center justify-between relative">
                  <div className="flex flex-1 mr-2 sm:mr-4">
                    <span className="flex items-center relative z-50 justify-center bg-mulberry text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 min-w-6 min-h-6 sm:min-w-8 sm:min-h-8 mr-2 sm:mr-3 text-xs sm:text-base font-semibold">
                      {stepCounter++}
                    </span>
                    <div>
                      <h4 className="font-semibold mb-1 sm:mb-2 text-sm sm:text-base">
                        Purchase Mulberries at Farm
                      </h4>
                      <p className="text-gray-600 text-xs sm:text-sm">
                        Pay for the mulberries you pick and purchased additional
                      </p>
                    </div>
                  </div>
                  {/* Conditionally render Pay or Redeem button based on clamshells value */}
                  {!clamshells ? (
                    <Button
                      className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-xs sm:text-sm w-[120px] sm:w-[150px] min-w-[120px] sm:min-w-[150px] h-8 sm:h-10 px-2 sm:px-4"
                      onClick={() =>
                        window.open(process.env.NEXT_PUBLIC_CHECKOUT_STORE_URL || '', '_blank')
                      }
                    >
                      Purchase...
                    </Button>
                  ) : (
                    <Button
                      className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-xs sm:text-sm w-[120px] sm:w-[150px] min-w-[120px] sm:min-w-[150px] h-8 sm:h-10 px-2 sm:px-4"
                      onClick={() => router.push(ROUTES.CHECKOUT(orderId))}
                    >
                      Redeem Mulberries
                    </Button>
                  )}
                </div>
              )}
            </>
          );
        })()}
      </div>
      {/* Remove the AlertDialog from here */}
    </div>
  );
};

export default OrderCard;
