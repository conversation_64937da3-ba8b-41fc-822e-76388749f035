import { getAuth } from 'firebase/auth';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import Spinner from '@/src/components/spinner';
import { setLastVisitedPath } from '@/src/features/auth/slices/authenticationSlice';
import { useAppDispatch } from '@/src/store';

import Login from '../components/Login';

/**
 * Higher-order component that handles authentication protection for routes
 * @template P - Props of the wrapped component
 * @param {ComponentType<P>} WrappedComponent - Component to be wrapped with authentication
 * @returns {ComponentType<P>} Wrapped component with authentication protection
 */
const withAuth = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  return function WithAuthComponent(props: P) {
    const router = useRouter();

    // Check for token in URL query params
    const accessToken =
      typeof window !== 'undefined'
        ? new URLSearchParams(window.location.search).get('token')
        : null;

    const dispatch = useAppDispatch();
    const [isChecking, setIsChecking] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const auth = getAuth();

    useEffect(() => {
      const unsubscribe = auth.onAuthStateChanged(async user => {
        if (user) {
          setIsAuthenticated(true);
          setIsChecking(false);
        } else if (accessToken && window.location.pathname.includes('/reservations/')) {
          // Allow access with token for reservation pages
          setIsAuthenticated(true);
          setIsChecking(false);
        } else {
          // Get the full URL including query parameters
          const currentPath = window.location.href.split(window.location.origin)[1];

          // Store the current path in Redux as the next URL
          dispatch(setLastVisitedPath(currentPath));

          // Get the previous URL
          const prevUrl = document.referrer ? new URL(document.referrer).pathname : '/';

          // Redirect to login with both nextUrl and prevUrl
          const encodedNextUrl = encodeURIComponent(currentPath);
          const encodedPrevUrl = encodeURIComponent(prevUrl);
          router.replace(`/auth/login?nextUrl=${encodedNextUrl}&prevUrl=${encodedPrevUrl}`);
        }
        setIsChecking(false);
      });

      return () => unsubscribe();
    }, [auth, accessToken]);

    if (isChecking) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <Spinner />
        </div>
      );
    }

    return isAuthenticated ? <WrappedComponent {...props} /> : <Login />;
  };
};

export default withAuth;
