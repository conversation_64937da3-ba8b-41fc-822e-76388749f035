'use client';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import Spinner from '@/src/components/spinner';
import { selectUser } from '@/src/features/auth/slices/authenticationSlice';
import { ROUTES, USER_ROLES } from '@/src/lib/constants';
import { RootState, useAppSelector } from '@/src/store';

/**
 * Higher-order component that handles authentication protection for admin routes
 * @template P - Props of the wrapped component
 * @param {ComponentType<P>} WrappedComponent - Component to be wrapped with admin authentication
 * @returns {ComponentType<P>} Wrapped component with admin authentication protection
 */
const withAdminAuth = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  return function WithAdminAuthComponent(props: P) {
    const router = useRouter();
    const [isChecking, setIsChecking] = useState(true);
    const user = useAppSelector(selectUser);
    const isAuthenticated = useAppSelector(
      (state: RootState) => state.authentication.isAuthenticated
    );
    const authLoading = useAppSelector((state: RootState) => state.authentication.loading);

    useEffect(() => {
      if (!authLoading) {
        if (!isAuthenticated) {
          // Redirect to login if not authenticated
          router.replace(ROUTES.LOGIN + '?nextUrl=' + encodeURIComponent('/admin/login'));
        } else if (user && user.role !== USER_ROLES.ADMIN) {
          // Redirect to home if authenticated but not admin
          router.replace('/');
        }
        setIsChecking(false);
      }
    }, [isAuthenticated, user, router, authLoading]);

    if (isChecking || authLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <Spinner />
        </div>
      );
    }

    // Only render the component if user is authenticated and has admin role
    return isAuthenticated && user && user.role === USER_ROLES.ADMIN ? (
      <WrappedComponent {...props} />
    ) : null;
  };
};

export default withAdminAuth;
