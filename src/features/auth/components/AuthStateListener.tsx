'use client';

import { onAuthStateChanged } from 'firebase/auth';
import { useEffect, useRef } from 'react';

import { auth } from '@/src/lib/firebaseConfig';
import { useAppDispatch } from '@/src/store';

import { syncUserData, setLoading, setAuthentication } from '../slices/authenticationSlice';

export default function AuthStateListener({ children }: { children: React.ReactNode }) {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();
  const initialLoadComplete = useRef(false);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    // Only set loading to true on initial mount, not on subsequent auth state changes
    if (!initialLoadComplete.current) {
      dispatch(setLoading(true));
      initialLoadComplete.current = true;
    }

    const unsubscribe = onAuthStateChanged(auth, async user => {
      if (user) {
        // User is signed in, sync their data
        dispatch(setAuthentication(true));
        await dispatch(syncUserData(user));
        // Loading will be set to false in the syncUserData.fulfilled reducer
      } else {
        // User is signed out
        dispatch(setLoading(false));
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [dispatch]);
  /**
   * End Lifecycle Methods
   */

  return <>{children}</>;
}
