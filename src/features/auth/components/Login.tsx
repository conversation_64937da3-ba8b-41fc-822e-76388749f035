import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import ErrorAlert from '@/src/components/error-alert';
import Footer from '@/src/components/footer';
import { Button } from '@/src/components/ui/button';
import { VM_EMAIL } from '@/src/lib/constants';
import { useAppDispatch, useAppSelector } from '@/src/store';

import VMLOGO from '../../../../public/vmicon.png';
import {
  clearMagicLinkSent,
  selectAuthError,
  selectIsAuthenticated,
  selectLastVisitedPath,
  selectMagicLinkSent,
  sendMagicLink,
  signInWithGoogle,
} from '../slices/authenticationSlice';

export default function Login() {
  /**
   * Start Initials
   */
  const [email, setEmail] = useState('');
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();

  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  // const isLoading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const magicLinkSent = useAppSelector(selectMagicLinkSent);
  const lastVisitedPath = useAppSelector(selectLastVisitedPath);
  const returnUrl = searchParams.get('returnUrl');
  const nextUrl = searchParams.get('nextUrl');
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    if (isAuthenticated) {
      const redirectPath = returnUrl || lastVisitedPath || '/';
      router.replace(redirectPath);
    }
  }, [isAuthenticated, returnUrl, lastVisitedPath, router]);

  // Reset magic link state when navigating away
  useEffect(() => {
    // Handle browser back button
    const handlePopState = () => {
      if (magicLinkSent) {
        dispatch(clearMagicLinkSent());
      }
    };

    // Add event listener for back/forward buttons
    window.addEventListener('popstate', handlePopState);

    // Cleanup function
    return () => {
      window.removeEventListener('popstate', handlePopState);

      // Reset magic link state when component unmounts
      if (magicLinkSent) {
        dispatch(clearMagicLinkSent());
      }
    };
  }, [dispatch, magicLinkSent]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const handleMagicLinkSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await dispatch(
      sendMagicLink({
        email,
        returnUrl: nextUrl || returnUrl || lastVisitedPath || '/',
      })
    );
  };

  const handleGoogleSignIn = async () => {
    await dispatch(signInWithGoogle());
  };

  // const handleClose = () => {
  //   dispatch(clearError());
  //   setEmail('');
  // };
  /**
   * End Custom Methods
   */

  // if (magicLinkSent)
  //   return (
  //     <div>
  //       <p className="mb-1">
  //         We have sent a magic link to <strong>{email}</strong>
  //       </p>
  //       <p>Click the link in the email to sign in.</p>
  //     </div>
  //   );

  return (
    <div className="flex flex-col min-h-screen bg-mulberry">
      <main className="flex-grow">
        <div className="flex justify-center py-12 bg-white">
          <div className="w-32 h-32 relative">
            <Image src={VMLOGO} alt="Very Mulberry Logo" fill className="object-contain" priority />
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-mulberryLightest py-12 max-md:px-2">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center max-md:text-2xl">
            One place for everything Very Mulberry.
          </h1>
          {magicLinkSent ? (
            <div className="flex justify-center items-center  p-4">
              <div className="w-full max-w-3xl bg-white rounded-lg shadow-md border border-gray-200">
                <div className="p-6">
                  {/* Success Alert */}
                  <div className="mb-6 bg-[#d3ebd2] border border-[#d3ebd2] rounded-md p-4 flex items-start">
                    <div>
                      <p className="text-green-700">
                        Your request for a Sign In link was successful.
                      </p>
                    </div>
                  </div>

                  {/* What happens next section */}
                  <div className="mb-8">
                    <h2 className=" font-semibold text-black mb-4">What happens next?</h2>
                    <ul className="space-y-1 mb-4 text-sm">
                      <li className="flex gap-2">
                        <span className="font-bold text-gray-700">•</span>
                        <span>
                          If <span className="font-bold text-black ">{email}</span> matches any
                          U-Pick members, you&apos;ll get a unique Sign In link via email.
                        </span>
                      </li>
                      <li className="flex gap-2">
                        <span className="font-bold text-gray-700">•</span>
                        <span>
                          Check your inbox, open the link, and manage your U-Pick details.
                        </span>
                      </li>
                    </ul>
                  </div>

                  {/* Troubleshooting section */}
                  <div>
                    <h2 className="font-semibold text-black  mb-4">
                      If you haven&apos;t received your email
                    </h2>
                    <ol className="list-decimal pl-5 text-sm  space-y-2 mb-6">
                      <li>
                        Ensure you used the email address{' '}
                        <span className="font-bold text-black">{email}</span>
                        <span className=" text-sm ml-1">that was used in the U-Pick booking.</span>
                      </li>
                      <li>Allow up to 5 minutes for the email to arrive.</li>
                      <li>Check your spam or junk folders for the email.</li>
                      <li>
                        {`Search your email inbox for "Very Mulberry" if you don't see the email.`}
                      </li>
                    </ol>

                    <p className="text-black text-sm">
                      If the above steps don&apos;t work, please contact support at{' '}
                      <a href={VM_EMAIL} className="text-mulberry hover:underline">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              <p className="text-lg mb-4 text-center">
                Need to manage your U-Pick event details? We’ll send you a unique link to access
                your account.
              </p>
              <div className="relative rounded-3xl mx-auto w-full max-w-md p-6 m-4">
                <>
                  <h2 className="text-2xl font-bold text-center mb-6">
                    {magicLinkSent ? 'Check your email' : ''}
                  </h2>

                  {error && <ErrorAlert error={error} />}

                  <div className="">
                    <form onSubmit={handleMagicLinkSubmit} className="space-y-4">
                      <div>
                        <label
                          htmlFor="email"
                          className="block text-sm font-medium text-black mb-1 text-left"
                        >
                          Please enter the email address used for the U-Pick event.
                        </label>
                        <input
                          id="email"
                          type="email"
                          required
                          className="w-full px-3 text-[##E1E1E1] shadow-none py-2 border border-[##E1E1E1] rounded-[8px] focus:outline-none focus:ring-mulberry focus:border-mulberry"
                          placeholder="Enter your email"
                          value={email}
                          onChange={e => setEmail(e.target.value)}
                        />
                      </div>

                      <Button
                        type="submit"
                        className="w-full  bg-mulberry font-semibold  rounded-[8px]  hover:bg-mulberryHover"
                      >
                        Send Me a Sign In Link
                      </Button>
                    </form>

                    <div className="relative my-[17px]">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-[#C5C5C5]" />
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="px-8 bg-mulberryLightest text-[#C5C5C5]">Or</span>
                      </div>
                    </div>

                    <Button
                      type="button"
                      onClick={handleGoogleSignIn}
                      className="w-full border shadow-none border-[#E1E1E1] bg-white text-black font-medium rounded-[8px]  hover:bg-gray-50 flex items-center justify-center gap-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="" viewBox="0 0 48 48">
                        <path
                          fill="#EA4335"
                          d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
                        />
                        <path
                          fill="#4285F4"
                          d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
                        />
                        <path
                          fill="#FBBC05"
                          d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
                        />
                        <path
                          fill="#34A853"
                          d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
                        />
                        <path fill="none" d="M0 0h48v48H0z" />
                      </svg>
                      Sign In with Google
                    </Button>
                  </div>
                </>
              </div>
            </>
          )}
        </div>
      </main>

      {/* Footer */}
      <Footer isAuthenticated={isAuthenticated} />
    </div>
  );
}
