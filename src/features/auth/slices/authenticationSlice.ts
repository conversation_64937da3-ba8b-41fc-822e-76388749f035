import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  isSignInWithEmailLink,
  signInWithEmailLink,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  signInWithCustomToken,
  User as FirebaseUser,
} from 'firebase/auth';

import { apiPost, apiPutFormData } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS } from '@/src/lib/constants';
import { auth } from '@/src/lib/firebaseConfig';
import { getFirebaseToken } from '@/src/lib/utils';
import { RootState } from '@/src/store';
import { Users } from '@/src/types/Users';

interface AuthState {
  user: Users | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  magicLinkSent: boolean;
  lastVisitedPath: string | null;
  tempProfileImage: string | null;
  manualLogout: boolean;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  loading: true,
  error: null,
  magicLinkSent: false,
  lastVisitedPath: null,
  tempProfileImage: null,
  manualLogout: false,
};

const formatUserData = (
  firebaseUser: FirebaseUser,
  additionalData?: Users,
  provider?: 'google' | 'email' | 'facebook' | 'apple' | undefined
): Users => ({
  firebase_user_id: firebaseUser.uid,
  email: firebaseUser.email || '',
  phone: additionalData?.phone || firebaseUser.phoneNumber || '',
  role: additionalData?.role || 'customer',
  first_name: additionalData?.first_name || firebaseUser.displayName?.split(' ')[0] || '',
  last_name: additionalData?.last_name || firebaseUser.displayName?.split(' ')[1] || '',
  profile_url: additionalData?.profile_url || firebaseUser.photoURL || '',
  age_group: additionalData?.age_group || 'adult',
  login_provider: additionalData?.login_provider || provider,
  agree_terms: additionalData?.agree_terms || null,
  opt_in_email: additionalData?.opt_in_email || null,
  marketing_consent: false,
});

// Async Actions
export const sendMagicLink = createAsyncThunk(
  'auth/sendMagicLink',
  async (
    {
      email,
      returnUrl,
    }: {
      email: string;
      returnUrl?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      // Send the magic link email using our SendGrid API .
      const response = await apiPost(
        API_ENDPOINTS.SEND_MAGIC_LINK,
        {
          email,
          returnUrl,
        },
        {
          // Enable replay protection
          useLimitedUseToken: true,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      return email;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to send magic link');
    }
  }
);

export const verifyMagicLink = createAsyncThunk(
  'auth/verifyMagicLink',
  async ({ email }: { email: string }, { rejectWithValue }) => {
    try {
      if (!isSignInWithEmailLink(auth, window.location.href)) {
        throw new Error('Invalid magic link');
      }

      const result = await signInWithEmailLink(auth, email, window.location.href);
      const { user } = result;

      // Call API endpoint to find user by email or firebase_user_id
      return user;
    } catch (error: any) {
      return rejectWithValue(
        error.message === 'Invalid magic link'
          ? 'This login link is invalid or has expired. Please request a new one.'
          : 'Failed to verify magic link. Please try again.'
      );
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // Set a flag to indicate this is a manual logout
      dispatch(setManualLogout(true));
      await signOut(auth);

      // Redirect to home page after logout
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }

      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to logout');
    }
  }
);

export const syncUserData = createAsyncThunk(
  'auth/syncUserData',
  async (user: FirebaseUser, { rejectWithValue }) => {
    try {
      // Call API endpoint to find or create user
      const response = await fetch(API_ENDPOINTS.SYNC_USER_DATA, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          phoneNumber: user.phoneNumber,
          photoURL: user.photoURL,
          provider: 'google', // Default provider, can be adjusted as needed
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      const { data } = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to sync user data');
    }
  }
);

export const signInWithGoogle = createAsyncThunk(
  'auth/signInWithGoogle',
  async (_, { rejectWithValue }) => {
    try {
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);
      const { user } = result;

      // Call API endpoint to find user by email or firebase_user_id
      const response = await fetch(API_ENDPOINTS.FIND_USER_BY_AUTH, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          uid: user.uid,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      const { data } = await response.json();

      if (data) {
        const userData = formatUserData(user, data as Users);
        return {
          ...userData,
          id: data.id,
        };
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to sign in with Google');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'auth/updateUserProfile',
  async (
    {
      userId,
      phone,
      firstName,
      lastName,
      profileImage,
      marketingConsent,
    }: {
      userId: string;
      phone?: string;
      firstName?: string;
      lastName?: string;
      profileImage?: File;
      marketingConsent?: boolean;
    },
    { rejectWithValue }
  ) => {
    try {
      const token = await getFirebaseToken();
      if (!token) throw new Error('Authentication token not found.');

      // Create form data for multipart request if there's an image
      const formData = new FormData();
      formData.append('userId', userId);
      if (phone) formData.append('phone', phone);
      if (firstName) formData.append('firstName', firstName);
      if (lastName) formData.append('lastName', lastName);
      if (profileImage) formData.append('profileImage', profileImage);
      if (marketingConsent !== undefined)
        formData.append('marketingConsent', marketingConsent.toString());

      // Update profile via API endpoint using apiPutFormData
      const response = await apiPutFormData(API_ENDPOINTS.UPDATE_PROFILE, formData, token, {
        // Enable replay protection
        useLimitedUseToken: true,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      const { data } = await response.json();

      // Return the data from the API response
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update user profile');
    }
  }
);

export const impersonateUser = createAsyncThunk(
  'auth/impersonateUser',
  async (
    {
      email,
    }: {
      email: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const token = await getFirebaseToken();
      if (!token) throw new Error('Authentication token not found.');

      const response = await apiPost(
        API_ENDPOINTS.IMPOSONATE_USER,
        { email },
        {
          headers: { Authorization: `Bearer ${token}` },
          useLimitedUseToken: true, // Enable replay protection
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      const { token: customToken } = await response.json();

      return customToken;
    } catch (error: any) {
      return rejectWithValue(error.message || 'failed to impersonate user');
    }
  }
);

const authenticationSlice = createSlice({
  name: 'authentication',
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
      state.loading = false;
      state.error = null;
    },
    setAuthentication: (state, _action) => {
      state.isAuthenticated = true;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    clearError: state => {
      state.error = null;
    },
    clearMagicLinkSent: state => {
      state.magicLinkSent = false;
    },
    setLastVisitedPath: (state, action) => {
      state.lastVisitedPath = action.payload;
    },
    setTempProfileImage: (state, action) => {
      state.tempProfileImage = action.payload;
    },
    setManualLogout: (state, action) => {
      state.manualLogout = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(sendMagicLink.pending, state => {
        state.loading = true;
        state.error = null;
        state.magicLinkSent = false;
      })
      .addCase(sendMagicLink.fulfilled, state => {
        state.loading = false;
        state.magicLinkSent = true;
        state.error = null;
      })
      .addCase(sendMagicLink.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.magicLinkSent = false;
      })
      .addCase(verifyMagicLink.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyMagicLink.fulfilled, (state, _action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.error = null;
        state.magicLinkSent = false;
      })
      .addCase(verifyMagicLink.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      })
      .addCase(logoutUser.pending, state => {
        state.loading = true;
      })
      .addCase(logoutUser.fulfilled, state => {
        state.user = null;
        state.isAuthenticated = false;
        state.loading = false;
        state.error = null;
        state.magicLinkSent = false;
        state.lastVisitedPath = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(syncUserData.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(syncUserData.fulfilled, (state, action) => {
        state.user = action.payload;
        state.isAuthenticated = true;
        state.loading = false;
        state.error = null;
      })
      .addCase(syncUserData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.user = null;
      })
      .addCase(signInWithGoogle.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(signInWithGoogle.fulfilled, (state, _action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(signInWithGoogle.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        if (state.user && action.payload.userId === state.user.id) {
          state.user = {
            ...state.user,
            ...action.payload,
          };
          // Clear temporary profile image after successful upload
          state.tempProfileImage = null;
        }
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.error = action.payload as string;
        state.loading = false;
      })
      .addCase(impersonateUser.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(impersonateUser.fulfilled, (state, action) => {
        const userToken = action.payload;
        signInWithCustomToken(auth, userToken);
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(impersonateUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setUser,
  setLoading,
  clearError,
  clearMagicLinkSent,
  setLastVisitedPath,
  setAuthentication,
  setTempProfileImage,
  setManualLogout,
} = authenticationSlice.actions;

export default authenticationSlice.reducer;

// Selectors
export const selectUser = (state: RootState) => state.authentication.user;
export const selectIsAuthenticated = (state: RootState) => state.authentication.isAuthenticated;
export const selectAuthLoading = (state: RootState) => state.authentication.loading;
export const selectAuthError = (state: RootState) => state.authentication.error;
export const selectMagicLinkSent = (state: RootState) => state.authentication.magicLinkSent;
export const selectLastVisitedPath = (state: RootState) => state.authentication.lastVisitedPath;
export const selectTempProfileImage = (state: RootState) => state.authentication.tempProfileImage;
