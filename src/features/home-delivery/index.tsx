import {
  format,
  parseISO,
  isValid,
  startOfToday,
  isBefore,
  isAfter,
  addDays,
  eachDayOfInterval,
} from 'date-fns';
import { Clock, MapPin, MapPinHouse } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import Spinner from '@/src/components/spinner';
import { Badge } from '@/src/components/ui/badge';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { ROUTES } from '@/src/lib/constants';
import { convertToPacificTime, getPacificTimeNow } from '@/src/lib/timezone';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

import { fetchAllDeliveryZones } from '../subscription/slices/deliveryZoneSlice';

export default function HomeDelivery() {
  const { zones, loading } = useAppSelector((state: RootState) => state.deliveryZone);
  const dispatch = useAppDispatch();
  const router = useRouter();

  useEffect(() => {
    if (zones.length === 0 && !loading) {
      dispatch(fetchAllDeliveryZones());
    }
  }, [dispatch, zones.length, loading]);

  // Process zones to show delivery schedule
  const deliverySchedule = (() => {
    const schedule: { date: Date; zones: typeof zones }[] = [];
    const today = convertToPacificTime(startOfToday());

    // Only process zones with required fields
    const validZones = zones.filter(
      zone =>
        zone.start_date &&
        isValid(parseISO(zone.start_date)) &&
        zone.delivery_days &&
        zone.delivery_days.length > 0 &&
        zone.delivery_window
    );

    if (validZones.length === 0) return [];

    // Find the latest end date among all zones
    const latestEndDate = validZones.reduce(
      (latest, zone) => {
        if (!zone.end_date) return latest;
        const endDate = parseISO(zone.end_date);
        return isAfter(endDate, latest) ? endDate : latest;
      },
      parseISO(validZones[0]?.end_date || '') || addDays(today, 365)
    ); // Default to 1 year if no end dates

    // Generate all days from today until the latest end date
    const allDays = eachDayOfInterval({
      start: today,
      end: latestEndDate,
    });

    // For each day, check which zones deliver on that day
    allDays.forEach(date => {
      // Skip past dates
      if (isBefore(date, today)) return;

      const dayName = format(date, 'EEEE').toLowerCase();

      // Find zones delivering on this day
      const zonesForDay = validZones.filter(zone => {
        const startDate = zone.start_date
          ? convertToPacificTime(parseISO(zone.start_date))
          : getPacificTimeNow();
        const endDate = zone.end_date ? convertToPacificTime(parseISO(zone.end_date)) : null;

        // Check if date is within zone's active period
        const isInDateRange = !isBefore(date, startDate) && (!endDate || !isAfter(date, endDate));

        // Check if zone delivers on this day of week
        const deliversOnDay = zone.delivery_days.some(
          (day: string) => day.toLowerCase() === dayName
        );

        return isInDateRange && deliversOnDay;
      });

      if (zonesForDay.length > 0) {
        schedule.push({ date, zones: zonesForDay });
      }
    });

    return schedule;
  })();

  if (loading && zones.length === 0) {
    return (
      <div className="min-h-0 max-w-5xl mb-6 m-auto flex justify-center items-center py-8">
        <Spinner />
      </div>
    );
  }

  // If no delivery schedule
  if (deliverySchedule.length === 0) {
    return (
      <div className="min-h-0 max-w-5xl mb-6 mx-0 sm:m-auto max-md:p-0">
        <Card className="border rounded-lg shadow-sm mb-4 p-2 mt-5 md:p-4">
          <CardContent className="text-center py-8 p-0">
            <h3 className="text-mulberry font-bold md:text-xl max-md: text-lg mb-2">
              We don&apos;t have any upcoming delivery zones scheduled at the moment.
            </h3>
            <p className="text-gray-600 mb-4 text-sm">
              Get sweet, juicy mulberries delivered to your door. Subscribe now!
            </p>
            <Button
              onClick={() => router.push(ROUTES.SUBSCRIPTION)}
              className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive rounded-full text-white px-6 py-2"
            >
              Subscribe
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-0 max-w-5xl mb-8 mx-0 sm:m-auto">
      {deliverySchedule.map(({ date, zones }) => (
        <div key={format(date, 'yyyy-MM-dd')}>
          {/* Date separator with visual divider */}
          <div className="relative flex items-center my-6">
            <div className="flex-grow border-t border-gray-300" />
            <div className="mx-4 bg-white px-4 py-1 text-gray-800 font-semibold rounded-full shadow-md border border-gray-200">
              {format(date, 'EEEE, MMMM d, yyyy')}
            </div>
            <div className="flex-grow border-t border-gray-300" />
          </div>

          {zones.map(zone => (
            <Card key={zone.id} className="border p-4 shadow-sm mb-6">
              <CardHeader className="flex flex-col sm:flex-row items-start justify-between p-0 gap-3">
                <div className="text-left">
                  <CardTitle className="text-lg sm:text-xl items-center gap-2 sm:gap-3 flex flex-wrap font-bold">
                    {zone.zone_name}{' '}
                    <span className="text-gray-800 font-medium text-xs sm:text-sm">
                      (Includes portions of {zone.zip_codes?.join(', ')})
                    </span>
                  </CardTitle>
                </div>

                <button
                  onClick={() => router.push(ROUTES.SUBSCRIPTION)}
                  className="px-3 py-1 hidden md:flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                >
                  <MapPinHouse className="h-3 w-3" />
                  Explore Home Delivery
                </button>
              </CardHeader>
              <CardContent className="text-left p-0">
                <div className="flex flex-col gap-1 items-start">
                  <Badge
                    variant="secondary"
                    className="font-normal mb-2 max-md:mt-2 w-fit bg-mulberryLight/50 text-mulberry hover:bg-mulberryLight border-mulberry/50"
                  >
                    Home Delivery
                  </Badge>

                  <div className="flex text-sm items-center gap-2">
                    <Clock className="font-medium h-4 w-4" />
                    <span className="flex flex-wrap gap-2 items-center">
                      <span className="uppercase font-medium ">{zone.delivery_window}</span>
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <MapPin className="text-gray-500 h-4 w-4" />
                    <Badge
                      variant="outline"
                      className="bg-mulberry rounded-full font-medium text-white  border-none text-xs sm:text-sm"
                    >
                      Open Map
                    </Badge>
                  </div>
                </div>
                <button
                  onClick={() => router.push(ROUTES.SUBSCRIPTION)}
                  className="px-3 py-1 w-full md:hidden mt-3 flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                >
                  <MapPinHouse className="h-3 w-3" />
                  Explore Home Delivery
                </button>
              </CardContent>
            </Card>
          ))}
        </div>
      ))}
    </div>
  );
}
