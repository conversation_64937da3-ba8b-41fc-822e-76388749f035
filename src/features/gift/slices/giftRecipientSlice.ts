import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { apiPost } from '@/src/lib/apiClient';
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';

interface GiftRecipientDetails {
  address: string;
  email?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  zipCode: string;
}

interface GiftRecipientState {
  loading: boolean;
  error: string | null;
  order: any | null;
  showUnavailableDialog: boolean;
  showAvailableDialog: boolean;
  showZoneRequestDialog: boolean;
  giftRecipientDetails: GiftRecipientDetails | null;
  shouldRedirect: boolean;
  currentComponent: 'address-validation' | 'add-details';
  addressValidationLoading: boolean;
  addressValidationError: string | null;
  updatedOrderId: string | null;
}

// Initial state
const initialState: GiftRecipientState = {
  loading: false,
  error: null,
  order: null,
  showUnavailableDialog: false,
  showAvailableDialog: false,
  showZoneRequestDialog: false,
  giftRecipientDetails: null,
  shouldRedirect: false,
  currentComponent: 'address-validation',
  addressValidationLoading: false,
  addressValidationError: null,
  updatedOrderId: null,
};

export const fetchGiftOrderById = createAsyncThunk(
  'giftRecipient/fetchOrderById',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await apiPost(API_ENDPOINTS.GET_ORDER_BY_ID, { orderId });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.FAILED_TO_FETCH_ORDER);
      }

      const resp = await response.json();
      return resp;
    } catch (error) {
      console.warn('Error fetching gift order by id:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_ORDER);
    }
  }
);

export const validateAddress = createAsyncThunk(
  'giftRecipient/validateAddress',
  async (
    address: {
      address_line_1: string;
      address_line_2: string;
      locality: string;
      administrative_district_level_1: string;
      postal_code: string;
      country: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiPost('/api/validate-address', { address });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || 'Address validation failed');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.warn('Error validating address:', error);
      return rejectWithValue('Address validation failed');
    }
  }
);

export const updateOrder = createAsyncThunk(
  'giftRecipient/updateOrder',
  async (
    userInfo: {
      firstName: string;
      lastName: string;
      phone: string;
      email: string;
      deliveryNotes: string;
      addressLine2: string;
      addressLine1: string;
      city: string;
      orderId: string;
      zipCode: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiPost(API_ENDPOINTS.UPDATE_GIFT_ORDER, { ...userInfo });
      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || 'Address validation failed');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.warn('Error validating address:', error);
      return rejectWithValue('Failed to update the order status');
    }
  }
);

const giftRecipientSlice = createSlice({
  name: 'giftRecipient',
  initialState,
  reducers: {
    resetAllDialogs: state => {
      state.showUnavailableDialog = false;
      state.showAvailableDialog = false;
      state.showZoneRequestDialog = false;
    },
    setGiftRecipientDetails: (state, action: PayloadAction<GiftRecipientDetails>) => {
      state.giftRecipientDetails = action.payload;
      state.currentComponent = 'add-details';
    },
    resetRedirect: state => {
      state.shouldRedirect = false;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setOrder: (state, action: PayloadAction<any>) => {
      state.order = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchGiftOrderById.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGiftOrderById.fulfilled, (state, action) => {
        state.loading = false;
        state.order = JSON.parse(action.payload.order) || null;
      })
      .addCase(fetchGiftOrderById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(validateAddress.pending, state => {
        state.addressValidationLoading = true;
        state.addressValidationError = null;
      })
      .addCase(validateAddress.fulfilled, (state, action) => {
        state.addressValidationLoading = false;
        if (!action.payload.valid) {
          state.addressValidationError = 'Invalid address';
        }
      })
      .addCase(validateAddress.rejected, (state, action) => {
        state.addressValidationLoading = false;
        state.addressValidationError = action.payload as string;
      })
      .addCase(updateOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateOrder.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateOrder.fulfilled, (state, action) => {
        state.updatedOrderId = action.payload.orderId || null;
        state.loading = false;
      });
  },
});

// Export actions and reducer
export const {
  resetAllDialogs,
  setGiftRecipientDetails,
  resetRedirect,
  setLoading,
  setError,
  setOrder,
} = giftRecipientSlice.actions;

export default giftRecipientSlice.reducer;
