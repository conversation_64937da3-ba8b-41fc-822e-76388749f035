import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { apiGet, apiPost } from '@/src/lib/apiClient';
import { API_ENDPOINTS, ERROR_MESSAGES, SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';
import { RootState } from '@/src/store';

// Types
export interface Recipient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface GiftDetailsInterface {
  variationId?: string;
  variationName?: string;
  variationImage?: string;
  productName?: string;
  productPrice?: number;
  deliveryCharge?: number;
  quantity: number;
  orderFor: (typeof SUBSCRIPTION_ORDER_TYPES)[keyof typeof SUBSCRIPTION_ORDER_TYPES];
  total_amount: number;
  recipients: Recipient[];
  giftMessage: string;
}

// Product type definition
type Product = {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  deliveryCharge: number;
  variations?: Array<{
    id: string;
    name: string;
    price?: number;
    image?: string;
    itemVariationData: {
      sellable?: boolean;
    };
  }>;
  customAttributes: Record<string, string | number | boolean | string[] | undefined>;
};

// Combined state interface
interface GiftGiverState {
  // Product state
  product: Product | null;
  productLoading: boolean;
  productError: string | null;

  // Details state
  details: GiftDetailsInterface;

  // Payment state
  paymentLoading: boolean;
  paymentError: string | null;
  orderId: string | null;
}

// Initial state
const initialState: GiftGiverState = {
  // Product initial state
  product: null,
  productLoading: false,
  productError: null,

  // Details initial state
  details: {
    variationId: '',
    variationName: '',
    variationImage: '',
    productName: '',
    productPrice: 0,
    deliveryCharge: 0,
    quantity: 1,
    orderFor: SUBSCRIPTION_ORDER_TYPES.ONE_TIME_GIFT,
    total_amount: 0,
    recipients: [{ id: '1', firstName: '', lastName: '', email: '' }],
    giftMessage: '',
  },

  // Payment initial state
  paymentLoading: false,
  paymentError: null,
  orderId: null,
};

/**
 * Async thunk to fetch gift product details from Square API
 */
export const fetchGiftProduct = createAsyncThunk(
  'giftGiver/fetchProduct',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiGet(API_ENDPOINTS.GET_PRODUCT);

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error);
      }

      const data = await response.json();
      return data as Product;
    } catch (error) {
      console.warn('Error fetching product:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_PRODUCT);
    }
  }
);

/**
 * Creates a gift order using Square API
 */
export const createGiftOrder = createAsyncThunk(
  'giftGiver/createOrder',
  async (
    {
      sourceId,
      firstName,
      lastName,
      email,
    }: {
      sourceId: string;
      firstName: string;
      lastName: string;
      email: string;
    },
    { rejectWithValue, getState }
  ) => {
    try {
      const state = getState() as RootState;
      const { product, details } = state.giftGiver;

      if (!product || !details) {
        return rejectWithValue('Product or gift details not found');
      }

      const payload = {
        variationId: details.variationId,
        variationName: details.variationName,
        variationImage: details.variationImage,
        productName: details.productName,
        productPrice: details.productPrice,
        deliveryCharge: details.deliveryCharge,
        quantity: details.quantity,
        recipients: details.recipients,
        giftMessage: details.giftMessage,
        sourceId,
        firstName,
        lastName,
        email,
      };

      const response = await apiPost(API_ENDPOINTS.CREATE_GIFT_ORDER, payload, {
        useLimitedUseToken: true,
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.FAILED_TO_CREATE_ORDER);
      }

      const resp = await response.json();
      return resp;
    } catch (error) {
      console.warn('Error creating gift order:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_CREATE_ORDER);
    }
  }
);

const giftGiverSlice = createSlice({
  name: 'giftGiver',
  initialState,
  reducers: {
    // Product reducers
    resetProductState: state => {
      state.product = null;
      state.productLoading = false;
      state.productError = null;
    },

    // Details reducers
    setGiftDetails: (state, action: PayloadAction<Partial<GiftDetailsInterface>>) => {
      state.details = { ...state.details, ...action.payload };
    },
    setRecipients: (state, action: PayloadAction<Recipient[]>) => {
      state.details.recipients = action.payload;
    },
    setGiftMessage: (state, action: PayloadAction<string>) => {
      state.details.giftMessage = action.payload;
    },
    resetGiftDetails: state => {
      state.details = initialState.details;
    },

    // Payment reducers
    resetPaymentState: state => {
      state.paymentLoading = false;
      state.paymentError = null;
      state.orderId = null;
    },
    setPaymentLoading: (state, action: PayloadAction<boolean>) => {
      state.paymentLoading = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      // Product thunks
      .addCase(fetchGiftProduct.pending, state => {
        state.productLoading = true;
        state.productError = null;
      })
      .addCase(fetchGiftProduct.fulfilled, (state, action) => {
        state.productLoading = false;
        state.product = action.payload;
      })
      .addCase(fetchGiftProduct.rejected, (state, action) => {
        state.productLoading = false;
        state.productError = action.payload as string;
      })

      // Payment thunks
      .addCase(createGiftOrder.pending, state => {
        state.paymentLoading = true;
        state.paymentError = null;
      })
      .addCase(createGiftOrder.fulfilled, (state, action) => {
        state.paymentLoading = false;
        state.orderId = action.payload.orderId;
      })
      .addCase(createGiftOrder.rejected, (state, action) => {
        state.paymentLoading = false;
        state.paymentError = action.payload as string;
      });
  },
});

// Export actions
export const {
  resetProductState,
  setGiftDetails,
  setRecipients,
  setGiftMessage,
  resetGiftDetails,
  resetPaymentState,
  setPaymentLoading,
} = giftGiverSlice.actions;

// Selectors
export const selectGiftProduct = (state: RootState) => state.giftGiver.product;
export const selectGiftProductLoading = (state: RootState) => state.giftGiver.productLoading;
export const selectGiftProductError = (state: RootState) => state.giftGiver.productError;

export const selectGiftDetails = (state: RootState) => state.giftGiver.details;
export const selectGiftRecipients = (state: RootState) => state.giftGiver.details.recipients;
export const selectGiftMessage = (state: RootState) => state.giftGiver.details.giftMessage;

export const selectGiftPaymentLoading = (state: RootState) => state.giftGiver.paymentLoading;
export const selectGiftPaymentError = (state: RootState) => state.giftGiver.paymentError;
export const selectGiftOrderId = (state: RootState) => state.giftGiver.orderId;

export default giftGiverSlice.reducer;
