'use client';

import React from 'react';

import { Button } from '@/src/components/ui/button';

interface GiftDeliveryZoneUnavailableDialogProps {
  handleResetForm: () => void;
}

const GiftDeliveryZoneUnavailableDialog: React.FC<GiftDeliveryZoneUnavailableDialogProps> = ({
  handleResetForm,
}) => {
  return (
    <div className="h-full bg-white p-8 rounded-xl shadow-sm border border-gray-100">
      <h2 className="text-xl md:text-3xl text-mulberry font-bold text-center mb-3">
        Gift Home Delivery Not Available
      </h2>
      <p className="mb-4 text-center text-gray-700">
        We&apos;re sorry, but Gift Home Delivery is not available at this address.
      </p>

      <div className="space-y-4 max-w-sm mx-auto">
        <Button
          onClick={handleResetForm}
          className="w-full py-3 px-4 bg-white border border-mulberry text-mulberry hover:bg-gray-100 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 font-medium"
        >
          Try another address
        </Button>

        <p className="text-xs text-center text-gray-500 mt-2">
          Need help?{' '}
          <a
            href={`${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}/faqs`}
            className="text-mulberry cursor-pointer"
          >
            View FAQs
          </a>{' '}
          or contact support at{' '}
          <a href="mailto:<EMAIL>" className="text-mulberry cursor-pointer">
            <EMAIL>
          </a>
        </p>
      </div>
    </div>
  );
};

export default GiftDeliveryZoneUnavailableDialog;
