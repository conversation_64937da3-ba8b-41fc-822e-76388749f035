import React from 'react';

const DeliveryInformation = () => {
  return (
    <div className="h-full">
      <div className="h-full bg-white p-4 lg:p-8 rounded-xl shadow-sm border border-gray-100">
        <h3 className="text-xl font-semibold mb-4 text-center md:text-3xl text-mulberry">
          Important Home Delivery Information
        </h3>
        <ul className="space-y-3 max-w-3xl mx-auto">
          <li className="flex items-start">
            <div className="text-mulberry mr-3 mt-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </div>
            <p>
              <span className="font-bold">Mulberries are highly perishable. </span>
              Please make sure you&apos;re able to refrigerate them after delivery, ASAP.
            </p>
          </li>
          {/* Additional list items */}
          {/* <InfoListItem boldText="You can choose between a one-time delivery or a weekly subscription." /> */}
          {/* <InfoListItem boldText="For subscriptions, your card is billed weekly and you can cancel or skip any week before Saturday midnight." /> */}
          <InfoListItem text="Delivery occurs within an 8-hour window. Delivery day is based on zone" />
          <InfoListItem boldText="Delivery day and time cannot be customized." />
          <InfoListItem>
            Deliveries have begun! After placing your order, you’ll receive a notification within 3
            days confirming your delivery day, route zone, and estimated arrival—based on harvest
            conditions and logistics
          </InfoListItem>
          <InfoListItem text="If we're unable to deliver to your address, you'll be notified and issued a full refund promptly." />
          <InfoListItem text="Mulberry season is short—ending late June." />
        </ul>
      </div>
    </div>
  );
};

// Helper component for list items
const InfoListItem = ({
  children,
  text,
  boldText,
}: {
  children?: React.ReactNode;
  text?: string;
  boldText?: string;
}) => {
  return (
    <li className="flex items-start">
      <div className="text-mulberry mr-3 mt-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </div>
      <p>
        {boldText && <span className="font-bold">{boldText}</span>}
        {text}
        {children}
      </p>
    </li>
  );
};

export default DeliveryInformation;
