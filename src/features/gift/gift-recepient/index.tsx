'use client';

import { useEffect } from 'react';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

import { fetchGiftOrderById } from '../slices/giftRecipientSlice';

import AddressValidation from './AddressValidation';
import RecipientDetails from './RecipientDetails';

export default function GiftRecepientPage({ orderId }: { orderId: string }) {
  const dispatch = useAppDispatch();
  const { error, loading, order, currentComponent } = useAppSelector(
    (state: RootState) => state.giftRecipient
  );

  useEffect(() => {
    if (orderId) {
      dispatch(fetchGiftOrderById(orderId));
    }
  }, [dispatch, orderId]);
  /**
   * End Lifecycle Methods
   */

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <h2 className="text-xl font-bold text-red-600">Error</h2>
        <p className="mt-2">{error || 'Product information unavailable'}</p>
        <Button
          onClick={() => dispatch(fetchGiftOrderById(orderId))}
          className="mt-4 bg-mulberry hover:bg-mulberryHover"
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (order && order.metadata.orderFor !== 'MultipleGift') {
    return (
      <div className="text-center p-8 max-w-2xl mx-auto">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Invalid Order</h2>
        <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
          <p className="text-gray-600">
            This is not a valid gift order. Please make sure you have the correct order link.
          </p>
        </div>
      </div>
    );
  }

  if (order?.state !== 'DRAFT') {
    const recipient = order?.fulfillments?.[0]?.shipmentDetails?.recipient;
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            {/* Header */}
            <div className="bg-mulberry px-6 py-8 text-center">
              <h2 className="text-3xl font-bold text-white">Gift Claimed</h2>
              <p className="mt-2 text-white/90">Your gift order has been confirmed</p>
            </div>

            {/* Content */}
            <div className="px-6 py-8">
              <div className="space-y-6">
                {/* Order Details */}
                <div className="bg-gray-50 rounded-xl p-6 space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Order Details</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-500">Order ID</p>
                        <p className="font-medium">{order?.id || 'N/A'}</p>
                      </div>
                      {recipient && (
                        <>
                          <div>
                            <p className="text-sm text-gray-500">Recipient Name</p>
                            <p className="font-medium">{recipient.displayName || 'N/A'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Email</p>
                            <p className="font-medium">{recipient.emailAddress || 'N/A'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Phone</p>
                            <p className="font-medium">{recipient.phoneNumber || 'N/A'}</p>
                          </div>
                        </>
                      )}
                    </div>

                    {/* Address Section */}
                    {recipient && (
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-gray-500">Delivery Address</p>
                          <div className="mt-1 space-y-1">
                            <p className="font-medium">
                              {recipient.address?.addressLine1 || 'N/A'}
                            </p>
                            {recipient.address?.addressLine2 && (
                              <p className="font-medium">{recipient.address.addressLine2}</p>
                            )}
                            <p className="font-medium">
                              {recipient.address?.locality || ''},{' '}
                              {recipient.address?.administrativeDistrictLevel1 || ''}{' '}
                              {recipient.address?.postalCode || ''}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Support Section */}
                <div className="text-center space-y-4">
                  <p className="text-sm text-gray-500">
                    Need help? Contact our support team at{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-mulberry hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {currentComponent === 'address-validation' ? (
        <AddressValidation order={order} />
      ) : (
        <RecipientDetails order={order} />
      )}
    </>
  );
}
