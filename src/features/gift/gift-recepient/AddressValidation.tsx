'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Order } from 'square';
import { UAParser } from 'ua-parser-js';
import { z } from 'zod';

import { setGiftRecipientDetails } from '@/src/features/gift/slices/giftRecipientSlice';
import {
  setShowAvailableDialog,
  resetAllDialogs,
  selectShowUnavailableDialog,
  selectShowAvailableDialog,
  selectShowZoneRequestDialog,
  selectDeliveryDays,
  validateAddress,
} from '@/src/features/subscription/slices/addressValidationSlice';
import {
  VALIDATION_ERROR_MESSAGES,
  ERROR_MESSAGES,
  SUBSCRIPTION_ORDER_TYPES,
} from '@/src/lib/constants';
import { useAppDispatch, useAppSelector } from '@/src/store';
import { SubscriptionOrderType } from '@/src/types/Subscriptions';

import DeliveryInformation from './components/DeliveryInformation';
import DeliveryZoneAvailableDialog from './components/DeliveryZoneAvailableDialog';
import GiftAddressValidationForm from './components/GiftAddressValidationForm';
import GiftDeliveryZoneUnavailableDialog from './components/GiftDeliveryZoneUnavailableDialog';

interface AddressData {
  fullAddress: string;
  addressLine1: string;
  city: string;
  state: string;
  zipCode: string;
  placeData: any;
}

// Zod Schema for Form Validation
const addressSchema = z.object({
  address: z.string().min(5, VALIDATION_ERROR_MESSAGES.ADDRESS_MIN),
});

type AddressFormValues = z.infer<typeof addressSchema>;

export default function AddressValidation({ order }: { order: Order | null }) {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();

  const [selectedAddressData, setSelectedAddressData] = useState<AddressData | null>(null);

  const showUnavailableDialog = useAppSelector(selectShowUnavailableDialog);
  const showAvailableDialog = useAppSelector(selectShowAvailableDialog);
  const showZoneRequestDialog = useAppSelector(selectShowZoneRequestDialog);
  const deliveryDays = useAppSelector(selectDeliveryDays);

  // Form initialization with Zod validation
  const form = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      address: '',
    },
  });
  const giftForm = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      address: '',
    },
  });

  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Add this useEffect to handle automatic redirect when no delivery days
  useEffect(() => {
    if (showAvailableDialog && deliveryDays.length === 0) {
      handleContinue();
    }
  }, [showAvailableDialog, deliveryDays]);

  // Reset available dialog when component mounts
  useEffect(() => {
    dispatch(setShowAvailableDialog(false));
  }, []);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const handleContinue = () => {
    dispatch(resetAllDialogs());
    dispatch(setShowAvailableDialog(false));

    if (!selectedAddressData) return;

    // Store gift recipient details in Redux
    dispatch(
      setGiftRecipientDetails({
        address: selectedAddressData.fullAddress,
        addressLine1: selectedAddressData.addressLine1,
        city: selectedAddressData.city,
        state: selectedAddressData.state,
        zipCode: selectedAddressData.zipCode,
      })
    );
  };

  // Update the handleResetForm function to be more thorough
  const handleResetForm = () => {
    // Reset all dialogs in the Redux store
    dispatch(resetAllDialogs());

    // Reset both form values
    form.reset({ address: '' });
    giftForm.reset({ address: '' });

    // Reset the selectedAddressData state
    setSelectedAddressData(null);
  };

  const handleAddressSubmit = async (data: AddressFormValues, addressData: AddressData | null) => {
    try {
      // Validate that we have a zip code before proceeding
      if (!addressData?.zipCode) {
        toast.error(VALIDATION_ERROR_MESSAGES.ZIP_CODE_REQUIRED);
        handleResetForm();
        return;
      }

      setSelectedAddressData(addressData);

      const userAgentInfo = getUserAgentInfo();

      // Create validation payload
      const validationPayload: {
        address: string;
        zipCode: string;
        orderFor: SubscriptionOrderType;
        userAgentInfo: any;
        giftOrderId: string;
      } = {
        address: data.address,
        zipCode: addressData.zipCode,
        orderFor: SUBSCRIPTION_ORDER_TYPES.MULTIPLE_GIFT,
        giftOrderId: order?.id as string,
        userAgentInfo,
      };

      await dispatch(validateAddress(validationPayload));
    } catch (error) {
      console.error('Error validating address:', error);
      toast.error(ERROR_MESSAGES.GENERIC_ERROR);
    }
  };

  const getUserAgentInfo = () => {
    const parser = new UAParser();
    const uaResult = parser.getResult();

    return {
      browser: {
        name: uaResult.browser.name,
        version: uaResult.browser.version,
      },
      device: {
        model: uaResult.device.model || '',
        type: uaResult.device.type || '',
        vendor: uaResult.device.vendor || '',
      },
      os: {
        name: uaResult.os.name || '',
        version: uaResult.os.version || '',
      },
      userAgent: navigator.userAgent,
      currentUrl: window.location.href, // Includes full URL with query parameters
    };
  };
  /**
   * End Custom Methods
   */

  return (
    <div className="container mx-auto px-4 lg:px-0 relative overflow-hidden" id="validate-address">
      <h1 className="text-center text-3xl font-bold mb-4 text-mulberry">Claim Your Gift</h1>
      <div className="flex flex-col md:grid md:grid-cols-2 gap-6 md:gap-8 md:items-start">
        {/* Column 1: Address Form or Success/Error Message */}
        <div className="h-full order-2 lg:order-1">
          {showAvailableDialog ? (
            <DeliveryZoneAvailableDialog
              deliveryDays={deliveryDays}
              handleContinue={handleContinue}
            />
          ) : showZoneRequestDialog || showUnavailableDialog ? (
            <GiftDeliveryZoneUnavailableDialog handleResetForm={handleResetForm} />
          ) : (
            <div>
              <GiftAddressValidationForm form={giftForm} onSubmit={handleAddressSubmit} />
            </div>
          )}
        </div>

        {/* Column 2: Delivery Information */}
        <DeliveryInformation />
      </div>
    </div>
  );
}
