'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Order } from 'square';
import { z } from 'zod';

import { CustomPhoneInput } from '@/src/components/phone-input';
import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/src/components/ui/form';
import { Input } from '@/src/components/ui/input';
import { Textarea } from '@/src/components/ui/textarea';
import { ERROR_MESSAGES, VALIDATION_ERROR_MESSAGES } from '@/src/lib/constants';
import { RootState, useAppDispatch, useAppSelector } from '@/src/store';

import { updateOrder } from '../slices/giftRecipientSlice';

// Define form schema with zod
const giftDetailsSchema = z
  .object({
    firstName: z.string().min(2, VALIDATION_ERROR_MESSAGES.FIRST_NAME_MIN),
    lastName: z.string().min(2, VALIDATION_ERROR_MESSAGES.LAST_NAME_MIN),
    email: z.string().email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID),
    confirmEmail: z.string().email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID),
    phone: z.string().min(1, VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER),
    address: z.string().min(5, VALIDATION_ERROR_MESSAGES.ADDRESS_IS_REQUIRED),
    addressLine2: z.string().optional(),
    deliveryNotes: z.string().optional(),
  })
  .refine(data => !data.email || data.email === data.confirmEmail, {
    message: 'Email addresses must match',
    path: ['confirmEmail'],
  });

type GiftDetailsFormValues = z.infer<typeof giftDetailsSchema>;

function RecipientDetails({ order }: { order: Order | null }) {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();
  const router = useRouter();

  const { giftRecipientDetails, loading, error } = useAppSelector(
    (state: RootState) => state.giftRecipient
  );

  // Initialize form with react-hook-form and zod validation
  const form = useForm<GiftDetailsFormValues>({
    resolver: zodResolver(giftDetailsSchema),
    defaultValues: {
      firstName:
        (order?.fulfillments &&
          order?.fulfillments[0].shipmentDetails?.recipient?.displayName?.split(' ')?.[0]) ||
        '',
      lastName:
        (order?.fulfillments &&
          order?.fulfillments[0].shipmentDetails?.recipient?.displayName?.split(' ')?.[1]) ||
        '',
      email:
        (order?.fulfillments && order?.fulfillments[0].shipmentDetails?.recipient?.emailAddress) ||
        '',
      confirmEmail:
        (order?.fulfillments && order?.fulfillments[0].shipmentDetails?.recipient?.emailAddress) ||
        '',
      phone: '',
      // For address fields, still use subscription details
      address: giftRecipientDetails?.address || '',
      addressLine2: '',
      deliveryNotes: '',
    },
  });
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const onSubmit = async (data: GiftDetailsFormValues) => {
    try {
      dispatch(
        updateOrder({
          addressLine1: giftRecipientDetails?.addressLine1 || '',
          addressLine2: data.addressLine2 || '',
          deliveryNotes: data.deliveryNotes || '',
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          city: giftRecipientDetails?.city || '',
          zipCode: giftRecipientDetails?.zipCode || '',
          orderId: order?.id || '',
        })
      )
        .unwrap()
        .then(data => {
          if (data.orderId) {
            window.location.reload();
          }
        });
    } catch (_error) {
      toast.error(ERROR_MESSAGES.FAILED_TO_UPDATE_ORDER_STATUS);
    }
  };
  /**
   * End Custom Methods
   */

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <h2 className="text-xl font-bold text-red-600">Error</h2>
        <p className="mt-2">{error || 'Unable to update the order.'}</p>
        <Button onClick={() => router.back()} className="mt-4 bg-mulberry hover:bg-mulberryHover">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="max-w-3xl mx-auto md:mb-6 mb-16">
        <h2 className="text-mulberry text-xl lg:text-3xl font-semibold text-center mb-4">
          Your Details
        </h2>
        <Card>
          <CardContent className="p-2 md:p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email *</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Email *</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number *</FormLabel>
                      <FormControl>
                        <CustomPhoneInput
                          value={field.value || ''}
                          onChange={value => field.onChange(value)}
                          className="w-full"
                          country="us"
                          disabled={false}
                          disableDropdown={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address *</FormLabel>
                      <FormControl>
                        <Input {...field} className="bg-gray-100" disabled />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="addressLine2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Apartment/Suite</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Apt, Suite, Floor, etc." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="deliveryNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Delivery Notes (optional — include anything the driver should know: gate
                        codes, access tips, etc.)
                      </FormLabel>
                      <FormControl>
                        <Textarea {...field} className="min-h-[50px]" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full bg-mulberry text-white hover:bg-mulberryHover"
                >
                  Continue
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </>
  );
}

export default RecipientDetails;
