'use client';

import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { CreditCard, PaymentForm } from 'react-square-web-payments-sdk';
import { toast } from 'sonner';

import { But<PERSON> } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/src/components/ui/dialog';
import { Input } from '@/src/components/ui/input';
import { Textarea } from '@/src/components/ui/textarea';
import { setUrlDiscountCode } from '@/src/features/subscription/slices/discountValidationSlice';
import { useAppDispatch, useAppSelector } from '@/src/store';

import {
  createGiftOrder,
  fetchGiftProduct,
  Recipient,
  selectGiftDetails,
  selectGiftPaymentLoading,
  selectGiftProduct,
  selectGiftProductError,
  selectGiftProductLoading,
  setGiftDetails,
  setGiftMessage,
  setRecipients,
} from '../slices/giftGiverSlice';

interface SelectVariationInterface {
  id: string;
  name: string;
  price?: number;
  image?: string;
}

export default function GiftGiverPage() {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const product = useAppSelector(selectGiftProduct);
  const isLoading = useAppSelector(selectGiftProductLoading);
  const error = useAppSelector(selectGiftProductError);
  const giftDetails = useAppSelector(selectGiftDetails);
  const [localRecipients, setLocalRecipients] = useState<Recipient[]>([]);
  const paymentLoading = useAppSelector(selectGiftPaymentLoading);
  const [buyerDetails, setBuyerDetails] = useState({
    firstName: '',
    lastName: '',
    email: '',
  });
  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [emailError, setEmailError] = useState('');

  // Track the selected variation with image included
  const [selectedVariation, setSelectedVariation] = useState<SelectVariationInterface | null>(null);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    // Prefer lowercase 'code' over uppercase 'CODE'
    const lower = searchParams.get('code');
    const upper = searchParams.get('CODE');
    const code = lower ?? upper;

    // Clear previous intent, then set new code if present
    dispatch(setUrlDiscountCode(code));
  }, [searchParams, dispatch]);

  useEffect(() => {
    // Add a check to prevent duplicate API calls
    if (!product && !isLoading) {
      dispatch(fetchGiftProduct());
    }
  }, [product, isLoading, dispatch]);

  useEffect(() => {
    if (product && product.variations?.length) {
      // If we have a saved variation ID, find the matching variation
      if (giftDetails?.variationId) {
        const matchingVariation = product.variations.find(v => v.id === giftDetails.variationId);

        if (matchingVariation) {
          // Use the image from gift details if available, otherwise use from variation
          const variationImage =
            giftDetails.variationImage || matchingVariation.image || product.image;
          setSelectedVariation({
            id: matchingVariation.id,
            name: matchingVariation.name,
            price: matchingVariation.price || product.price,
            image: variationImage,
          });
        } else {
          // Default to first variation if no match
          const firstVariation = product.variations[0];
          setSelectedVariation({
            id: firstVariation.id,
            name: firstVariation.name,
            price: firstVariation.price || product.price,
            image: firstVariation.image || product.image,
          });
          // Update gift details in Redux with first variation
          dispatch(
            setGiftDetails({
              variationId: firstVariation.id,
              variationName: firstVariation.name,
              variationImage: firstVariation.image || product.image,
              productName: product.name,
              productPrice: firstVariation.price || product.price,
              deliveryCharge: product.deliveryCharge,
              total_amount: (firstVariation.price || product.price) + product.deliveryCharge,
            })
          );
        }
      } else {
        // Default to first variation if no saved variation
        const firstVariation = product.variations[0];
        setSelectedVariation({
          id: firstVariation.id,
          name: firstVariation.name,
          price: firstVariation.price || product.price,
          image: firstVariation.image || product.image,
        });
        // Update gift details in Redux with first variation
        dispatch(
          setGiftDetails({
            variationId: firstVariation.id,
            variationName: firstVariation.name,
            variationImage: firstVariation.image || product.image,
            productName: product.name,
            productPrice: firstVariation.price || product.price,
            deliveryCharge: product.deliveryCharge,
            total_amount: (firstVariation.price || product.price) + product.deliveryCharge,
          })
        );
      }
    }
  }, [product, giftDetails, dispatch]);

  // Initialize recipients from Redux state
  useEffect(() => {
    setLocalRecipients(giftDetails.recipients);
  }, [giftDetails.recipients]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  const handleVariationSelect = (variation: SelectVariationInterface) => {
    if (product && product.variations?.length) {
      setSelectedVariation({
        id: variation.id,
        name: variation.name,
        price: variation.price || product?.price,
        image: variation.image || product.image,
      });

      // Update gift details in Redux
      dispatch(
        setGiftDetails({
          variationId: variation.id,
          variationName: variation.name,
          variationImage: variation.image || product.image,
          productName: product.name,
          productPrice: variation.price || product.price,
          deliveryCharge: product.deliveryCharge,
          total_amount: (variation.price || product.price) + product.deliveryCharge,
        })
      );
    }
  };

  const addRecipient = () => {
    const newRecipients = [
      ...localRecipients,
      { id: Date.now().toString(), firstName: '', lastName: '', email: '' },
    ];
    setLocalRecipients(newRecipients);
    dispatch(setRecipients(newRecipients));
  };

  const removeRecipient = (id: string) => {
    if (localRecipients.length > 1) {
      const newRecipients = localRecipients.filter(recipient => recipient.id !== id);
      setLocalRecipients(newRecipients);
      dispatch(setRecipients(newRecipients));
    }
  };

  const handleInputChange = (id: string, field: keyof Recipient, value: string) => {
    const newRecipients = localRecipients.map(recipient =>
      recipient.id === id ? { ...recipient, [field]: value } : recipient
    );
    setLocalRecipients(newRecipients);
    dispatch(setRecipients(newRecipients));
  };

  const handleGiftMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    dispatch(setGiftMessage(e.target.value));
  };

  const handleBuyerDetailsChange = (field: keyof typeof buyerDetails, value: string) => {
    setBuyerDetails(prev => ({
      ...prev,
      [field]: value,
    }));

    // Validate on change
    switch (field) {
      case 'firstName':
        setFirstNameError(value.trim() === '' ? 'First name is required' : '');
        break;
      case 'lastName':
        setLastNameError(value.trim() === '' ? 'Last name is required' : '');
        break;
      case 'email':
        if (value.trim() === '') {
          setEmailError('Email is required');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          setEmailError('Please enter a valid email address');
        } else {
          setEmailError('');
        }
        break;
    }
  };

  const isBuyerDetailsValid = () => {
    return (
      buyerDetails.firstName.trim() !== '' &&
      buyerDetails.lastName.trim() !== '' &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(buyerDetails.email)
    );
  };

  const handlePaymentFormSubmit = async (token: any) => {
    if (!token || !token.token) {
      toast.error('Payment failed. Please try again.');
      return;
    }

    try {
      const result = await dispatch(
        createGiftOrder({
          sourceId: token.token,
          firstName: buyerDetails.firstName,
          lastName: buyerDetails.lastName,
          email: buyerDetails.email,
        })
      ).unwrap();

      if (result.orderId) {
        // Show success modal
        setIsSuccessModalOpen(true);

        // Clear form data
        setBuyerDetails({
          firstName: '',
          lastName: '',
          email: '',
        });
        setLocalRecipients([{ id: Date.now().toString(), firstName: '', lastName: '', email: '' }]);
        dispatch(
          setRecipients([{ id: Date.now().toString(), firstName: '', lastName: '', email: '' }])
        );
        dispatch(setGiftMessage(''));

        // Reset gift details but keep product data
        dispatch(
          setGiftDetails({
            variationId: product?.variations?.[0]?.id || '',
            variationName: product?.variations?.[0]?.name || '',
            variationImage: product?.variations?.[0]?.image || product?.image || '',
            productName: product?.name || '',
            productPrice: product?.variations?.[0]?.price || product?.price || 0,
            deliveryCharge: product?.deliveryCharge || 0,
            total_amount:
              (product?.variations?.[0]?.price || product?.price || 0) +
              (product?.deliveryCharge || 0),
          })
        );

        // Set selected variation back to first one
        if (product?.variations?.[0]) {
          setSelectedVariation({
            id: product.variations[0].id,
            name: product.variations[0].name,
            price: product.variations[0].price || product.price,
            image: product.variations[0].image || product.image,
          });
        }
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Payment failed. Please try again.');
    }
  };
  /**
   * End Custom Methods
   */

  if (error) {
    return (
      <div className="text-center p-8">
        <h2 className="text-xl font-bold text-red-600">Error</h2>
        <p className="mt-2">{error || 'Something went wrong! Please refrsh page and try aggain'}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="relative w-full flex items-center overflow-hidden py-0 lg:py-6 md:py-0 !pb-10">
        <div className="container mx-auto px-4 sm:px-6 z-10 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-12 items-center">
            <div className="order-1 lg:order-1">
              <h1 className="font-bold text-2xl sm:text-3xl md:text-4xl lg:text-5xl mb-4 md:mb-6 leading-tight">
                <span className="text-mulberry ">
                  Send the Gift of Fresh-Picked Very Mulberries
                </span>
              </h1>
              <ul className="space-y-3 max-w-3xl mx-auto">
                <InfoListItem>
                  <span className="font-bold">Delight friends and family in the Bay Area</span> with
                  a sweet surprise—fresh-picked Very Mulberries delivered to their door.
                </InfoListItem>
                <InfoListItem>
                  <span className="font-bold">Gifts will be delivered within the next week</span>.
                  No need to schedule — we handle the timing.
                </InfoListItem>
                <InfoListItem>
                  <span className="font-bold">
                    Select a pack size, optional gift message, enter a recipient name and email
                  </span>{' '}
                  — we&apos;ll take care of the rest.
                </InfoListItem>
                <InfoListItem>
                  <span className="font-bold">Send to one or many recipients</span>. Add as many
                  names and emails as you&apos;d like below (The selected pack size and gift message
                  will be used for all recipients).
                </InfoListItem>
                <InfoListItem>
                  <i>
                    (If a gift can&apos;t be completed—for example, if it&apos;s outside our
                    delivery area—you&apos;ll be notified and refunded.)
                  </i>
                </InfoListItem>
              </ul>
            </div>
            <div className="relative order-1 lg:order-2 lg:mb-0">
              <div className="rounded-xl lg:w-[80%] overflow-hidden shadow-lg mx-auto max-w-md lg:max-w-none">
                <Image
                  width={1000}
                  height={1000}
                  src="/mulberry-bag.png"
                  alt="Very Mulberry Delivery Package"
                  className="w-full h-auto object-cover"
                />
                <div className="absolute top-4 right-4 bg-mulberry backdrop-blur-sm rounded-full px-4 py-2 md:px-4 md:py-2 shadow-lg animate-bounce">
                  <p className="text-white font-semibold text-sm md:text-base">Limited Season!</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Selection Section */}
      <div className="container mx-auto p-0 pb-10">
        <Card className="shadow-none rounded-xl">
          <CardContent className="p-4 md:p-6 space-y-6">
            <div className="my-4">
              <p className="font-semibold text-2xl text-center">Select Pack Size</p>
              <p className="text-gray-500 text-center">Choose the perfect size for your gift</p>
            </div>
            {isLoading ? (
              <div className="grid grid-cols-2 md:grid-cols-4 md:gap-8 gap-4">
                {[1, 2, 3, 4].map(index => (
                  <div
                    key={index}
                    className="w-full flex flex-col items-center justify-start h-auto min-h-[120px] p-4 border rounded-lg animate-pulse"
                  >
                    <div className="w-full h-[10rem] bg-gray-200 rounded-md mb-2" />
                    <div className="w-3/4 h-4 bg-gray-200 rounded-md mb-2" />
                    <div className="w-1/2 h-4 bg-gray-200 rounded-md" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 md:gap-8 gap-4">
                {product?.variations?.map(variation => {
                  return (
                    <Button
                      key={variation.id}
                      variant={selectedVariation?.id === variation.id ? 'default' : 'outline'}
                      onClick={() => handleVariationSelect(variation)}
                      className={`w-full gap-0 p-0 pb-1 flex flex-col items-center justify-start h-auto min-h-[120px] overflow-hidden ${
                        selectedVariation?.id === variation.id
                          ? 'bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive'
                          : ''
                      }`}
                    >
                      <div className="w-full md:h-[10rem] h-[5rem] relative mb-1">
                        <Image
                          src={variation.image || product?.image || ''}
                          alt={variation.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <span className="text-base truncate w-full font-semibold text-center">
                        {variation.name}
                      </span>
                      <span className="w-full text-center">
                        ${(variation.price || product?.price || 0).toFixed(2)}
                      </span>
                    </Button>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recipients Section */}
      <div className="container mx-auto p-0 pb-10">
        <Card className="shadow-none rounded-xl">
          <CardContent className="p-4 md:p-6 space-y-6">
            <div className="flex flex-col space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-2xl">Recipients</span>
              </div>
              <div className="space-y-4">
                {localRecipients.map((recipient, index) => (
                  <div key={recipient.id} className="flex flex-col md:flex-row gap-4">
                    <div className="w-12 flex items-center">
                      <span className="text-gray-500 font-medium">#{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <Input
                        placeholder="First Name"
                        value={recipient.firstName}
                        onChange={e => handleInputChange(recipient.id, 'firstName', e.target.value)}
                        data-1p-ignore="true"
                      />
                    </div>
                    <div className="flex-1">
                      <Input
                        placeholder="Last Name"
                        value={recipient.lastName}
                        onChange={e => handleInputChange(recipient.id, 'lastName', e.target.value)}
                        data-1p-ignore="true"
                      />
                    </div>
                    <div className="flex-1">
                      <Input
                        type="email"
                        placeholder="Email Address"
                        value={recipient.email}
                        onChange={e => handleInputChange(recipient.id, 'email', e.target.value)}
                        data-1p-ignore="true"
                      />
                    </div>
                    <div className="w-[200px] flex gap-2">
                      {localRecipients.length > 1 && (
                        <Button
                          variant="ghost"
                          onClick={() => removeRecipient(recipient.id)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          Remove
                        </Button>
                      )}
                      {index === localRecipients.length - 1 && (
                        <Button
                          variant="outline"
                          onClick={addRecipient}
                          className="text-mulberry hover:text-mulberryHover hover:bg-mulberry/10"
                        >
                          Add Recipient
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Gift Message Section */}
            <div className="flex flex-col space-y-2">
              <span className="font-semibold text-lg">Gift Message (Optional)</span>
              <Textarea
                placeholder="Add a personal message that will be included with each gift..."
                value={giftDetails.giftMessage}
                onChange={handleGiftMessageChange}
                className="min-h-[100px]"
                data-1p-ignore="true"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment and order summery section */}
      <div className="container mx-auto p-0 md:pb-0 pb-10">
        <Card className="shadow-none border-none rounded-xl">
          <CardContent className="p-0 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-5">
              {/* Order Summary Column - 40% */}
              <div className="md:col-span-2 space-y-4 order-1 md:order-2 border p-6 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="font-semibold text-2xl">Order Summary</span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Item:</span>
                    <span className="font-medium">
                      {product?.name || ''} ({selectedVariation?.name})
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Item Price:</span>
                    <span className="font-medium">${selectedVariation?.price?.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Delivery Charge Per Recipient:</span>
                    <span className="font-medium">
                      ${product?.deliveryCharge?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Number of Recipients:</span>
                    <span className="font-medium">{localRecipients.length}</span>
                  </div>
                  <div className="border-t pt-2 mt-2">
                    <div className="flex justify-between">
                      <span className="font-semibold text-xl">Total Amount:</span>
                      <span className="font-semibold text-mulberry">
                        $
                        {(
                          ((selectedVariation?.price || 0) + (product?.deliveryCharge || 0)) *
                          localRecipients.length
                        ).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Details Column - 60% */}
              <div className="md:col-span-3 space-y-4 order-2 md:order-1 border p-6 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="font-semibold text-2xl">Payment Details</span>
                </div>

                <div className="space-y-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <Input
                        placeholder="First Name"
                        value={buyerDetails.firstName}
                        onChange={e => handleBuyerDetailsChange('firstName', e.target.value)}
                        required
                        className={firstNameError ? 'border-red-500' : ''}
                        data-1p-ignore="true"
                      />
                      {firstNameError && (
                        <p className="text-red-500 text-sm mt-1">{firstNameError}</p>
                      )}
                    </div>
                    <div className="flex-1">
                      <Input
                        placeholder="Last Name"
                        value={buyerDetails.lastName}
                        onChange={e => handleBuyerDetailsChange('lastName', e.target.value)}
                        required
                        className={lastNameError ? 'border-red-500' : ''}
                        data-1p-ignore="true"
                      />
                      {lastNameError && (
                        <p className="text-red-500 text-sm mt-1">{lastNameError}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <Input
                        placeholder="Email"
                        type="email"
                        value={buyerDetails.email}
                        onChange={e => handleBuyerDetailsChange('email', e.target.value)}
                        required
                        className={emailError ? 'border-red-500' : ''}
                        data-1p-ignore="true"
                      />
                      {emailError && <p className="text-red-500 text-sm mt-1">{emailError}</p>}
                    </div>
                    <div className="flex-1"></div>
                  </div>
                </div>

                <div className="space-y-4">
                  <PaymentForm
                    applicationId={process.env.NEXT_PUBLIC_SQUARE_APP_ID || ''}
                    locationId={process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID || ''}
                    cardTokenizeResponseReceived={handlePaymentFormSubmit}
                    createVerificationDetails={() => ({
                      amount: String(
                        Math.round(
                          ((selectedVariation?.price || 0) + (product?.deliveryCharge || 0)) *
                            localRecipients.length *
                            100
                        )
                      ),
                      currencyCode: 'USD',
                      intent: 'STORE',
                      billingContact: {
                        familyName: buyerDetails.lastName,
                        givenName: buyerDetails.firstName,
                        email: buyerDetails.email,
                        country: 'US',
                      },
                    })}
                  >
                    <CreditCard
                      buttonProps={{
                        css: {
                          "[data-theme='dark'] &": {
                            backgroundColor: '#892b76',
                            '&:hover': { backgroundColor: '#6e225e' },
                          },
                          backgroundColor: '#892b76',
                          '&:hover': { backgroundColor: '#6e225e' },
                          ...(paymentLoading && {
                            opacity: '0.5',
                            cursor: 'not-allowed',
                            '&:hover': { backgroundColor: '#892b76' },
                          }),
                          ...(!isBuyerDetailsValid() && {
                            opacity: '0.5',
                            cursor: 'not-allowed',
                            '&:hover': { backgroundColor: '#892b76' },
                          }),
                        },
                      }}
                    >
                      {paymentLoading ? 'Processing...' : 'Pay'}
                    </CreditCard>
                  </PaymentForm>

                  <div className="mt-4 text-xs text-gray-600">
                    By paying, I authorize Very Mulberry (Habitera Farms LLC) to securely charge my
                    card via Square for the gift delivery payment.
                  </div>
                  <div className="mt-3 flex justify-center">
                    <Image
                      src="/Square_LogoLockup_Black.png"
                      alt="Powered by Square"
                      width={120}
                      height={30}
                      className="object-contain"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Success Modal */}
      <Dialog
        open={isSuccessModalOpen}
        onOpenChange={open => {
          if (!open) {
            window.location.reload();
          }
          setIsSuccessModalOpen(open);
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-mulberry">
              Order Successful!
            </DialogTitle>
            <DialogDescription className="text-base">
              Thank you for your gift order. Your recipients will receive their Very Mulberries
              soon!
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-gray-600">
              Your order confirmation has been emailed to you. You can close this window and place
              another order if you&apos;d like.
            </p>
          </div>
          <DialogFooter>
            <Button
              onClick={() => {
                setIsSuccessModalOpen(false);
                window.location.reload();
              }}
              className="bg-mulberry hover:bg-mulberryHover text-white"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Helper component for list items
const InfoListItem = ({
  children,
  text,
  boldText,
}: {
  children?: React.ReactNode;
  text?: string;
  boldText?: string;
}) => {
  return (
    <li className="flex items-start">
      <div className="text-mulberry mr-3 mt-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </div>
      <p>
        {boldText && <span className="font-bold">{boldText}</span>}
        {text}
        {children}
      </p>
    </li>
  );
};
