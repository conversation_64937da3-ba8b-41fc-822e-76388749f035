import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { format, parse } from 'date-fns';

import { apiGet } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { Events } from '@/src/types/Events';

/**
 * Interface representing the state shape for the schedule slice
 */
interface ScheduleState {
  // Object containing events grouped by date
  groupedSchedules: { [key: string]: Events[] };
  // Loading state for async operations
  loading: boolean;
  // Error message if any operation fails
  error: string | null;
}

// Initial state for the schedule slice
const initialState: ScheduleState = {
  groupedSchedules: {},
  loading: false,
  error: null,
};

/**
 * Async thunk action creator for fetching event schedules from the API
 *
 * @returns Promise containing array of Events on success, error message on failure
 * @throws RejectWithValue containing error message if fetch fails
 */
export const fetchEventSchedules = createAsyncThunk(
  'schedule/fetchEventSchedules',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch event schedules from API using apiGet
      const response = await apiGet(API_ENDPOINTS.EVENT_SCHEDULES);
      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_SCHEDULES);
      }
      const data = await response.json();
      return data.data as Events[];
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_SCHEDULES);
    }
  }
);

/**
 * Redux slice for managing event schedule state
 * Handles loading, success, and error states for schedule fetching
 */
const scheduleSlice = createSlice({
  name: 'schedule',
  initialState,
  reducers: {}, // No synchronous reducers needed
  extraReducers: builder => {
    builder
      // Handle pending state when fetching schedules
      .addCase(fetchEventSchedules.pending, state => {
        state.loading = true;
        state.error = null;
      })
      // Handle successful schedule fetch
      .addCase(fetchEventSchedules.fulfilled, (state, action) => {
        state.loading = false;
        // Group schedules by date using utility function
        state.groupedSchedules = groupSchedulesByDate(action.payload);
      })
      // Handle failed schedule fetch
      .addCase(fetchEventSchedules.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default scheduleSlice.reducer;

/**
 * Groups event schedules by date and organizes them into a structured format.
 *
 * @param events - Array of Events objects containing event schedules
 * @returns An object where keys are dates (YYYY-MM-DD) and values are arrays of Events
 *          Each event contains title, location, and sorted schedules for that date
 *
 * Example output:
 * {
 *   "2024-03-20": [
 *     {
 *       title: "Event 1",
 *       event_location: "Location 1",
 *       event_schedules: [{ id, title, start_time, end_time }, ...]
 *     }
 *   ]
 * }
 */
const groupSchedulesByDate = (events: Events[]): { [key: string]: Events[] } => {
  // Initialize empty object to store grouped events
  const grouped: { [key: string]: Events[] } = {};

  events.forEach(event => {
    event.event_schedules?.forEach(schedule => {
      // Format the date to YYYY-MM-DD using date-fns
      const date = schedule.event_date
        ? format(schedule.event_date, 'yyyy-MM-dd')
        : format(getPacificTimeNow(), 'yyyy-MM-dd'); // fallback to current date if undefined

      // Initialize array for this date if it doesn't exist
      if (!grouped[date]) {
        grouped[date] = [];
      }

      // Create simplified schedule entry with essential information
      const scheduleEntry = {
        id: schedule.id,
        title: schedule.title,
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        event_date: schedule.event_date,
        tickets_sold: schedule.tickets_sold,
        max_no_of_tickets: schedule.max_no_of_tickets,
      };

      // Check if event already exists for this date
      const eventEntry = grouped[date].find(e => e.title === event.title);

      if (eventEntry) {
        // Add schedule to existing event
        eventEntry.event_schedules?.push(scheduleEntry);
      } else {
        // Create new event entry with this schedule
        grouped[date].push({
          title: event.title ?? '',
          event_location: event.event_location,
          id: event.id,
          event_schedules: [scheduleEntry],
          description: event.description,
          guidelines: event.guidelines ?? [],
          what_to_expect: event.what_to_expect ?? null,
        });
      }
    });
  });

  // Sort schedules within each event by start time
  // Sort the main grouped dates in ascending order
  const sortedGrouped: { [key: string]: Events[] } = Object.keys(grouped)
    .sort((a, b) => {
      const dateA = parse(a, 'yyyy-MM-dd', getPacificTimeNow());
      const dateB = parse(b, 'yyyy-MM-dd', getPacificTimeNow());
      return dateA.getTime() - dateB.getTime();
    }) // Sorting dates in ascending order
    .reduce(
      (acc, date) => {
        acc[date] = grouped[date]; // Preserve events and schedules without sorting
        return acc;
      },
      {} as { [key: string]: Events[] }
    );

  Object.keys(sortedGrouped).forEach(date => {
    sortedGrouped[date].forEach(event => {
      // Keep event_schedules order unchanged
      event.event_schedules = event.event_schedules ?? [];
    });
  });

  return sortedGrouped;
};
