import { format, parseISO, eachDayOfInterval, isValid } from 'date-fns';
import { ClockI<PERSON>, MapPin, TicketCheck, XCircle } from 'lucide-react';
import React, { useEffect, useMemo } from 'react';

import Spinner from '@/src/components/spinner';
import { Card, CardContent, CardHeader } from '@/src/components/ui/card';
import {
  BUTTON_TEXTS,
  CLOSE_UPICK_CARD_TEXT,
  EVENT_STATUS,
  FARM_ADDRESS,
  FARM_ADDRESS_LINK,
  SOLD_OUT_CARD_TEXT,
} from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { formatTimeRange } from '@/src/lib/utils';
import { useAppSelector, RootState, useAppDispatch } from '@/src/store';
import { TicketTailorEvent } from '@/src/types/TicketTailor';

import { fetchEventSchedule } from '../ticket-tailor/slice';

/**
 * Interface for grouped events data structure
 * Organizes events by date, then by event name, then as an array of events
 */

/**
 * Removes the 'ev_' prefix from event IDs if present
 */

export default function Schedules() {
  // Get event schedule data from Redux store
  const { eventSchedule, loading } = useAppSelector((state: RootState) => state.ticketTailor);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (!eventSchedule?.data) {
      dispatch(fetchEventSchedule());
    }
  }, [dispatch, eventSchedule?.data]);

  /**
   * Handles the "Book Now" button click by redirecting to the event's checkout URL
   */
  function handleBookNow(event: TicketTailorEvent) {
    const eventSeriesId = event.event_series_id;
    const eventIdWithoutPrefix = eventSeriesId.substring(3);
    window.location.href = `https://www.tickettailor.com/events/${process.env.NEXT_PUBLIC_TICKET_TAILOR_EVENT}/${eventIdWithoutPrefix}/select-date/${event?.start.date}`;
  }

  /**
   * Opens Google Maps directions to the event venue when location is clicked
   */
  const handleLocationClick = (venue: string) => {
    if (venue) {
      // Google Maps URL with destination coordinates
      const mapsUrl = `${venue}`;
      window.open(mapsUrl, '_blank');
    }
  };

  /**
   * Checks if an event's end time has passed
   */
  const isEventTimePassed = (event: TicketTailorEvent) => {
    if (!event.end?.date || !event.end?.time) return false;

    const now = getPacificTimeNow();
    const eventDate = parseISO(event.end.date);
    const [hours, minutes] = event.end.time.split(':').map(Number);

    const eventEndTime = new Date(eventDate);
    eventEndTime.setHours(hours, minutes, 0, 0);

    return now > eventEndTime;
  };

  /**
   * Groups events by date and event name, ensuring all dates in the range are included
   * even if there are no events on some dates
   */
  const groupedEvents = useMemo(() => {
    // Return empty object if no event data is available
    if (!eventSchedule?.data || eventSchedule.data.length === 0) {
      return {};
    }

    // Get current date in Pacific time
    const today = getPacificTimeNow();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Create a copy of events, filter if needed, and sort by date and time
    const filteredEvents = [...eventSchedule.data]
      .filter(event => {
        // Filter out hidden or unavailable events
        if (
          event.hidden === 'true' ||
          event.unavailable === 'true' ||
          event.status !== EVENT_STATUS.PUBLISHED
        )
          return false;

        // Filter out past dates
        if (event.start?.date) {
          const eventDate = parseISO(event.start.date);
          if (isValid(eventDate) && eventDate < todayStart) return false;
        }

        return true;
      })
      .sort((a, b) => {
        // Sort by date
        const dateA = a.start?.date ? parseISO(a.start.date) : getPacificTimeNow();
        const dateB = b.start?.date ? parseISO(b.start.date) : getPacificTimeNow();

        // If dates are the same, sort by time
        if (dateA.getTime() === dateB.getTime()) {
          const timeA = a.start?.time || '';
          const timeB = b.start?.time || '';
          return timeA.localeCompare(timeB);
        }

        return dateA.getTime() - dateB.getTime();
      });

    // Initialize the grouped events object
    const groups: { [date: string]: { [eventName: string]: TicketTailorEvent[] } } = {};

    // // Always include today's date
    // const today = getPacificTimeNow();
    // groups[format(today, 'yyyy-MM-dd')] = {};

    // Find date range and create entries for all dates in one pass
    if (filteredEvents.length > 0) {
      // Extract all valid dates from events
      const allEventDates = filteredEvents
        .filter(event => event?.start?.date)
        .map(event => parseISO(event?.start?.date));

      if (allEventDates.length > 0) {
        // Find min and max dates in a single pass
        const dateRange = allEventDates.reduce(
          (range, date) => ({
            min: !range.min || date < range.min ? date : range.min,
            max: !range.max || date > range.max ? date : range.max,
          }),
          { min: null, max: null } as { min: Date | null; max: Date | null }
        );

        // Create entries for all dates in the range
        if (dateRange.min && dateRange.max && isValid(dateRange.min) && isValid(dateRange.max)) {
          eachDayOfInterval({ start: dateRange.min, end: dateRange.max }).forEach(date => {
            groups[format(date, 'yyyy-MM-dd')] = {};
          });
        }
      }
    }

    // Populate the groups object with actual events
    filteredEvents.forEach(event => {
      if (!event.start?.date) return; // Skip events without a date
      if (!event.name) return; // Skip events without a name

      const dateKey = event.start.date;
      const eventName = event.name.toString();

      // Ensure the date entry exists
      if (!groups[dateKey]) {
        groups[dateKey] = {};
      }

      // Ensure the event name entry exists
      if (!groups[dateKey][eventName]) {
        groups[dateKey][eventName] = [];
      }

      // Add this event to the appropriate group
      groups[dateKey][eventName].push(event);
    });

    return groups;
  }, [eventSchedule]);

  if (loading && (!eventSchedule?.data || eventSchedule.data.length === 0)) return <Spinner />;

  // Check if there are no events at all after filtering
  if (Object.keys(groupedEvents).length === 0) {
    return (
      <div className="min-h-0 max-w-5xl mb-6 m-auto">
        <Card className="p-4 text-center">
          <CardContent>
            <XCircle className="mx-auto mb-2 h-8 w-8 text-mulberry" />
            <h3 className="text-lg font-semibold">No U-Pick Events Available</h3>
            <p className="text-gray-600">
              There are currently no upcoming U-Pick events scheduled.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-0 max-w-5xl mb-6 m-auto">
      {/* Render each date section */}
      {Object.entries(groupedEvents).map(([date, eventsByName]) => (
        <div key={date}>
          {/* Date separator with visual divider */}
          <div className="relative flex items-center my-6">
            <div className="flex-grow border-t border-gray-300" />
            <div className="mx-4 bg-white px-4 py-1 text-gray-800 font-semibold rounded-full shadow-md border border-gray-200">
              {format(parseISO(date), 'EEEE, MMMM d, yyyy')}
            </div>
            <div className="flex-grow border-t border-gray-300" />
          </div>

          <div className="">
            {/* Check if there are any events for this date */}
            {Object.keys(eventsByName).length > 0 ? (
              // Show each event separately
              Object.entries(eventsByName).map(([eventName, events]) => {
                // Group is NOT sold out if ANY event has ANY ticket type available
                const isSoldOut = !events.some(event =>
                  event.ticket_types?.some(ticket => ticket.status === 'on_sale')
                );

                // Check if ALL event time slots have passed
                const areAllTimeSlotsPassed = events.every(event => isEventTimePassed(event));

                // Check if the event has started yet
                const hasEventStarted = () => {
                  if (!events[0].start?.date || !events[0].start?.time) return false;

                  const now = getPacificTimeNow();
                  const eventDate = parseISO(events[0].start.date);
                  const [hours, minutes] = events[0].start.time.split(':').map(Number);

                  const eventStartTime = new Date(eventDate);
                  eventStartTime.setHours(hours, minutes, 0, 0);

                  return now >= eventStartTime;
                };

                // Show sold out card if event is sold out and hasn't started yet
                // Show closed card if ALL time slots have passed
                if (isSoldOut && !hasEventStarted()) {
                  return (
                    <Card
                      key={eventName}
                      className="relative p-4 overflow-hidden border border-gray-200 mb-4"
                    >
                      <div className="absolute inset-0 bg-gray-100/70 backdrop-blur-[1px] flex items-center justify-center z-10">
                        <div className="bg-gray-500 text-white px-6 py-2 rounded-full shadow-md flex items-center ">
                          <span className="font-bold">{SOLD_OUT_CARD_TEXT.HEADING}</span>
                        </div>
                      </div>

                      <CardContent className="p-0 opacity-60">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold">{eventName}</h3>
                          </div>
                        </div>
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center text-gray-600">
                            <MapPin className="w-4 h-4 mr-2" />
                            <span className="text-sm">{CLOSE_UPICK_CARD_TEXT.ADDRESS}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                } else if (areAllTimeSlotsPassed) {
                  return (
                    <Card
                      key={eventName}
                      className="relative p-4 overflow-hidden border border-gray-200 mb-4"
                    >
                      <div className="absolute inset-0 bg-gray-100/70 backdrop-blur-[1px] flex items-center justify-center z-10">
                        <div className="bg-mulberry text-white px-6 py-2 rounded-full shadow-md flex items-center ">
                          <span className="font-bold">{CLOSE_UPICK_CARD_TEXT.HEADING}</span>
                        </div>
                      </div>

                      <CardContent className="p-0 opacity-60">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold">{eventName}</h3>
                          </div>
                        </div>
                        <div className="flex flex-col gap-1">
                          <div className="flex items-center text-gray-600">
                            <MapPin className="w-4 h-4 mr-2" />
                            <span className="text-sm">{CLOSE_UPICK_CARD_TEXT.ADDRESS}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                } else if (isSoldOut) {
                  // Display sold out card for events that have started but are sold out
                  return (
                    <Card
                      key={eventName}
                      className="relative p-4 overflow-hidden border border-gray-200 mb-4"
                    >
                      <div className="absolute inset-0 bg-gray-100/70 backdrop-blur-[1px] flex items-center justify-center z-10">
                        <div className="bg-gray-500 text-white px-6 py-2 rounded-full shadow-md flex items-center ">
                          <span className="font-bold">{SOLD_OUT_CARD_TEXT.HEADING}</span>
                        </div>
                      </div>

                      <CardContent className="p-0 opacity-60">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold">{eventName}</h3>
                          </div>
                        </div>
                        <div className="flex flex-col gap-1">
                          {/* Time Section */}

                          {/* Location Section */}
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600 text-sm">
                              {events[0].venue
                                ? `${events[0].venue.name}${events[0].venue.postal_code ? `, ${events[0].venue.postal_code}` : ''}`
                                : ''}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                }

                // Regular event card (existing code)
                return (
                  <Card key={eventName} className="mb-4 p-4 ">
                    <CardHeader className="p-0 mb-2">
                      <div className="flex justify-between items-center w-full">
                        <h3 className="text-lg font-semibold m-0">{eventName}</h3>
                        <button
                          onClick={() => {
                            if (events[0]) handleBookNow(events[0]);
                          }}
                          className="px-3 py-1 hidden md:flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                        >
                          <TicketCheck className="h-3 w-3" />
                          {BUTTON_TEXTS.BOOK_NOW}
                        </button>
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      {/* Event content */}
                      <div className="flex flex-col gap-1 justify-between">
                        {/* Time Section */}
                        <div className="flex items-center gap-2">
                          <ClockIcon className="h-4 w-4" />
                          <span className="font-medium text-sm">
                            {(() => {
                              // Group consecutive time slots
                              const timeGroups = [];
                              let currentGroup: TicketTailorEvent[] = [];

                              // Sort events by start time
                              const sortedEvents = [...events].sort((a, b) => {
                                const timeA = a.start?.time || '';
                                const timeB = b.start?.time || '';
                                return timeA.localeCompare(timeB);
                              });

                              // Process each event
                              for (let i = 0; i < sortedEvents.length; i++) {
                                const event = sortedEvents[i];

                                // Skip events without start/end times
                                if (!event.start?.time || !event.end?.time) continue;

                                // First event or after we've started a new group
                                if (currentGroup.length === 0) {
                                  currentGroup.push(event);
                                } else {
                                  const prevEvent = currentGroup[
                                    currentGroup.length - 1
                                  ] as TicketTailorEvent;

                                  // Check if this event starts EXACTLY when the previous one ends
                                  if (prevEvent.end?.time === event.start?.time) {
                                    currentGroup.push(event);
                                  } else {
                                    // This is a break, so start a new group
                                    timeGroups.push([...currentGroup]);
                                    currentGroup = [event];
                                  }
                                }
                              }

                              // Add the last group if it exists
                              if (currentGroup.length > 0) {
                                timeGroups.push(currentGroup);
                              }

                              // Render each group
                              return timeGroups
                                .map((group, groupIndex) => {
                                  const startTime = group[0].start?.time;
                                  const endTime = group[group.length - 1].end?.time;

                                  if (!startTime || !endTime) return null;

                                  return (
                                    <React.Fragment key={groupIndex}>
                                      {formatTimeRange(startTime, endTime)}
                                      {groupIndex < timeGroups.length - 1 ? ', ' : ''}
                                    </React.Fragment>
                                  );
                                })
                                .filter(Boolean);
                            })()}
                          </span>
                        </div>

                        {/* Location and Book Now Section */}
                        <div
                          onClick={() => handleLocationClick(FARM_ADDRESS_LINK)}
                          className="flex items-center gap-2 group cursor-pointer"
                        >
                          <MapPin className="h-4 w-4 text-gray-500 group-hover:text-mulberry" />
                          <span className="text-gray-600 text-sm group-hover:text-mulberry">
                            {FARM_ADDRESS}
                          </span>
                        </div>

                        <button
                          onClick={() => {
                            if (events[0]) handleBookNow(events[0]);
                          }}
                          className="px-3 py-1 md:hidden mt-2 flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                        >
                          <TicketCheck className="h-3 w-3" />
                          {BUTTON_TEXTS.BOOK_NOW}
                        </button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            ) : (
              // Display a message when there are no events for this date
              <Card className="relative p-4 overflow-hidden border border-gray-200 mb-4">
                <div className="absolute inset-0 bg-gray-100/70 backdrop-blur-[1px] flex items-center justify-center z-10">
                  <div className="bg-mulberry text-white px-6 py-2 rounded-full shadow-md flex items-center ">
                    <span className="font-bold">{CLOSE_UPICK_CARD_TEXT.HEADING}</span>
                  </div>
                </div>

                <CardContent className="p-0 opacity-60">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                    <div>
                      <h3 className="text-lg font-semibold">{CLOSE_UPICK_CARD_TEXT.NAME}</h3>
                    </div>
                  </div>
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center text-gray-600">
                      <MapPin className="w-4 h-4 mr-2" />
                      <span className="text-sm">{FARM_ADDRESS}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
