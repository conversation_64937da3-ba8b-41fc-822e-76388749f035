import Link from 'next/link';
import React from 'react';

import { But<PERSON> } from '@/src/components/ui/button';
import { Card, CardHeader, CardContent } from '@/src/components/ui/card';
import { useAppSelector, RootState } from '@/src/store';

const SendGiftCard = () => {
  const user = useAppSelector((state: RootState) => state.authentication.user);

  return (
    <Card className="mt-6 border rounded-lg shadow-sm overflow-hidden">
      <CardHeader className="pb-2">
        <h2 className="text-base font-bold">My Gifts</h2>
      </CardHeader>
      <CardContent className="px-6">
        <div className="space-y-1">
          <p className="text-mulberry text-sm font-semibold">
            Hey {user?.first_name || 'there'}, you haven&apos;t sent any gifts.
          </p>
          <p className="text-gray-600 text-sm">
            Brighten someone&apos;s day with the gift of Mulberries.
          </p>
        </div>
        <div className="pt-4">
          <Link href="/gifts">
            <Button className="bg-black hover:bg-mulberry text-white px-3 rounded">
              Send Gift
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

export default SendGiftCard;
