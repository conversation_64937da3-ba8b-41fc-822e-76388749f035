import Link from 'next/link';
import React, { useMemo } from 'react';

import { But<PERSON> } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { BUTTON_TEXTS, ROUTES, TICKET_TYPES } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { getTicketType } from '@/src/lib/utils';
import { RootState, useAppSelector } from '@/src/store';
import { Tickets } from '@/src/types/Tickets';

export default function UpcomingEventCard({
  order,
  isHomePage,
}: {
  order: {
    [x: string]: Tickets[];
  } | null;
  isHomePage?: boolean;
}) {
  /**
   * Start Initials
   */
  // Get current user from Redux store
  const { user } = useAppSelector((state: RootState) => state.authentication);

  const upcomingEvent = useMemo(() => {
    if (!order)
      return { guestCount: 0, eventTitle: '', isBooker: false, ticketId: null, orderId: null };

    // Get the first (and only) order ID
    const orderId = Object.keys(order)[0];
    if (!orderId)
      return { guestCount: 0, eventTitle: '', isBooker: false, ticketId: null, orderId: null };

    const tickets = order[orderId];

    // Check if current user is the booker by matching name and email
    const isBooker = tickets.some(
      ticket =>
        ticket?.booker?.first_name?.toLowerCase() === user?.first_name?.toLowerCase() &&
        ticket?.booker?.last_name?.toLowerCase() === user?.last_name?.toLowerCase() &&
        ticket?.booker?.email?.toLowerCase() === user?.email?.toLowerCase()
    );

    // Filter out clamshell and parking tickets
    const filteredTickets = tickets.filter(ticket => {
      const type = getTicketType(ticket.ticket_type || '');
      return type !== TICKET_TYPES.CLAM_SHELL && type !== TICKET_TYPES.PARKING;
    });

    // Find the current user's ticket ID
    const userTicket = filteredTickets.find(
      ticket =>
        ticket?.user_details?.first_name?.toLowerCase() === user?.first_name?.toLowerCase() &&
        ticket?.user_details?.last_name?.toLowerCase() === user?.last_name?.toLowerCase() &&
        ticket?.user_details?.email?.toLowerCase() === user?.email?.toLowerCase()
    );
    const ticketId = userTicket?.id || null;

    const firstTicket = filteredTickets[0] || tickets[0];

    const signedWaiverCount = filteredTickets.reduce((count, ticket) => {
      // Check if the ticket has a signed waiver
      const hasWaiver = ticket?.hasValidWaiver === true || ticket?.waiver;
      return hasWaiver ? count + 1 : count;
    }, 0);

    return {
      eventId: firstTicket?.event_details?.id || '',
      guestCount: filteredTickets.length,
      eventTitle: firstTicket?.event_details?.name || firstTicket?.event?.title || 'Upcoming Event',
      eventDate: firstTicket?.event_details?.start_date?.formatted || '',
      signedWaiverCount,
      isBooker,
      ticketId,
      orderId,
    };
  }, [order, user]);

  /**
   * End Initials
   */

  return (
    <Card className="w-full shadow-none border-none bg-transparent p-4">
      <CardHeader className="flex flex-row  items-center justify-between  p-0">
        <CardTitle
          className={`text-base font-bold  ${isHomePage && 'border-b pb-2 w-full border-gray-100'}`}
        >
          My Upcoming U-Pick Experience
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0 pt-2">
        <div className="space-y-1">
          {!order ? (
            <>
              <p className="text-mulberry text-sm font-semibold">
                Hey {user?.first_name || ''}, you don&apos;t have any upcoming U-Pick events at the
                farm.
              </p>
              <p className="text-gray-600 text-sm">
                Don&apos;t miss out! Book your next event with us today!
              </p>
            </>
          ) : (
            <div className="flex flex-col md:flex-row md:justify-between md:items-center  text-gray-600">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 flex-1  md:mb-0">
                <div>
                  <p className="text-sm font-medium">Event Title</p>
                  <p className="font-medium text-sm text-black">
                    {upcomingEvent.eventTitle || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Date & Time</p>
                  <p className="text-sm text-black">{upcomingEvent.eventDate}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">No. of People</p>
                  <p className="text-sm text-black">{upcomingEvent.guestCount || 0}</p>
                </div>
              </div>

              {upcomingEvent.eventDate &&
              new Date(upcomingEvent.eventDate).toDateString() ===
                getPacificTimeNow().toDateString() &&
              upcomingEvent.ticketId ? (
                <div className="md:ml-4">
                  <Link href={`${ROUTES.RESERVATIONS}/${upcomingEvent.ticketId}`}>
                    <Button className="w-full md:w-auto bg-black hover:bg-mulberryHover text-white">
                      {BUTTON_TEXTS.CHECK_IN}
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="md:ml-4">
                  <Link href={`${ROUTES.RESERVATIONS}?orderId=${upcomingEvent.orderId}`}>
                    <Button className="w-full md:w-auto bg-mulberry hover:bg-mulberryHover text-white">
                      Manage
                    </Button>
                  </Link>
                </div>
              )}

              {/* {showCheckOutButton && (
                <div className="md:ml-4">
                  <Link href={`${process.env.NEXT_PUBLIC_CHECKOUT_STORE_URL}?mode=check-out`}>
                    <Button className="w-full md:w-auto bg-black hover:bg-mulberryHover text-white">
                      {BUTTON_TEXTS.CHECK_OUT}
                    </Button>
                  </Link>
                </div>
              )} */}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
