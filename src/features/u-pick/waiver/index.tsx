'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useRef, useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
// import SignatureCanvas from 'react-signature-canvas';
import { toast } from 'sonner';
import { z } from 'zod';

import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Checkbox } from '@/src/components/ui/checkbox';
import { Input } from '@/src/components/ui/input';
import { Label } from '@/src/components/ui/label';
import { selectUser, selectIsAuthenticated } from '@/src/features/auth/slices/authenticationSlice';
import { fetchWaiverContent } from '@/src/features/u-pick/slices/waiverContentSlice';
import { submitWaiver } from '@/src/features/u-pick/slices/waiversSlice';
import {
  BUTTON_TEXTS,
  VALIDATION_ERROR_MESSAGES,
  ERROR_MESSAGES,
  ROUTES,
  AGE_GROUPS,
  TICKET_TYPES,
} from '@/src/lib/constants';
import { decodeWaiverToken, getTicketType } from '@/src/lib/utils';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';
import { Waivers } from '@/src/types/Waivers';

import { clearWaiverState, fetchTicketsByOrder } from '../../ticket-tailor/slice';

// Zod Schema for Form Validation - matching Waivers interface
const waiverSchema = z.object({
  age_group: z.enum(['adult', 'child', 'senior']).optional(),
  first_name: z.string().min(2, VALIDATION_ERROR_MESSAGES.FIRST_NAME_MIN),
  last_name: z.string().min(2, VALIDATION_ERROR_MESSAGES.LAST_NAME_MIN),
  email: z.string().min(1, 'Email cannot be empty').email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID), // Added min(1)
  // signature: z.string().min(1, VALIDATION_ERROR_MESSAGES.SIGNATURE_REQUIRED),
  ip_address: z.string().optional(),
  device_info: z.string().optional(),
  mailingList: z.boolean().optional(),
  agreementAccepted: z.boolean().refine(val => val === true, {
    message: 'You must accept the waiver agreement to continue',
  }),
  version: z.string().optional(),
  children: z
    .array(
      z.object({
        first_name: z.string().min(2, VALIDATION_ERROR_MESSAGES.FIRST_NAME_MIN),
        last_name: z.string().min(2, VALIDATION_ERROR_MESSAGES.LAST_NAME_MIN),
        email: z.string().email(VALIDATION_ERROR_MESSAGES.EMAIL_INVALID).optional(),
      })
    )
    .optional(),
});

type WaiverFormValues = z.infer<typeof waiverSchema>;

const WaiverForm = () => {
  /**
   * Start Initials
   */
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const { loading, error, tickets, order } = useAppSelector(
    (state: RootState) => state.ticketTailor
  );
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const { content: waiverContent, version: waiverVersion } = useAppSelector(
    (state: RootState) => state.waiverContent
  );
  const [waiverUserName, setWaiverUserName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  // const [isOrgaizer, setIsOrganizer] = useState(false);
  const [children, setChildren] = useState<
    Array<
      Tickets & {
        user: Users | null;
      }
    >
  >([]);
  // const [isChildWaiver, setIsChildWaiver] = useState(false);

  // Get orderId and nextUrl from search params
  const orderId = searchParams.get('tt_order_id');
  const nextUrl = searchParams.get('nextUrl');
  const token = searchParams.get('t');

  // Form initialization with Zod validation
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<WaiverFormValues>({
    resolver: zodResolver(waiverSchema),
    defaultValues: {
      age_group: AGE_GROUPS.ADULT as Waivers['age_group'],
      first_name: '',
      last_name: '',
      email: '',
      mailingList: true,
      agreementAccepted: false,
      children: children.map(child => ({
        first_name: child.user?.first_name || '',
        last_name: child.user?.last_name || '',
      })),
    },
  });

  // Add state to track if form was populated from token
  const [hasTokenData, setHasTokenData] = useState(false);

  // // Reference for the signature canvas
  // const signaturePadRef = useRef<SignatureCanvas>(null);
  const initRef = useRef(false);
  // Fetch tickets if orderId is provided
  const fetchRef = useRef(false);
  // Add this state to store signature data
  // const [signatureData, setSignatureData] = useState<string>('');

  // useEffect(() => {
  //   const handleResize = () => {
  //     // Restore signature from saved data when window is resized
  //     if (signatureData && signaturePadRef.current) {
  //       signaturePadRef.current.clear();
  //       setTimeout(() => {
  //         signaturePadRef.current?.fromDataURL(signatureData);
  //       }, 0);
  //     }
  //   };

  //   window.addEventListener('resize', handleResize);
  //   return () => window.removeEventListener('resize', handleResize);
  // }, [signatureData]);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    if (orderId && !fetchRef.current) {
      const preparedId = orderId.includes('or_') ? orderId : `or_${orderId}`;
      dispatch(fetchTicketsByOrder(preparedId));
      fetchRef.current = true;
    }
  }, [dispatch, orderId]);

  // Show error toast if submission fails
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  useEffect(() => {
    if (!initRef.current) {
      dispatch(fetchWaiverContent());
      initRef.current = true;
    }
  }, [dispatch]);

  useEffect(() => {
    if (tickets.length > 0) {
      setChildren(
        tickets.filter(ticket => {
          const ticketType = getTicketType(ticket.ticket_type || '');
          return ticketType === TICKET_TYPES.CHILD;
        })
      );

      const adult = tickets.filter(ticket => {
        const ticketType = getTicketType(ticket.ticket_type || '');
        return ticketType === TICKET_TYPES.ADULT;
      });

      if (adult && adult.length > 0) {
        setWaiverUserName(
          `${adult[0]?.user_details?.first_name} ${adult[0]?.user_details?.last_name}`
        );
        setValue('first_name', adult[0]?.user_details?.first_name);
        setValue('last_name', adult[0]?.user_details?.last_name);
        setValue('email', adult[0]?.user_details?.email || '');
        setValue('age_group', AGE_GROUPS.ADULT as 'adult');
      } else if (order && order.organizer) {
        setWaiverUserName(`${order?.organizer?.first_name} ${order?.organizer?.last_name}`);
        setValue('first_name', order?.organizer?.first_name);
        setValue('last_name', order?.organizer?.last_name);
        setValue('email', order?.organizer?.email || '');
        setValue('age_group', AGE_GROUPS.ADULT as 'adult');
      }
    } else {
      if (order && order.organizer) {
        setWaiverUserName(`${order?.organizer?.first_name} ${order?.organizer?.last_name}`);
        setValue('first_name', order?.organizer?.first_name);
        setValue('last_name', order?.organizer?.last_name);
        setValue('email', order?.organizer?.email || '');
        setValue('age_group', AGE_GROUPS.ADULT as 'adult');
      }
    }
  }, [tickets, order, setValue]); // Added setValue to dependencies

  // Handle URL token parameters
  useEffect(() => {
    // If token is provided, decode it and populate form fields
    if (token && !orderId) {
      try {
        const decodedData = decodeWaiverToken(token);
        setHasTokenData(true);
        // setIsOrganizer(decodedData.email === order?.organizer.email);

        if (decodedData.age_group === AGE_GROUPS.CHILD) {
          // For child waivers, use logged-in user's info for parent fields if available
          if (isAuthenticated && user) {
            // Use logged-in parent's information
            setWaiverUserName(`${user.first_name} ${user.last_name}`);
            setValue('first_name', user.first_name);
            setValue('last_name', user.last_name);
            setValue('email', user.email || '');
            setValue('age_group', AGE_GROUPS.ADULT as 'adult'); // Parent is always adult
          } else {
            // If not logged in, leave parent fields empty
            setValue('age_group', AGE_GROUPS.ADULT as 'adult');
          }

          // setIsChildWaiver(true);

          // Add child info to children array
          setChildren([
            {
              user: {
                first_name: decodedData.first_name,
                last_name: decodedData.last_name,
                age_group: AGE_GROUPS.CHILD,
                email: decodedData.email,
              } as Users,
            } as Tickets & { user: Users | null },
          ]);
        } else {
          // For adult waivers, proceed as normal
          setWaiverUserName(`${decodedData.first_name} ${decodedData.last_name}`);
          setValue('first_name', decodedData.first_name);
          setValue('last_name', decodedData.last_name);
          // Use nullish coalescing for safety, although email is expected
          setValue('email', decodedData.email ?? '');
          setValue('age_group', decodedData.age_group || AGE_GROUPS.ADULT);
        }
      } catch (_error) {
        setHasTokenData(false);
      }
    }
  }, [user, token, orderId, isAuthenticated, setValue]); // Added setValue to dependencies

  // Auto-fill form with user data when logged in
  useEffect(() => {
    if (isAuthenticated && user && !token && !orderId) {
      setWaiverUserName(`${user.first_name} ${user.last_name}`);
      setValue('first_name', user.first_name);
      setValue('last_name', user.last_name);
      setValue('email', user?.email ?? '');
      setValue('age_group', user?.age_group || AGE_GROUPS.ADULT);
      // setIsOrganizer(user.email === order?.organizer.email);
    }
  }, [isAuthenticated, user, token, orderId, setValue]); // Added setValue to dependencies

  // Cleanup function added to destroy the states of order and tickets
  useEffect(() => {
    return () => {
      // Reset the waiver state when component unmounts
      dispatch(clearWaiverState());
    };
  }, [dispatch]);

  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  // Handle form submission
  const onSubmit = async (data: WaiverFormValues) => {
    try {
      setIsSubmitting(true); // Set local loading state
      await dispatch(
        submitWaiver({
          ...data,
          waiver_version: waiverVersion,
          age_group: data.age_group as 'adult' | 'child' | 'senior',
        })
      ).unwrap();
      // Pass the nextUrl to the thank you page
      const thankYouUrl = nextUrl
        ? `${ROUTES.WAIVER_THANK_YOU}?nextUrl=${encodeURIComponent(nextUrl)}` // Encode nextUrl
        : ROUTES.WAIVER_THANK_YOU;
      router.push(thankYouUrl);
    } catch (error) {
      setIsSubmitting(false); // Clear local loading on error
      if (typeof error === 'string') {
        toast.error(error);
      } else {
        toast.error(ERROR_MESSAGES.FAILED_TO_SUBMIT_WAIVER);
      }
    }
  };
  /**
   * End Custom Methods
   */

  // Show loading spinner when form is being submitted OR during local submission
  if (loading || isSubmitting) {
    return <Spinner />;
  }

  return (
    <div className="max-w-4xl mx-auto mb-10 p-2 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Waiver and Limitation of Liability</h1>
        <p className="text-muted-foreground">
          Please complete before visiting the farm. Please bring the confirmation email to show at
          the U-Pick farm entrance and ensure each adult signs the waiver.
        </p>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 text-yellow-800">
        <ul className="list-disc pl-5 space-y-1">
          <li>
            Bags, Containers, Backpacks, Pull/Push Carts, Large Strollers, Wagons, Food are NOT
            Allowed in the Orchard. Please leave them in the Car.
          </li>
          <li>
            Dogs are not allowed. Only service dogs are permitted, but they are not allowed inside
            the orchard.
          </li>
        </ul>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl mx-auto space-y-6">
        {/* Show parent/booker info header if it's a child waiver or organizer */}
        <h1 className="font-bold">Organizer</h1>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <Label>First Name *</Label>
            <Input
              {...register('first_name', {
                onChange: e => {
                  const lastName = getValues('last_name');
                  setWaiverUserName(`${e.target.value} ${lastName}`);
                },
              })}
              className={isAuthenticated || hasTokenData ? 'bg-gray-100' : ''}
            />
            {errors.first_name && <p className="text-red-500">{errors.first_name.message}</p>}
          </div>
          <div>
            <Label>Last Name *</Label>
            <Input
              {...register('last_name', {
                onChange: e => {
                  const firstName = getValues('first_name');
                  setWaiverUserName(`${firstName} ${e.target.value}`);
                },
              })}
              className={isAuthenticated || hasTokenData ? 'bg-gray-100' : ''}
            />
            {errors.last_name && <p className="text-red-500">{errors.last_name.message}</p>}
          </div>
          <div>
            <Label>Email *</Label>
            <Input
              {...register('email')}
              type="email"
              className={isAuthenticated || hasTokenData ? 'bg-gray-100' : ''}
            />
            {errors.email && <p className="text-red-500">{errors.email.message}</p>}
          </div>
        </div>

        {/* Show children section for both child waiver and organizer cases */}
        {children.length > 0 && (
          <div className="space-y-4 mt-6">
            <h1 className="font-bold">Children Information</h1>
            {children.map((child, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <h2 className="font-semibold">Child {index + 1}</h2>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label>First Name *</Label>
                    <Input
                      {...register(`children.${index}.first_name` as const, {
                        value: child.user_details?.first_name || '',
                      })}
                      defaultValue={child.user_details?.first_name || ''}
                      className="bg-gray-100"
                    />
                    {errors.children?.[index]?.first_name && (
                      <p className="text-red-500">{errors.children[index].first_name?.message}</p>
                    )}
                  </div>
                  <div>
                    <Label>Last Name *</Label>
                    <Input
                      {...register(`children.${index}.last_name` as const, {
                        value: child.user_details?.last_name || '',
                      })}
                      defaultValue={child.user_details?.last_name || ''}
                      className="bg-gray-100"
                    />
                    {errors.children?.[index]?.last_name && (
                      <p className="text-red-500">{errors.children[index].last_name?.message}</p>
                    )}
                  </div>
                  {child.user_details?.email !== order?.organizer?.email && ( // Corrected access
                    <div>
                      <Label>Email *</Label>
                      <Input
                        {...register(`children.${index}.email` as const, {
                          value: child.user_details?.email || '',
                        })}
                        defaultValue={child.user_details?.email || ''}
                        className="bg-gray-100"
                      />
                      {errors.children?.[index]?.last_name && (
                        <p className="text-red-500">{errors.children[index].last_name?.message}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="space-y-4">
          <Label className="font-semibold">
            Waiver Agreement {waiverVersion && `(v${waiverVersion})`}
          </Label>

          <div className="border rounded-lg bg-card p-4 max-h-[400px] overflow-y-auto space-y-6">
            {waiverContent ? (
              <div dangerouslySetInnerHTML={{ __html: waiverContent }} />
            ) : (
              <Spinner />
            )}
          </div>
        </div>

        <Controller
          name="mailingList"
          control={control}
          render={({ field }) => (
            <div className="flex items-center space-x-2 mt-4">
              <Checkbox
                id="mailingList"
                checked={field.value}
                required
                onCheckedChange={field.onChange}
              />
              <Label htmlFor="mailingList" className="cursor-pointer">
                I am signing up for the &quot;My Very Mulberry&quot; mailing list to receive
                important farm updates, future special offers.
              </Label>
            </div>
          )}
        />

        {/* <Controller
          name="signature"
          control={control}
          render={({ field }) => (
            <div>
              <Label>
                I am digitally signing and agreeing to observe all safety precautions as outlined
                here. I accept the waiver and limitation of liability as detailed below. *
              </Label>
              <div className="relative">
                <SignatureCanvas
                  canvasProps={{ className: 'border border-gray-300 w-full h-40' }}
                  onEnd={() => {
                    const canvas = signaturePadRef.current;
                    if (canvas) {
                      const dataURL = canvas.toDataURL();
                      field.onChange(dataURL);
                      setSignatureData(dataURL); // Save signature data
                    }
                  }}
                  ref={signaturePadRef}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="absolute bottom-2 right-2"
                  onClick={() => {
                    const canvas = signaturePadRef.current;
                    if (canvas) {
                      canvas.clear();
                      field.onChange('');
                    }
                  }}
                >
                  Reset Signature
                </Button>
              </div>
              {errors.signature && <p className="text-red-500">{errors.signature.message}</p>}
            </div>
          )}
        /> */}

        <Controller
          name="agreementAccepted"
          control={control}
          render={({ field }) => (
            <div className="flex items-center space-x-2 mt-4">
              <Checkbox
                id="agreementAccepted"
                checked={field.value}
                required
                onCheckedChange={field.onChange}
              />
              <Label htmlFor="agreementAccepted" className="cursor-pointer">
                <b>
                  {`/s/ `}
                  {waiverUserName}
                </b>
                {` I acknowledge and accept the safety rules and liability waiver as stated above. This serves as my digital signature.`}
              </Label>
            </div>
          )}
        />
        {errors.agreementAccepted && (
          <p className="text-red-500">{errors.agreementAccepted.message}</p>
        )}

        <Button
          type="submit"
          className="w-full  bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive"
        >
          {BUTTON_TEXTS.ACCEPT_WAIVER_AND_COMPLETE}
        </Button>
      </form>
    </div>
  );
};

export default WaiverForm;
