'use client';

import { parseISO, isBefore, startOfDay, isAfter, format, isValid } from 'date-fns';
import { CalendarIcon, MapPinIcon, TicketsIcon } from 'lucide-react';
import React, { useState, useMemo } from 'react';

import { But<PERSON> } from '@/src/components/ui/button';
import { Calendar } from '@/src/components/ui/calendar';
import { Card, CardHeader, CardContent } from '@/src/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/src/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/src/components/ui/select';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { cn, formatDate, formatTime } from '@/src/lib/utils';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';

/**
 * Props interface for the EventCard component
 * @interface EventCardProps
 * @property {Events} event - The event details including title, description, location, and date range
 * @property {EventSchedules} schedule - The specific schedule details including start/end times
 */
interface EventScheduleCardProps {
  event: Events;
  onScheduleSelect: (schedule: EventSchedules | undefined) => void;
}

const EventScheduleCard: React.FC<EventScheduleCardProps> = ({ event, onScheduleSelect }) => {
  // Check if event is expired
  const isEventExpired = useMemo(() => {
    if (!event.end_date) return false;
    const endDate = parseISO(event.end_date.toString());
    return isBefore(endDate, startOfDay(getPacificTimeNow()));
  }, [event.end_date]);

  // Helper function to check if a time slot is passed
  const isTimeSlotPassed = (date: Date, timeString: string) => {
    const now = getPacificTimeNow();
    const [hours, minutes] = timeString.split(':').map(Number);
    const slotTime = new Date(date);
    slotTime.setHours(hours, minutes, 0, 0);
    return isBefore(slotTime, now);
  };

  // Group schedules by date, filtering out past dates and past time slots
  const schedulesByDate = useMemo(() => {
    const grouped = new Map<string, EventSchedules[]>();
    const today = getPacificTimeNow();
    const todayStart = startOfDay(today);

    event.event_schedules?.forEach(schedule => {
      if (schedule.event_date) {
        const date = parseISO(schedule.event_date.toString());

        // Skip invalid dates
        if (!isValid(date)) return;

        // Skip past dates
        if (isBefore(date, todayStart)) return;

        const dateKey = format(date, 'yyyy-MM-dd');

        // For today, filter out past time slots
        if (format(date, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')) {
          if (schedule.start_time && isTimeSlotPassed(date, schedule.start_time)) return;
        }

        if (!grouped.has(dateKey)) {
          grouped.set(dateKey, []);
        }
        grouped.get(dateKey)?.push(schedule);
      }
    });

    // Remove dates that have no available time slots
    for (const [dateKey, schedules] of grouped.entries()) {
      if (schedules.length === 0) {
        grouped.delete(dateKey);
      }
    }

    return grouped;
  }, [event.event_schedules]);

  // State for selected date and schedule
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [selectedSchedule, setSelectedSchedule] = useState<EventSchedules | undefined>();

  // Get available time slots for selected date
  const availableTimeSlots = useMemo(() => {
    if (!selectedDate) return [];

    const dateKey = selectedDate.toISOString().split('T')[0];
    const slots = schedulesByDate.get(dateKey) || [];

    // If selected date is today, filter out past time slots
    if (format(selectedDate, 'yyyy-MM-dd') === format(getPacificTimeNow(), 'yyyy-MM-dd')) {
      return slots.filter(
        slot => slot.start_time && !isTimeSlotPassed(selectedDate, slot.start_time)
      );
    }

    return slots;
  }, [selectedDate, schedulesByDate]);

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    setSelectedSchedule(undefined); // Reset time selection when date changes
    onScheduleSelect(undefined); // Reset parent component's selected schedule
  };

  // Handle time slot selection
  const handleTimeSelect = (scheduleId: string) => {
    const schedule = availableTimeSlots.find(slot => slot.id === scheduleId);
    setSelectedSchedule(schedule);
    onScheduleSelect(schedule);
  };

  // If event is expired, show message
  if (isEventExpired) {
    return (
      <Card className="p-3 shadow-md max-w-2xl mx-auto mb-3">
        <div className="text-center text-red-600 py-4">This event has already ended.</div>
      </Card>
    );
  }

  // If no future schedules available
  if (schedulesByDate.size === 0) {
    return (
      <Card className="p-3 shadow-md max-w-2xl mx-auto mb-3">
        <div className="text-center text-gray-600 py-4">
          No upcoming schedules available for this event.
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-3 shadow-md max-w-2xl mx-auto mb-3">
      {/* Date and Time Selection Section */}
      <div className="flex max-md:flex-col items-center mb-4 gap-2">
        {/* Date Picker */}
        <div className="flex items-center w-full">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'w-full justify-start text-left font-normal',
                  !selectedDate && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? formatDate(selectedDate) : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                initialFocus
                disabled={date => {
                  const today = startOfDay(getPacificTimeNow());

                  // Disable past dates
                  if (isBefore(date, today)) return true;

                  // Check if date is within event's range
                  const startDate = event.start_date
                    ? parseISO(event.start_date.toString())
                    : today;
                  const endDate = event.end_date
                    ? parseISO(event.end_date.toString())
                    : new Date(today.getFullYear() + 1, today.getMonth(), today.getDate());

                  const isWithinRange = !isBefore(date, startDate) && !isAfter(date, endDate);
                  if (!isWithinRange) return true;

                  // Check if this date has available schedules
                  const dateKey = date.toISOString().split('T')[0];
                  return !schedulesByDate.has(dateKey);
                }}
                modifiers={{
                  highlighted: date => {
                    const dateKey = date.toISOString().split('T')[0];
                    return schedulesByDate.has(dateKey);
                  },
                }}
                modifiersStyles={{
                  highlighted: { backgroundColor: 'rgba(0, 0, 0, 0.05)' },
                }}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Time Selector */}
        <div className="flex items-center w-full">
          <Select
            value={selectedSchedule?.id}
            onValueChange={handleTimeSelect}
            disabled={!selectedDate || availableTimeSlots.length === 0}
          >
            <SelectTrigger className="w-full">
              <SelectValue
                placeholder={
                  selectedDate && availableTimeSlots.length === 0
                    ? 'No available time slots'
                    : 'Select Time'
                }
              >
                {selectedSchedule
                  ? `${formatTime(selectedSchedule.start_time)} - ${formatTime(selectedSchedule.end_time)}`
                  : 'Select Time'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {availableTimeSlots.map(schedule => (
                <SelectItem key={schedule.id} value={schedule.id || ''}>
                  {`${formatTime(schedule.start_time)} - ${formatTime(schedule.end_time)}`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Event Details Section */}
      <CardHeader className="text-md flex font-semibold p-0 py-1 m-0">
        <span className="flex items-center gap-2">
          <TicketsIcon size={18} /> {event.title}
        </span>
      </CardHeader>

      <CardContent className="text-gray-600 text-sm p-0 py-1 m-0">{event.description}</CardContent>

      {/* Location Information */}
      <div className="flex items-center text-sm text-gray-800 mt-2 gap-2">
        <MapPinIcon size={18} />
        {event.event_location?.name}, {event.event_location?.city}, {event.event_location?.state},{' '}
        {event.event_location?.country}
      </div>
    </Card>
  );
};

export default EventScheduleCard;
