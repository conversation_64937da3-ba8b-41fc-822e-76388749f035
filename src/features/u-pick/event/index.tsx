'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import ErrorAlert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Card, CardTitle } from '@/src/components/ui/card';
import { BUTTON_TEXTS, ROUTES } from '@/src/lib/constants';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';
import { EventSchedules } from '@/src/types/EventSchedules';

import { fetchEventById } from '../slices/guestsSlice';

import EventScheduleCard from './EventScheduleCard';

const Event = () => {
  const params = useParams();
  const router = useRouter();
  const eventId = params.eventId as string;
  const dispatch = useAppDispatch();
  const { event, loading, error } = useAppSelector((state: RootState) => state.guests);
  const [selectedSchedule, setSelectedSchedule] = useState<EventSchedules | null>(null);

  useEffect(() => {
    if (eventId) {
      dispatch(fetchEventById(eventId));
    }
  }, [dispatch, eventId]);

  const handleScheduleSelect = (schedule: EventSchedules | undefined) => {
    setSelectedSchedule(schedule || null);
  };

  const handleBookNow = () => {
    if (event?.id && selectedSchedule?.id) {
      router.push(ROUTES.EVENT_GUESTS(event.id, selectedSchedule.id));
    }
  };

  if (loading) return <Spinner />;
  if (error) return <ErrorAlert error={error} title="Error" />;
  if (!event) return null;

  return (
    <div className="max-w-2xl mx-auto pb-6">
      <EventScheduleCard event={event} onScheduleSelect={handleScheduleSelect} />

      {/* Guidelines */}
      {event.guidelines?.length > 0 && (
        <Card className="shadow-md p-3 mx-auto mb-3">
          <div className="mb-3">
            <CardTitle>Guidelines</CardTitle>
          </div>
          <div>
            <ul className="space-y-3">
              {event.guidelines.map((guideline: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-red-600">•</span>
                  <span>{guideline}</span>
                </li>
              ))}
            </ul>
          </div>
        </Card>
      )}

      {/* What to Expect */}
      {event.what_to_expect?.length > 0 && (
        <Card className="shadow-md p-3 mx-auto mb-3">
          <div className="mb-3">
            <CardTitle>What to Expect</CardTitle>
          </div>
          <div>
            <ul className="space-y-3">
              {event.what_to_expect.map((item: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-purple-600">•</span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </Card>
      )}
      {/* Book Now Button */}
      <div className="mb-6">
        <Button
          onClick={handleBookNow}
          disabled={!selectedSchedule}
          className="w-full bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {!selectedSchedule ? 'Select a Schedule' : BUTTON_TEXTS.BOOK_NOW}
        </Button>
      </div>
    </div>
  );
};

export default Event;
