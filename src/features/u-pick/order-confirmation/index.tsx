'use client';

import { useParams, useSearchParams } from 'next/navigation';
import { useEffect, useRef } from 'react';

import ErrorAlert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import OrderDetailsCard from '@/src/features/u-pick/order-confirmation/OrderDetailsCard';
import { fetchOrderDetails, updateOrderStatus } from '@/src/features/u-pick/slices/guestsSlice';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';
import { Tickets } from '@/src/types/Tickets';

const OrderConfirmation = () => {
  /* Start Initials */
  const params = useParams();
  const searchParams = useSearchParams();
  const orderId = params.orderId as string;
  const transactionId = searchParams.get('transactionId');
  const squareOrderId = searchParams.get('orderId');
  const fetchRef = useRef(false); // Add ref to track fetch status

  const dispatch = useAppDispatch();

  const { loading, error, order, tickets } = useAppSelector((state: RootState) => state.guests);
  /* End Initials */

  /* Start Lifecycle Methods */
  useEffect(() => {
    if (!fetchRef.current) {
      fetchOrUpdateOrderDetails();
      fetchRef.current = true;
    }
  }, [orderId, transactionId, squareOrderId]);
  /* End Lifecycle Methods */

  /* Start Custom Methods */
  /**
   * Fetch order details if transactionId and squareOrderId are not present in the URL
   * Update order status if transactionId and squareOrderId are present in the URL
   */
  const fetchOrUpdateOrderDetails = async () => {
    if (transactionId && squareOrderId) {
      await dispatch(updateOrderStatus({ orderId, transactionId, squareOrderId }));
    } else {
      dispatch(fetchOrderDetails(orderId));
    }
  };
  /* End Custom Methods */

  if (loading) {
    return <Spinner />;
  }

  if (error) {
    return <ErrorAlert error={error} />;
  }

  return <>{order && <OrderDetailsCard order={order} tickets={tickets as Tickets[]} />}</>;
};

export default OrderConfirmation;
