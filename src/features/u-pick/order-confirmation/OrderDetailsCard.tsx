import { PDFDownloadLink } from '@react-pdf/renderer';
import { format } from 'date-fns';
import {
  CalendarIcon,
  CheckIcon,
  ClockIcon,
  Download,
  Eye,
  MapPinIcon,
  TicketsIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { toast } from 'sonner';

import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/src/components/ui/popover';
import { AGE_GROUPS, BUTTON_TEXTS, ROUTES } from '@/src/lib/constants';
import { formatEventLocation, formatTime, generateWaiverToken } from '@/src/lib/utils';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Orders } from '@/src/types/Orders';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

import TicketPDF from '../tickets/TicketPDF';

/**
 * Props interface for the OrderDetailsCard component
 * @interface OrderDetailsCardProps
 * @property {Orders & { event: Events | null, schedule: EventSchedules | null }} order - Order details including event and schedule
 * @property {Tickets[]} tickets - Array of ticket details
 */
interface OrderDetailsCardProps {
  order: Orders & {
    event: Events | null;
    schedule: EventSchedules | null | undefined;
    waiver_count: number;
    booker: Users | undefined;
    pending_child_waivers?: { first_name: string; last_name: string }[];
  };
  tickets: Tickets[] | [];
}

const OrderDetailsCard = ({ order, tickets }: OrderDetailsCardProps) => {
  /** Start Initials */
  const router = useRouter();
  /** End Initials */

  /** Start Functions */
  /**
   * Counts the number of adults and children in the tickets array and calculates the total price.
   * @param {Tickets[]} tickets - The array of tickets to count and calculate.
   * @returns {Object} An object containing the counts and prices for adults and children, and the total price.
   */
  const countGuestsAndGetPrice = (tickets: Tickets[]): object => {
    return tickets.reduce(
      (result, ticket) => {
        const ageGroup = ticket?.user?.age_group; // Ensure correct access
        const adultPrice = ticket?.schedule?.price_per_adult || 0;
        const childPrice = ticket?.schedule?.price_per_child || 0;

        if (ageGroup === AGE_GROUPS.ADULT) {
          result.adultCount += 1;
          result.adultPrice = adultPrice; // Store price, not total
        } else if (ageGroup === AGE_GROUPS.CHILD) {
          result.childCount += 1;
          result.childPrice = childPrice; // Store price, not total
        }

        result.totalPrice =
          result.adultCount * result.adultPrice + result.childCount * result.childPrice;
        return result;
      },
      {
        adultCount: 0,
        childCount: 0,
        adultPrice: 0, // Stores only price, not total
        childPrice: 0, // Stores only price, not total
        totalPrice: 0, // Stores calculated total
      }
    );
  };

  // Destructure the result of countGuestsAndGetPrice
  interface GuestCounts {
    adultCount: number;
    childCount: number;
    adultPrice: number;
    childPrice: number;
    totalPrice: number;
  }

  const { adultCount, childCount, adultPrice, childPrice, totalPrice }: GuestCounts =
    countGuestsAndGetPrice(tickets) as GuestCounts;

  /**
   * Navigates to the sign waiver page with a generated token.
   * If the order has a booker, the token includes the booker's details.
   * Otherwise, it navigates to the sign waiver page without a token.
   */
  const navigateToSignWaiver = () => {
    if (order?.booker) {
      const token = generateWaiverToken({
        first_name: order.booker.first_name,
        last_name: order.booker.last_name,
        email: order.booker.email || '',
      } as Users);

      router.push(`${ROUTES.SIGN_WAIVER}?t=${token}&orderId=${order.id}`);
    } else {
      router.push(`${ROUTES.SIGN_WAIVER}?orderId=${order.id}`);
    }
  };

  const navigateToTickets = () => {
    router.push(ROUTES.RESERVATIONS);
  };
  /** End Functions */

  const addToGoogleCalendar = async () => {
    try {
      if (!order?.event || !order?.schedule) {
        throw new Error('Missing event or schedule information');
      }

      const { event, schedule } = order;

      // Format event details
      const title = event.title || 'Event';
      const baseDescription = event.description || '';
      const location = formatEventLocation(event.event_location || {});

      // Create ticket details section
      const ticketDetails = tickets
        .map(ticket => {
          const ticketUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/tickets/${ticket.id}`;
          return `\n\nTicket #${ticket.ticket_id}:\n${ticketUrl}`;
        })
        .join('');

      // Combine base description with ticket details
      const description = `${baseDescription}${ticketDetails}`;

      // Helper function to format datetime for Google Calendar
      const createDateTime = (timeString: string) => {
        if (!schedule.event_date || !timeString) return '';

        if (!schedule.event_date) throw new Error('Event date is required');
        const dateTime = new Date(schedule.event_date);
        const [hours, minutes] = timeString.split(':');
        dateTime.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);

        return dateTime.toISOString().replace(/[-:]|\.\d{3}/g, '');
      };

      // Create start and end times
      const start = schedule.start_time ? createDateTime(schedule.start_time) : '';
      const end = schedule.end_time ? createDateTime(schedule.end_time) : '';

      // Format attendees
      const formatAttendees = () => {
        let attendeeString = '';

        if (tickets?.length) {
          attendeeString = tickets
            .filter(ticket => ticket.user?.email?.trim())
            .map(ticket => `&add=${encodeURIComponent(ticket.user?.email || '')}`)
            .join('');
        }

        return attendeeString;
      };

      // Construct Google Calendar URL
      const calendarUrl = new URL('https://www.google.com/calendar/render');
      calendarUrl.searchParams.append('action', 'TEMPLATE');
      calendarUrl.searchParams.append('text', title);
      calendarUrl.searchParams.append('dates', `${start}/${end}`);
      calendarUrl.searchParams.append('details', description);
      calendarUrl.searchParams.append('location', location);

      const finalUrl = calendarUrl.toString() + formatAttendees();

      // Open calendar in new window
      const newWindow = window.open(finalUrl, '_blank');
      if (!newWindow) {
        throw new Error('Pop-up blocker may be preventing the calendar from opening');
      }
    } catch (_error) {
      toast.error('Failed to add to Google Calendar');
    }
  };

  const createICSFile = () => {
    try {
      if (!order?.event || !order?.schedule) {
        throw new Error('Missing event or schedule information');
      }

      const { event, schedule } = order;

      if (!schedule.event_date || !schedule.start_time || !schedule.end_time) {
        throw new Error('Missing date or time information');
      }

      // Format event details
      const title = event.title || 'Event';
      const baseDescription = event.description || '';
      const location = formatEventLocation(event.event_location || {});

      // Create ticket details section
      const ticketDetails = tickets
        .map(ticket => {
          const ticketUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/tickets/${ticket.id}`;
          return `\\n\\nTicket #${ticket.ticket_id}:\\n${ticketUrl}`;
        })
        .join('');

      // Combine base description with ticket details
      const description = `${baseDescription}${ticketDetails}`;

      // Helper function to format datetime for ICS
      const formatICSDateTime = (timeString: string) => {
        if (!schedule.event_date) {
          throw new Error('Event date is required');
        }
        const dateTime = new Date(schedule.event_date);
        const [hours, minutes] = timeString.split(':');
        dateTime.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);
        return dateTime.toISOString().replace(/[-:]|\.\d{3}/g, '');
      };

      // Create start and end times
      const start = formatICSDateTime(schedule.start_time);
      const end = formatICSDateTime(schedule.end_time);

      // Format organizer and attendees
      const organizerLine = order.booker?.email
        ? `ORGANIZER;CN=${order.booker.first_name} ${order.booker.last_name}:mailto:${order.booker.email}`
        : '';

      const allAttendees = tickets
        .filter(ticket => ticket.user?.email)
        .map(
          ticket =>
            `ATTENDEE;CN=${ticket.user?.first_name} ${ticket.user?.last_name}:mailto:${ticket.user?.email}`
        );

      const formattedLocation = location.replace(/,\s+/g, '\\, ').replace(/\n/g, '\\n');

      // Create ICS content
      const icsContent = [
        'BEGIN:VCALENDAR',
        'VERSION:2.0',
        'PRODID:-//Augment Code//Event Calendar//EN',
        'CALSCALE:GREGORIAN',
        'METHOD:REQUEST',
        'BEGIN:VEVENT',
        `DTSTART:${start}`,
        `DTEND:${end}`,
        `SUMMARY:${title}`,
        `DESCRIPTION:${description.replace(/\n/g, '\\n')}`,
        `LOCATION:${formattedLocation}`,
        organizerLine,
        ...allAttendees,
        //TODO-DATE
        `UID:${schedule.id}-${Date.now()}@yourdomain.com`,
        'STATUS:CONFIRMED',
        'SEQUENCE:0',
        'CLASS:PUBLIC',
        'TRANSP:OPAQUE',
        'END:VEVENT',
        'END:VCALENDAR',
      ]
        .filter(Boolean)
        .join('\r\n');

      // Create and download the file
      const blob = new Blob([icsContent], {
        type: 'text/calendar;charset=utf-8;method=REQUEST',
      });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.setAttribute('download', `${title}.ics`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Calendar file downloaded! Open the file to add this event to your calendar.');

      // Cleanup
      window.URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Error creating ICS file:', error);
      toast.error('Failed to create calendar file');
    }
  };

  const handleLocationClick = (
    latitude: number | string | undefined,
    longitude: number | string | undefined
  ) => {
    if (latitude && longitude) {
      const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;
      window.open(mapsUrl, '_blank');
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-4 space-y-4">
      {/* Success Message */}
      <div className="bg-green-50 p-6 border-2 border-green-100 rounded-lg text-center mb-6">
        <div className="flex justify-center mb-4">
          <div className="bg-green-100 p-2 rounded-full">
            <CheckIcon className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <h2 className="text-xl font-semibold text-green-800 mb-2">Booking Confirmed!</h2>
        <p className="text-green-700">Order ID: {order?.id}</p>
        <div className="flex justify-center gap-4 mt-6">
          <div className="flex justify-center max-md:flex-col max-md:w-full gap-4 mt-6">
            <Button
              onClick={navigateToTickets}
              variant="outline"
              className="flex items-center max-md:w-full gap-2 border-2 hover:bg-gray-50"
            >
              <Eye className="h-4 w-4" />
              {BUTTON_TEXTS.VIEW_RESERVATIONS}
            </Button>
            <PDFDownloadLink
              document={<TicketPDF tickets={tickets} order={order} />}
              fileName={`tickets-${order?.id || 'download'}.pdf`}
              style={{
                textDecoration: 'none',
              }}
            >
              {({ loading }) => (
                <Button
                  variant="outline"
                  className="flex items-center max-md:w-full gap-2 border-2 hover:bg-gray-50"
                  disabled={loading}
                >
                  <Download className="h-4 w-4" />
                  {loading ? BUTTON_TEXTS.LOADING : BUTTON_TEXTS.DOWNLOAD}
                </Button>
              )}
            </PDFDownloadLink>
          </div>
        </div>
      </div>

      <div className="bg-red-50 p-4 border-2 border-red-100 rounded-lg text-center mb-6">
        <p className="text-red-800">
          Please note that every person attending, including yourself and any guests coming with
          you, must sign a waiver before entry.
        </p>
        <div className="flex justify-center gap-2 mt-6">
          {(order.waiver_count === 0 ||
            (order.pending_child_waivers && order.pending_child_waivers.length > 0)) && (
            <Button
              className="flex items-center max-md:w-full gap-1 bg-mulberry hover:text-white text-white hover:bg-mulberryHover active:bg-mulberryActive"
              onClick={navigateToSignWaiver}
            >
              {BUTTON_TEXTS.SIGN_WAIVER}
            </Button>
          )}
        </div>
      </div>

      {/* Event Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="text-lg flex gap-2">
              <TicketsIcon /> {order?.event?.title}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start gap-3">
            <CalendarIcon className="h-5 w-5 text-gray-500 flex-shrink-0" />
            <div>
              {order?.schedule?.event_date
                ? format(new Date(order.schedule.event_date), 'MMMM d, yyyy')
                : 'Date not set'}
            </div>
          </div>
          <div className="flex items-start gap-3">
            <ClockIcon className="h-5 w-5 text-gray-500 flex-shrink-0" />
            <div>
              {formatTime(order?.schedule?.start_time)} - {formatTime(order?.schedule?.end_time)}
            </div>
          </div>
          <div className="flex items-start gap-3">
            <MapPinIcon className="h-5 w-5 text-gray-500 flex-shrink-0" />
            <div
              onClick={() =>
                handleLocationClick(
                  order?.event?.event_location?.latitude,
                  order?.event?.event_location?.longitude
                )
              }
              className="hover:text-mulberry cursor-pointer"
            >
              {order?.event?.event_location &&
                `${order.event.event_location.name}, ${
                  order.event.event_location.city
                }, ${order.event.event_location.state}${
                  order.event.event_location.zip_code
                    ? `, ${order.event.event_location.zip_code}`
                    : ''
                }`}
            </div>
          </div>

          {/* Add to Calendar Button */}
          <Popover>
            <PopoverTrigger asChild>
              <Button className="w-full bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white text-sm rounded-full">
                <CalendarIcon className="mr-2 h-4 w-4" />
                Add to Calendar
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64">
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  className="w-full justify-start text-left font-normal"
                  onClick={addToGoogleCalendar}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  Add to Google Calendar
                </Button>
                <Button
                  variant="ghost"
                  className="w-full justify-start text-left font-normal"
                  onClick={createICSFile}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  Add to Personal Calendar
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span>Adult Ticket × {adultCount}</span>
              <span>${adultPrice}</span>
              <span>${adultPrice * adultCount}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Child Ticket × {childCount}</span>
              <span>${childPrice}</span>
              <span>${childPrice * childCount}</span>
            </div>
            <div className="border-t pt-3 flex justify-between items-center font-semibold">
              <span>Total Amount</span>
              <span>${totalPrice}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Event Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm space-y-2">
            {order?.event?.instructions?.map((instruction, index) => (
              <p key={index}>• {instruction}</p>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrderDetailsCard;
