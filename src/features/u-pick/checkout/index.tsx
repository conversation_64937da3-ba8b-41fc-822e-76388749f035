'use client';

import { QrC<PERSON>, ChevronLeft, ShoppingCartIcon } from 'lucide-react';
import Image from 'next/image';
import { useRouter, useParams } from 'next/navigation';
import QRCode from 'qrcode';
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Button } from '@/src/components/ui/button';
import { Dialog, DialogContent } from '@/src/components/ui/dialog';
import { ROUTES, SUCCESS_MESSAGES, TICKET_TYPES } from '@/src/lib/constants';
import { getTicketType } from '@/src/lib/utils';
import { RootState, useAppSelector, useAppDispatch } from '@/src/store';
import { Tickets } from '@/src/types/Tickets';

import {
  redeemClamshells,
  setClamshells as setCheckoutClamshells,
  setOrderId,
} from '../slices/checkoutSlice';

const Checkout = () => {
  /**
   * Start Initials
   */
  const [qrCodes, setQrCodes] = useState<{ [key: string]: string }>({});
  const [selectedQrCode, setSelectedQrCode] = useState<string | null>(null);

  const dispatch = useAppDispatch();
  const { activeTickets } = useAppSelector((state: RootState) => state.guests);
  const { clamshells } = useAppSelector((state: RootState) => state.checkout);
  const [redeemingId, setRedeemingId] = useState<string | null>(null);
  const [bulkRedeeming, setBulkRedeeming] = useState<boolean>(false);

  const router = useRouter();
  const params = useParams();
  const orderId = params.orderId as string;

  const totalClamshells = clamshells.length;
  const redeemedCount = clamshells.filter(cs => cs.check_in).length;
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Add useEffect to generate QR codes when clamshells change
  useEffect(() => {
    const loadQRCodes = async () => {
      const codes: { [key: string]: string } = {};
      for (const clamshell of clamshells) {
        if (clamshell.ticket_id) {
          codes[clamshell.ticket_id] = await generateQRCode(clamshell.ticket_id);
        }
      }
      setQrCodes(codes);
    };

    loadQRCodes();
  }, [clamshells]);

  // Extract clamshells from activeTickets
  useEffect(() => {
    // Set the orderId in the checkout state
    dispatch(setOrderId(orderId));

    if (!activeTickets || activeTickets.length === 0) {
      // Redirect back if no active tickets
      router.push(ROUTES.RESERVATIONS);
      return;
    }

    const extractedClamshells: Tickets[] = [];

    // Loop through activeTickets to find clamshells
    activeTickets.forEach((tickets: Record<string, Tickets[]>) => {
      if (tickets[orderId]) {
        tickets[orderId].forEach(ticket => {
          const type = getTicketType(ticket.ticket_type || '');
          if (type === TICKET_TYPES.CLAM_SHELL) {
            extractedClamshells.push(ticket);
          }
        });
        return;
      }
    });

    dispatch(setCheckoutClamshells(extractedClamshells));

    if (!extractedClamshells || extractedClamshells.length === 0) {
      // Redirect back if no active tickets
      router.push(ROUTES.RESERVATIONS);
      return;
    }
  }, [activeTickets, orderId, router, dispatch]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  // Add function to generate QR code
  const generateQRCode = async (value: string) => {
    try {
      return await QRCode.toDataURL(value, {
        width: 80,
        margin: 0,
      });
    } catch (err) {
      console.error('Error generating QR code:', err);
      return '';
    }
  };

  const handleRedeem = async (id: string) => {
    try {
      setRedeemingId(id);
      await dispatch(redeemClamshells([id])).unwrap();
      toast.success(SUCCESS_MESSAGES.CLAMSHELL_REDEEMED);
    } catch (error) {
      toast.error(error as string);
    } finally {
      setRedeemingId(null);
    }
  };

  const handleRedeemAll = async () => {
    try {
      // Get all unredeemed clamshells
      const unredeemedClamshellIds = clamshells
        .filter(cs => !cs.check_in)
        .map(cs => cs.id as string);

      if (unredeemedClamshellIds.length === 0) {
        return;
      }

      setBulkRedeeming(true);
      await dispatch(redeemClamshells(unredeemedClamshellIds)).unwrap();
      toast.success(SUCCESS_MESSAGES.ALL_CLAMSHELLS_REDEEMED);
    } catch (error) {
      toast.error(error as string);
    } finally {
      setBulkRedeeming(false);
    }
  };

  const handleQrCodeClick = (qrCode: string) => {
    setSelectedQrCode(qrCode);
  };
  /**
   * End Custom Methods
   */

  return (
    <div className="container mx-auto w-full">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="sm"
            className="mr-4 text-mulberry hover:bg-mulberryLight flex items-center gap-1"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-5 w-5" />
            <span>Back</span>
          </Button>
          <ShoppingCartIcon className="h-7 w-7 text-mulberry mr-2" />
          <h1 className="text-xl md:text-2xl font-bold">Clamshell Redemption</h1>
        </div>
        {/* Only show Redeem All button when there are multiple unredeemed clamshells */}
        {totalClamshells - redeemedCount > 1 && (
          <Button
            onClick={handleRedeemAll}
            className="bg-mulberry hover:bg-mulberryHover text-white"
            disabled={bulkRedeeming}
          >
            {bulkRedeeming ? 'Processing...' : 'Redeem All'}
          </Button>
        )}
      </div>

      <div className="space-y-4 mb-8">
        {clamshells.map(clamshell => (
          <div
            key={clamshell.id}
            className="border rounded-lg p-4 flex justify-between items-center bg-white shadow-sm"
          >
            <div className="flex items-center">
              <div className="bg-gray-100 p-3 rounded-lg mr-4">
                {clamshell.ticket_id && qrCodes[clamshell.ticket_id] ? (
                  <Image
                    src={qrCodes[clamshell.ticket_id]}
                    alt="QR Code"
                    width={40}
                    height={40}
                    className="cursor-pointer"
                    onClick={() => handleQrCodeClick(qrCodes[clamshell.ticket_id as string])}
                  />
                ) : (
                  <QrCode className="h-10 w-10 text-gray-700" />
                )}
              </div>
              <div>
                <h3 className="font-medium">{clamshell.ticket_type}</h3>
                <p className="text-sm text-gray-500">ID: {clamshell.ticket_id}</p>
                <p className="text-sm text-gray-500">Barcode: {clamshell?.barcode}</p>
              </div>
            </div>
            <Button
              onClick={() => handleRedeem(clamshell.id as string)}
              className={
                clamshell.check_in
                  ? 'bg-green-100 text-green-800 hover:bg-green-100 cursor-default'
                  : 'bg-mulberry hover:bg-mulberryHover text-white'
              }
              disabled={
                (clamshell?.check_in && clamshell?.check_in?.length > 1) ||
                redeemingId === clamshell.id
              }
            >
              {redeemingId === clamshell.id
                ? 'Processing...'
                : clamshell.check_in
                  ? 'Redeemed'
                  : 'Redeem'}
            </Button>
          </div>
        ))}
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border mb-4">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-gray-700">
              Total Clamshells: <span className="font-bold">{totalClamshells}</span>
              <span className="mx-2 text-gray-400">•</span>
              Redeemed: <span className="font-bold">{redeemedCount}</span>
            </p>
          </div>
          <div className="text-right">
            <Button
              className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-xs sm:text-sm w-[140px] sm:w-[180px] min-w-[140px] sm:min-w-[180px] h-9 sm:h-11 px-3 sm:px-5 font-semibold"
              onClick={() =>
                window.open(process.env.NEXT_PUBLIC_CHECKOUT_STORE_URL || '', '_blank')
              }
            >
              Buy more clamshells
            </Button>
          </div>
        </div>
      </div>

      {/* QR Code Modal */}
      <Dialog open={!!selectedQrCode} onOpenChange={open => !open && setSelectedQrCode(null)}>
        <DialogContent className="sm:max-w-md p-0 bg-transparent border-none shadow-none">
          <div
            className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedQrCode(null)}
          >
            <div
              className="bg-white p-6 rounded-lg max-w-sm w-full flex flex-col items-center"
              onClick={e => e.stopPropagation()}
            >
              <Image
                src={selectedQrCode || ''}
                alt="QR Code"
                width={256}
                height={256}
                className="w-64 h-64 mb-4"
                unoptimized
              />
              <h2 className="text-center text-xl mb-4 text-gray-600">
                {clamshells.find(
                  clamshell =>
                    clamshell.ticket_id && qrCodes[clamshell.ticket_id] === selectedQrCode
                )?.barcode || ''}
              </h2>
              <Button onClick={() => setSelectedQrCode(null)} className="w-full" variant="outline">
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Checkout;
