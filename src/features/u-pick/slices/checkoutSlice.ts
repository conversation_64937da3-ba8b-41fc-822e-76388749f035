import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { getAuth } from 'firebase/auth';

import { updateTicketsStatus } from '@/src/features/u-pick/slices/guestsSlice';
import { apiPost } from '@/src/lib/apiClient'; // Import apiClient
import { ERROR_MESSAGES, API_ENDPOINTS } from '@/src/lib/constants';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { RootState } from '@/src/store';
import { Tickets } from '@/src/types/Tickets';

interface CheckoutState {
  clamshells: Tickets[];
  loading: boolean;
  error: string | null;
  orderId: string;
}

const initialState: CheckoutState = {
  clamshells: [],
  loading: false,
  error: null,
  orderId: '',
};

export const redeemClamshells = createAsyncThunk(
  'checkout/redeemClamshells',
  async (ticketIds: string[], { rejectWithValue, dispatch, getState }) => {
    try {
      if (ticketIds.length === 0) {
        return rejectWithValue(ERROR_MESSAGES.NO_CLAMSHELLS_SELECTED);
      }

      // Get the current user's auth token
      const auth = getAuth();
      const currentUser = auth.currentUser;

      if (!currentUser) {
        return rejectWithValue('You must be logged in to redeem clamshells');
      }

      const token = await currentUser.getIdToken();

      const response = await apiPost(
        API_ENDPOINTS.REDEEM_CLAMSHELLS,
        { ticketIds },
        {
          headers: { Authorization: `Bearer ${token}` },
          // Enable replay protection
          useLimitedUseToken: true,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.error || ERROR_MESSAGES.FAILED_TO_REDEEM_CLAMSHELL);
      }

      const data = await response.json();

      // Also update the activeTickets in the guests slice
      const state = getState() as RootState;
      const { orderId } = state.checkout;

      // Import and dispatch the updateTicketsStatus action from guestsSlice
      dispatch(updateTicketsStatus({ ticketIds, orderId }));

      return data.ticketIds;
    } catch (error) {
      console.warn('Error redeeming clamshells:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_REDEEM_CLAMSHELL);
    }
  }
);

const checkoutSlice = createSlice({
  name: 'checkout',
  initialState,
  reducers: {
    setClamshells: (state, action) => {
      state.clamshells = action.payload;
    },
    setOrderId: (state, action) => {
      state.orderId = action.payload;
    },
    resetCheckout: () => initialState,
  },
  extraReducers: builder => {
    builder
      .addCase(redeemClamshells.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(redeemClamshells.fulfilled, (state, action) => {
        state.loading = false;
        // Update redeemed clamshells in state
        state.clamshells = state.clamshells.map(clamshell =>
          action.payload.includes(clamshell.id as string)
            ? ({
                ...clamshell,
                check_in: [{ status: true, timestamp: getPacificTimeNow() }],
                status: 'checked_in',
                checked_in_at: getPacificTimeNow(),
              } as unknown as Tickets)
            : clamshell
        );
      })
      .addCase(redeemClamshells.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setClamshells, setOrderId, resetCheckout } = checkoutSlice.actions;
export default checkoutSlice.reducer;
