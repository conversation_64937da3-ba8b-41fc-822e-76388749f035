import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { apiGet } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { getFirebaseToken } from '@/src/lib/utils';
import { Waivers } from '@/src/types/Waivers';

interface UserWaiversState {
  userWaivers: Waivers[];
  childWaivers: Waivers[];
  loading: boolean;
  error: string | null;
}

const initialState: UserWaiversState = {
  userWaivers: [],
  childWaivers: [],
  loading: false,
  error: null,
};

/**
 * Async thunk for fetching all waivers for a user
 */
export const fetchUserWaivers = createAsyncThunk(
  'userWaivers/fetchUserWaivers',
  async (email: string, { rejectWithValue }) => {
    try {
      const token = await getFirebaseToken();
      if (!token) throw new Error('Authentication token not found.');

      const response = await apiGet(
        `${API_ENDPOINTS.GET_USER_WAIVERS}?email=${encodeURIComponent(email)}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      ); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.GENERIC_ERROR);
      }

      const data = await response.json();
      return data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.GENERIC_ERROR);
    }
  }
);

/**
 * Async thunk for fetching waiver PDF URL
 */
export const fetchWaiverPdfUrl = createAsyncThunk(
  'userWaivers/fetchWaiverPdfUrl',
  async (pdfPath: string, { rejectWithValue }) => {
    try {
      const response = await fetch(
        `${API_ENDPOINTS.GET_WAIVER_PDF_URL}?pdfPath=${encodeURIComponent(pdfPath)}`
      );

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.GENERIC_ERROR);
      }

      return await response.json();
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.GENERIC_ERROR);
    }
  }
);

const userWaiversSlice = createSlice({
  name: 'userWaivers',
  initialState,
  reducers: {
    clearWaivers: state => {
      state.userWaivers = [];
      state.childWaivers = [];
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchUserWaivers.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserWaivers.fulfilled, (state, action) => {
        state.loading = false;
        state.userWaivers = action.payload.userWaivers || [];
        state.childWaivers = action.payload.childWaivers || [];
      })
      .addCase(fetchUserWaivers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearWaivers } = userWaiversSlice.actions;
export default userWaiversSlice.reducer;
