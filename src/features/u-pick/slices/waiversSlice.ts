import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { apiGet, apiPost } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { Orders } from '@/src/types/Orders';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';
import { Waivers } from '@/src/types/Waivers';

/**
 * State interface for the waivers slice
 * Manages loading, error, and success states for waiver submission
 */
interface WaiversState {
  loading: boolean;
  error: string | null;
  order: (Orders & { organizer: Users }) | null;
  tickets: Array<
    Tickets & {
      user: Users | null;
    }
  >;
}

// Initial state for the waivers slice
const initialState: WaiversState = {
  loading: false,
  error: null,
  order: null,
  tickets: [],
};

/**
 * Async thunk for fetching tickets by order ID
 */
export const fetchTicketsByOrder = createAsyncThunk(
  'waivers/fetchTicketsByOrder',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await apiGet(`${API_ENDPOINTS.GET_TICKETS_BY_ORDER}?orderId=${orderId}`); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_TICKETS);
      }

      const data = await response.json();
      return data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_TICKETS);
    }
  }
);

/**
 * Async thunk for submitting waiver data to the backend
 */
export const submitWaiver = createAsyncThunk(
  'waivers/submitWaiver',
  async (waiver: Waivers, { rejectWithValue }) => {
    try {
      // Collect device info for audit purposes
      const deviceInfo = {
        userAgent: window.navigator.userAgent,
        platform: window.navigator.platform || 'Unknown',
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        browser: window.navigator.userAgent.split(' ').pop() || 'Unknown',
      };

      // Get IP address using a public API
      let ipAddress = 'IP_NOT_FOUND';
      try {
        const ipResponse = await fetch('https://api.ipify.org?format=json');
        if (ipResponse.ok) {
          const ipData = await ipResponse.json();
          ipAddress = ipData.ip;
        } else {
          console.warn('Could not fetch IP address.');
        }
      } catch (ipError) {
        console.warn('Error fetching IP address:', ipError);
      }

      // Prepare waiver data with device info and IP
      const waiverData = {
        ...waiver,
        ip_address: ipAddress, // Use fetched IP or default
        device_info: JSON.stringify(deviceInfo),
      };

      // Send to API using apiPost
      const response = await apiPost(API_ENDPOINTS.CREATE_WAIVER, waiverData, {
        // Enable replay protection
        useLimitedUseToken: true,
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || ERROR_MESSAGES.FAILED_TO_SUBMIT_WAIVER);
      }

      return data.waiver;
    } catch (error) {
      console.warn('Error submitting waiver:', error);
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_SUBMIT_WAIVER);
    }
  }
);

const waiversSlice = createSlice({
  name: 'waivers',
  initialState,
  reducers: {
    resetWaiverState: state => {
      state.loading = false;
      state.error = null;
      state.order = null;
      state.tickets = [];
    },
  },
  extraReducers: builder => {
    builder
      // Handle fetchTicketsByOrder
      .addCase(fetchTicketsByOrder.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTicketsByOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.tickets = action.payload.tickets;
        state.order = action.payload.order;
      })
      .addCase(fetchTicketsByOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Handle submitWaiver
      .addCase(submitWaiver.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(submitWaiver.fulfilled, state => {
        state.loading = false;
      })
      .addCase(submitWaiver.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { resetWaiverState } = waiversSlice.actions;
export default waiversSlice.reducer;
