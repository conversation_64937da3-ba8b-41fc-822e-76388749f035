import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { apiGet } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';

interface WaiverContentState {
  content: string;
  version: string;
  loading: boolean;
  error: string | null;
}

const initialState: WaiverContentState = {
  content: '',
  version: '',
  loading: false,
  error: null,
};

export const fetchWaiverContent = createAsyncThunk(
  'waiverContent/fetchContent',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiGet(API_ENDPOINTS.GET_WAIVER_CONTENT); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_WAIVER);
      }

      return await response.json();
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_WAIVER);
    }
  }
);

const waiverContentSlice = createSlice({
  name: 'waiverContent',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchWaiverContent.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWaiverContent.fulfilled, (state, action) => {
        state.loading = false;
        state.content = action.payload.content;
        state.version = action.payload.version;
      })
      .addCase(fetchWaiverContent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default waiverContentSlice.reducer;
