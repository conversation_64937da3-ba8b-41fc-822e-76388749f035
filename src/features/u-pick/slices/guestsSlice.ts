import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { getAuth } from 'firebase/auth';
import { Timestamp } from 'firebase/firestore';
import { CreatePaymentLinkResponse } from 'square';

import { apiGet, apiPost } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES, TICKET_STATUS } from '@/src/lib/constants';
import { getFirebaseToken } from '@/src/lib/utils';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Orders } from '@/src/types/Orders';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

/**
 * Type for grouped tickets by order ID
 */
export interface GroupedTickets {
  [orderId: string]: Tickets[];
}

/**
 * State interface for the U-Pick feature
 * Manages event details, schedule, booking information, and guest data
 *
 * @interface UPickState
 * @property {Events | null} event - Current event information
 * @property {EventSchedules | null} schedule - Selected event schedule
 * @property {boolean} loading - Loading state for async operations
 * @property {string | null} error - Error message if any operation fails
 * @property {Users | null} booker - Primary booking user's information
 * @property {Users[]} guests - Array of additional guests' information
 */
interface UPickState {
  event: Events | null;
  schedule: EventSchedules | null;
  loading: boolean;
  error: string | null;
  booker: Users | null;
  guests: Users[];
  paymentProcessLink: CreatePaymentLinkResponse | null;
  order:
    | (Orders & {
        event: Events | null;
        schedule: EventSchedules | null;
        waiver_count: number;
        booker: Users | undefined;
        pending_child_waivers?: { first_name: string; last_name: string }[];
      })
    | null;
  tickets: Tickets[] | [];
  selectedTicket: Tickets | null;
  bookerTickets: Tickets[] | [];
  activeTickets: GroupedTickets[];
  completedTickets: Tickets[];
  availableTicketStock: number | null;
  orders: Orders[];
  isUserHasValidWaiver: boolean;
}

/**
 * Initial state for the U-Pick slice
 * All fields are initialized to their default empty values
 */
const initialState: UPickState = {
  event: null,
  schedule: null,
  loading: false,
  error: null,
  booker: null,
  guests: [],
  paymentProcessLink: null,
  order: null,
  tickets: [],
  selectedTicket: null,
  bookerTickets: [],
  activeTickets: [],
  completedTickets: [],
  availableTicketStock: null,
  orders: [],
  isUserHasValidWaiver: false,
};

/**
 * Async thunk to fetch event and schedule details
 * Makes an API call to retrieve event and schedule information based on provided IDs
 *
 * @async
 * @param {Object} params - Parameters for the API call
 * @param {string} params.eventId - ID of the event to fetch
 * @param {string} params.scheduleId - ID of the schedule to fetch
 * @returns {Promise<{event: Events, schedule: EventSchedules}>} Event and schedule data
 * @throws Will reject with an error message if the fetch fails
 */
export const fetchEventAndSchedule = createAsyncThunk(
  'uPick/fetchEventAndSchedule',
  async ({ eventId, scheduleId }: { eventId: string; scheduleId: string }, { rejectWithValue }) => {
    try {
      const response = await apiGet(
        `${API_ENDPOINTS.EVENT_SCHEDULE}?eventId=${eventId}&scheduleId=${scheduleId}`
      ); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_EVENTS);
      }

      const data = await response.json();
      return data.data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_EVENTS);
    }
  }
);

export const fetchUserTickets = createAsyncThunk(
  'tickets/fetchUserTickets',
  async ({ userId }: { userId?: string }, { rejectWithValue }) => {
    // console.info('inside the fetchUserTickets'); // Replaced console.log
    try {
      const token = await getFirebaseToken();
      if (!token) throw new Error('Authentication token not found.');

      const response = await apiGet(`${API_ENDPOINTS.TICKETS}?userId=${userId}`, {
        headers: { Authorization: `Bearer ${token}` },
      }); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_TICKETS);
      }

      const data = await response.json();

      return data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_TICKETS);
    }
  }
);

export const checkWaiver = createAsyncThunk(
  'guests/checkWaiver',
  async (
    { email, firstName, lastName }: { email: string; firstName: string; lastName: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiGet(
        `${API_ENDPOINTS.CHECK_WAIVER}?email=${email}&firstName=${firstName}&lastName=${lastName}`
      ); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || 'Failed to check waiver status');
      }

      const data = await response.json();
      return data.hasValidWaiver;
    } catch (_error) {
      return rejectWithValue('Failed to check waiver status');
    }
  }
);

export const fetchTicketById = createAsyncThunk(
  'tickets/fetchTicketById',
  async (ticketId: string, { rejectWithValue, getState }) => {
    try {
      // Get current user ID from auth state
      const state = getState() as { authentication: { user: { uid: string } } };
      const currentUserId = state.authentication.user?.uid;

      const response = await apiGet(
        `${API_ENDPOINTS.GET_TICKET_BY_ID}?ticketId=${ticketId}${currentUserId ? `&currentUserId=${currentUserId}` : ''}`
      ); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_TICKET);
      }

      const data = await response.json();
      return data.ticket;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_TICKET);
    }
  }
);

interface PaymentLinkParams {
  event: Events | null;
  schedule: EventSchedules | null;
  guests: Users[];
  booker: Users | null;
}

export const fetchPaymentProcessLink = createAsyncThunk(
  'schedule/fetchPaymentProcessLink',
  async ({ event, schedule, guests, booker }: PaymentLinkParams, { rejectWithValue }) => {
    try {
      const response = await apiPost(
        API_ENDPOINTS.PROCESS_PAYMENT_LINK,
        {
          event,
          schedule,
          guests,
          booker,
        },
        {
          // Enable replay protection
          useLimitedUseToken: true,
        }
      ); // Use apiPost
      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_SCHEDULES);
      }
      const data = await response.json();

      return data as CreatePaymentLinkResponse;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_SCHEDULES);
    }
  }
);

export const updateOrderStatus = createAsyncThunk(
  'schedule/updateOrderStatus',
  async (
    {
      orderId,
      transactionId,
      squareOrderId,
    }: { orderId: string; transactionId: string; squareOrderId: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiPost(
        API_ENDPOINTS.UPDATE_ORDER_STATUS,
        {
          order_id: orderId,
          square_transaction_id: transactionId,
          square_order_id: squareOrderId,
        },
        {
          // Enable replay protection
          useLimitedUseToken: true,
        }
      ); // Use apiPost
      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_UPDATE_ORDER_STATUS);
      }
      const data = await response.json();
      return data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_UPDATE_ORDER_STATUS);
    }
  }
);

export const fetchOrderDetails = createAsyncThunk(
  'schedule/fetchOrderDetails',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await apiPost(
        API_ENDPOINTS.GET_ORDER_DETAILS,
        {
          orderId,
        },
        {
          // Enable replay protection
          useLimitedUseToken: true,
        }
      ); // Use apiPost
      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_ORDER_DETAILS);
      }
      const data = await response.json();
      return data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_ORDER_DETAILS);
    }
  }
);

export const verifyCheckIn = createAsyncThunk(
  'tickets/verifyCheckIn',
  async (
    {
      ticketId,
      passcode,
      selectedGuestIds,
    }: {
      ticketId: string;
      passcode: string;
      selectedGuestIds?: string[];
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiPost(
        API_ENDPOINTS.VERIFY_CHECK_IN,
        {
          ticketId,
          passcode,
          selectedGuestIds,
        },
        {
          // Enable replay protection
          useLimitedUseToken: true,
        }
      ); // Use apiPost

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || 'Failed to verify check-in');
      }

      const data = await response.json();
      return data;
    } catch (_error) {
      return rejectWithValue('Failed to verify check-in');
    }
  }
);

export const fetchEventById = createAsyncThunk(
  'guests/fetchEventById',
  async (eventId: string, { rejectWithValue }) => {
    try {
      const response = await apiGet(API_ENDPOINTS.EVENT(eventId)); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_EVENTS);
      }

      const data = await response.json();
      return data.data;
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_EVENTS);
    }
  }
);

export const getTicketStocks = createAsyncThunk(
  'tickets/getTicketStocks',
  async (
    { event_id, schedule_id }: { event_id: string; schedule_id: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiGet(
        `${API_ENDPOINTS.GET_TICKET_STOCK}?event_id=${event_id}&event_schedule_id=${schedule_id}`
      ); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch ticket stocks');
      }

      const data = await response.json();
      return data.available_tickets;
    } catch (_error) {
      return rejectWithValue('Failed to fetch ticket stocks');
    }
  }
);

export const fetchUserOrders = createAsyncThunk(
  'guests/fetchUserOrders',
  async ({ userId, firebaseId }: { userId?: string; firebaseId?: string }, { rejectWithValue }) => {
    try {
      // Get the current user ID
      const auth = getAuth();
      const currentUser = auth.currentUser;

      if (!currentUser) {
        return rejectWithValue('User not authenticated');
      }

      // Use provided userId or fall back to current user's ID
      // This ensures we're only requesting data the user is authorized to access
      const requestUserId = userId || currentUser.uid;

      // Get the Firebase token
      const token = await getFirebaseToken();

      if (!token) {
        return rejectWithValue('User not authenticated');
      }

      // Make the API request
      const response = await apiGet(
        `${API_ENDPOINTS.GET_USER_ORDERS}?userId=${requestUserId}&firebaseId=${firebaseId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      ); // Use apiGet

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || 'Failed to fetch user orders');
      }

      const data = await response.json();
      return data.orders;
    } catch (_error) {
      return rejectWithValue('Failed to fetch user orders');
    }
  }
);

/**
 * Interface for the payload when saving guest data
 * @interface SaveGuestDataPayload
 */
interface SaveGuestDataPayload {
  booker: Users; // Primary booking user
  guests: Users[]; // Additional guests
}

/**
 * Redux slice for managing guest-related state
 * Handles guest data persistence, clearing, and async event/schedule fetching
 */
const guestsSlice = createSlice({
  name: 'guests',
  initialState,
  reducers: {
    /**
     * Saves booker and guest information to the state
     * @param {UPickState} state - Current state
     * @param {PayloadAction<SaveGuestDataPayload>} action - Action containing booker and guests data
     */
    saveGuestData: (state, action: PayloadAction<SaveGuestDataPayload>) => {
      state.booker = action.payload.booker;
      state.guests = action.payload.guests;
    },

    /**
     * Clears all guest-related data from the state
     * @param {UPickState} state - Current state
     */
    clearGuestData: state => {
      state.booker = null;
      state.guests = [];
    },

    updateTicketsStatus: (
      state,
      action: PayloadAction<{ ticketIds: string[]; orderId: string }>
    ) => {
      const { ticketIds, orderId } = action.payload;

      // Update the check_in status for the specified tickets in activeTickets
      state.activeTickets = state.activeTickets.map(ticketGroup => {
        if (ticketGroup[orderId]) {
          const updatedTickets = ticketGroup[orderId].map(ticket => {
            if (ticketIds.includes(ticket.id as string)) {
              return {
                ...ticket,
                check_in: [{ status: true, timestamp: new Date() }],
                checked_in_at: Timestamp.now(),
              };
            }
            return ticket;
          });

          return { ...ticketGroup, [orderId]: updatedTickets };
        }
        return ticketGroup;
      });
    },
  },
  extraReducers: builder => {
    builder
      // Handle pending state for fetch operation
      .addCase(fetchEventAndSchedule.pending, state => {
        state.loading = true;
        state.error = null;
      })
      // Handle successful fetch operation
      .addCase(fetchEventAndSchedule.fulfilled, (state, action) => {
        state.loading = false;
        state.event = action.payload.event;
        state.schedule = action.payload.schedule;
        state.error = null;
      })
      // Handle failed fetch operation
      .addCase(fetchEventAndSchedule.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchPaymentProcessLink.fulfilled, (state, action) => {
        state.paymentProcessLink = action.payload;
        state.loading = false;
      })
      .addCase(fetchPaymentProcessLink.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchPaymentProcessLink.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrderDetails.fulfilled, (state, action) => {
        state.order = action.payload.order;
        state.tickets = action.payload.tickets;
        state.loading = false;
        state.error = null;
      })
      .addCase(fetchOrderDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchOrderDetails.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        state.order = action.payload.order;
        state.tickets = action.payload.tickets;
        state.loading = false;
        state.error = null;
      })
      .addCase(updateOrderStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateOrderStatus.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserTickets.pending, state => {
        state.loading = state.activeTickets.length > 0 ? false : true;
        state.error = null;
      })
      .addCase(fetchUserTickets.fulfilled, (state, action) => {
        state.loading = false;
        // Fix: Use action.payload.tickets instead of action.payload
        state.tickets = Array.isArray(action.payload.tickets) ? action.payload.tickets : [];
        const ticketsData = Array.isArray(action.payload.tickets)
          ? action.payload.tickets.filter(
              (ticket: Tickets) =>
                ticket.status === TICKET_STATUS.RESERVED ||
                ticket.status === TICKET_STATUS.CHECKED_IN ||
                ticket.status === TICKET_STATUS.VALID
            )
          : [];

        const groupedTickets: { [key: string]: Tickets[] } = {};

        // Group tickets by orderId
        ticketsData.forEach((ticket: Tickets) => {
          const orderId = ticket?.order_id as unknown as string;

          if (!orderId) return;

          if (!groupedTickets[orderId]) {
            groupedTickets[orderId] = [];
          }

          groupedTickets[orderId].push(ticket);
        });

        // Convert to array format [{orderId: [ticket1, ticket2]}, {orderId: [ticket3, ticket4]}]
        const formattedGroupedTickets = Object.entries(groupedTickets).map(
          ([orderId, tickets]) => ({
            [orderId]: tickets,
          })
        );

        state.activeTickets = formattedGroupedTickets;
        state.completedTickets = Array.isArray(action.payload.tickets)
          ? action.payload.tickets.filter(
              (ticket: Tickets) => ticket.status === TICKET_STATUS.CHECKED_OUT
            )
          : [];
      })
      .addCase(fetchUserTickets.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchTicketById.pending, state => {
        state.loading = true;
        state.error = null;
        state.selectedTicket = null;
      })
      .addCase(fetchTicketById.fulfilled, (state, action) => {
        state.selectedTicket = action.payload;
        state.loading = false;
      })
      .addCase(fetchTicketById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchEventById.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEventById.fulfilled, (state, action) => {
        state.loading = false;
        state.event = action.payload;
      })
      .addCase(fetchEventById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getTicketStocks.fulfilled, (state, action) => {
        state.availableTicketStock = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(getTicketStocks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getTicketStocks.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserOrders.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload;
        state.error = null;
      })
      .addCase(fetchUserOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(checkWaiver.fulfilled, (state, action) => {
        state.isUserHasValidWaiver = action.payload;
        state.loading = false;
      })
      .addCase(checkWaiver.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(checkWaiver.pending, state => {
        state.loading = true;
        state.error = null;
      });
  },
});

// Export actions and reducer
export const { saveGuestData, clearGuestData, updateTicketsStatus } = guestsSlice.actions;
export default guestsSlice.reducer;
