'use client';
import React from 'react';

import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader } from '@/src/components/ui/card';
import { EventSchedules } from '@/src/types/EventSchedules';
import { Users } from '@/src/types/Users';

/**
 * Props interface for PaymentDetails component
 * @interface EventCardProps
 * @property {EventSchedules} schedule - Event schedule containing pricing information
 * @property {Users[]} guests - Array of guest information including age groups
 */
interface EventCardProps {
  schedule: EventSchedules;
  guests: Users[];
  showButton: boolean;
  handlePayAndBook?: () => void;
}

const PaymentDetailsCard: React.FC<EventCardProps> = ({
  schedule,
  guests,
  handlePayAndBook,
  showButton,
}) => {
  /**
   * Start Initials
   */
  // Calculate guest counts and pricing
  const adultCount = guests.filter(guest => guest.age_group === 'adult').length;
  const childCount = guests.filter(guest => guest.age_group === 'child').length;
  const totalGuests = adultCount + childCount;

  // Calculate total price using nullish coalescing to handle undefined prices
  const totalPrice =
    adultCount * (schedule.price_per_adult ?? 0) + childCount * (schedule.price_per_child ?? 0);
  /**
   * End Initials
   */

  return (
    <Card className="p-3 shadow-md max-w-2xl mt-3  mx-auto">
      {/* Card Title */}
      <h1 className="font-semibold mb-2">Payment Details</h1>

      {/* Table Header */}
      <CardHeader className="border-b pb-3">
        <div className="flex justify-between items-center text-gray-700 font-medium text-sm">
          <span className="w-1/2">Item</span>
          <div className="flex w-1/2 justify-between">
            <span className="w-1/3 text-center">Qty</span>
            <span className="w-1/3 text-center">Unit Price</span>
            <span className="w-1/3 text-center">Total</span>
          </div>
        </div>
      </CardHeader>

      {/* Adult Tickets Row */}
      <CardContent className="py-3">
        <div className="flex justify-between items-center">
          <span className="w-1/2">{adultCount > 1 ? 'Adults Tickets' : 'Adult Ticket'}</span>
          <div className="flex w-1/2 justify-between text-gray-700 text-sm">
            <span className="w-1/3 text-center">{`${adultCount} X`}</span>
            <span className="w-1/3 text-center">${schedule?.price_per_adult}</span>
            <span className="w-1/3 text-center">
              ${adultCount * (schedule?.price_per_adult ?? 0)}
            </span>
          </div>
        </div>
      </CardContent>

      {/* Child Tickets Row */}
      <CardContent className="py-3">
        <div className="flex justify-between items-center">
          <span className="w-1/2">{childCount > 1 ? 'Children Tickets' : 'Child Ticket'}</span>
          <div className="flex w-1/2 justify-between text-gray-700 text-sm">
            <span className="w-1/3 text-center">{`${childCount} X`}</span>
            <span className="w-1/3 text-center">${schedule?.price_per_child}</span>
            <span className="w-1/3 text-center">
              ${childCount * (schedule?.price_per_child ?? 0)}
            </span>
          </div>
        </div>
      </CardContent>

      {/* Total Amount Row */}
      <CardContent className="py-3 border-t">
        <div className="flex justify-between items-center">
          <span className="w-1/2 font-semibold">Total</span>
          <div className="flex w-1/2 justify-between text-gray-900 font-semibold text-sm">
            <span className="w-1/3 text-center">{totalGuests}</span>
            <span className="w-1/3 text-center"></span>
            <span className="w-1/3 text-center">${totalPrice.toFixed(2)}</span>
          </div>
        </div>
      </CardContent>

      {/* Payment Action Button */}
      {showButton && (
        <div className="flex justify-end mt-4">
          <Button
            onClick={handlePayAndBook}
            className="w-full bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white px-6 py-2"
          >
            Pay & Book Ticket
          </Button>
        </div>
      )}
    </Card>
  );
};

export default PaymentDetailsCard;
