import { CalendarDays, Clock, CheckCircle, MapPin, TicketCheck, LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

import { Button } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';
import { BUTTON_TEXTS, ROUTES, TICKET_STATUS, TICKET_STATUS_DISPLAY } from '@/src/lib/constants';
import { formatDate, formatTimeRange, generateWaiverToken } from '@/src/lib/utils';
import { Tickets } from '@/src/types/Tickets';
import { Users } from '@/src/types/Users';

interface TicketCardProps {
  ticket: Tickets;
  isCompleted: boolean;
  onCheckIn: (ticketId: string) => void;
  isUserHasValidWaiver: boolean;
}

const TicketCard: React.FC<TicketCardProps> = ({
  ticket,
  isCompleted,
  onCheckIn,
  isUserHasValidWaiver,
}) => {
  /**
   * Start Initials
   */
  const router = useRouter();
  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  const handleCheckInClick = () => {
    onCheckIn(ticket.id ?? '');
  };

  const handleCheckOutClick = () => {
    router.push(`${process.env.NEXT_PUBLIC_CHECKOUT_STORE_URL}?mode=check-out`);
  };

  const navigateToSignWaiver = () => {
    if (ticket.user) {
      const token = generateWaiverToken({
        first_name: ticket.user.first_name,
        last_name: ticket.user.last_name,
        email: ticket.user.email || '',
      } as Users);

      router.push(`${ROUTES.SIGN_WAIVER}?t=${token}&nextUrl=${ROUTES.RESERVATIONS}`);
    } else {
      router.push(ROUTES.SIGN_WAIVER);
    }
  };

  const renderActionButton = () => {
    if (isCompleted) return null;

    if (ticket.status === TICKET_STATUS.CHECKED_IN) {
      return (
        <Button
          onClick={handleCheckOutClick}
          className="flex items-center gap-2 cursor-pointer bg-mulberry text-white hover:bg-mulberryHover active:bg-mulberryActive transition-colors"
        >
          <LogOut className="h-4 w-4" />
          {BUTTON_TEXTS.CHECK_OUT}
        </Button>
      );
    }

    if (isUserHasValidWaiver) {
      return (
        <Button
          onClick={handleCheckInClick}
          className="flex items-center gap-2 cursor-pointer bg-mulberry text-white hover:bg-mulberryHover active:bg-mulberryActive transition-colors"
        >
          <CheckCircle className="h-4 w-4" />
          {BUTTON_TEXTS.CHECK_IN}
        </Button>
      );
    }

    return (
      <Button
        onClick={navigateToSignWaiver}
        className="flex items-center gap-2 cursor-pointer bg-mulberry text-white hover:bg-mulberryHover active:bg-mulberryActive transition-colors"
      >
        {BUTTON_TEXTS.SIGN_WAIVER}
      </Button>
    );
  };

  const handleLocationClick = () => {
    if (ticket?.event?.event_location?.latitude && ticket?.event?.event_location?.longitude) {
      // Google Maps URL with destination coordinates
      const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${ticket.event.event_location.latitude},${ticket.event.event_location.longitude}`;
      window.open(mapsUrl, '_blank');
    }
  };
  /**
   * End Custom Methods
   */

  return (
    <Card
      className={`mb-4 hover:shadow-lg transition-all duration-300 ${
        isCompleted ? 'bg-gray-50' : 'bg-white'
      }`}
    >
      <CardContent className="pt-6">
        <div className="flex flex-col space-y-4">
          <div className="flex justify-between items-start">
            <div className="space-y-1">
              <h3 className="text-lg font-semibold tracking-tight">#{ticket.ticket_id}</h3>
              <span className="text-sm text-gray-700">Event: {ticket.event_details?.name}</span>
              <h3 className="text-sm text-gray-700">Ticket Type: {ticket.ticket_type}</h3>
            </div>

            <div className="flex items-center gap-2">
              <TicketCheck className="h-4 w-4 text-gray-500" />
              <span
                className={`text-sm font-medium ${
                  ticket.check_in ? 'text-green-600' : 'text-blue-600'
                }`}
              >
                {ticket.check_in
                  ? 'Checked In'
                  : (TICKET_STATUS_DISPLAY[ticket.status ?? 'valid'] ?? 'N/A')}
              </span>
            </div>
          </div>

          <div className=" flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <CalendarDays className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {formatDate(ticket.event_details?.start_date.date) ??
                  ticket.schedule?.event_date ??
                  'N/A'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {formatTimeRange(
                  ticket.event_details?.start_date.time || '',
                  ticket.event_details?.end_date.time || ''
                )}
              </span>
            </div>

            <div
              className="flex items-center gap-2 hover:text-mulberry cursor-pointer"
              onClick={handleLocationClick}
            >
              <MapPin className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {ticket.event_details && ticket.event_details.venue.name},{' '}
                {ticket.event_details && ticket.event_details.venue.postal_code}
              </span>
            </div>
          </div>

          {!isCompleted && (
            <div className="flex justify-end items-center pt-4 border-t">
              {renderActionButton()}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TicketCard;
