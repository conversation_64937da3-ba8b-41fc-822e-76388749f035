import { parsePhoneNumberWithError } from 'libphonenumber-js';
import { BabyIcon, MailIcon, PhoneIcon, UserIcon } from 'lucide-react';
import React, { JSX } from 'react';

import { Card } from '@/src/components/ui/card';
import { useAppSelector, RootState } from '@/src/store';

const formatPhoneNumberWithLib = (phone: string | null | undefined): string => {
  if (!phone) return '';
  try {
    const phoneNumber = parsePhoneNumberWithError(phone);
    return phoneNumber ? phoneNumber.formatNational() : phone;
  } catch {
    return phone;
  }
};

const TicketDetailsCard = (): JSX.Element => {
  /**
   * Start Initials
   */
  const { guests } = useAppSelector((state: RootState) => state.guests);

  const totalGuests = guests.length;

  // Get organizer (first guest) and remaining guests when there are multiple guests
  const organizer = totalGuests > 1 ? guests[0] : null;
  const remainingGuests = totalGuests > 1 ? guests.slice(1) : guests;
  const remainingAdultGuests = remainingGuests.filter(guest => guest.age_group === 'adult');
  const remainingChildGuests = remainingGuests.filter(guest => guest.age_group === 'child');
  /**
   * End Initials
   */

  return (
    <Card className="p-3 shadow-md max-w-2xl mt-3  mx-auto">
      {/* Organizer Section - Only show if more than 1 guest */}
      {totalGuests > 1 && organizer && (
        <div>
          <div className="flex items-center gap-2 mb-3">
            <span className="font-medium">Ticket 1 (Event Organizer)</span>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-4 border-b border-gray-100 pb-3">
            <div className="flex items-center gap-2 w-full">
              <UserIcon className="text-gray-500" size={18} />
              <p className="capitalize text-gray-700">
                {organizer.first_name} {organizer.last_name}
              </p>
            </div>
            <div className="flex items-center gap-2 w-full">
              <MailIcon className="text-gray-500" size={18} />
              <p className="text-gray-700">{organizer.email}</p>
            </div>
            {organizer.phone && (
              <div className="flex items-center gap-2 w-full">
                <PhoneIcon className="text-gray-500" size={18} />
                <p className="text-gray-700">{formatPhoneNumberWithLib(organizer.phone)}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Adult Guests Section */}
      <div>
        {remainingAdultGuests.map((guest, index) => (
          <div key={index} className="border-b border-gray-100 pb-3 mb-3">
            <div className="font-medium mb-2">
              Ticket {totalGuests > 1 ? index + 2 : index + 1} (Adult)
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <div className="flex items-center gap-2 w-full sm:w-1/2">
                <UserIcon className="text-gray-500" size={18} />
                <p className="capitalize text-gray-700">
                  {guest.first_name} {guest.last_name}
                </p>
              </div>
              <div className="flex items-center gap-2 w-full sm:w-1/2">
                <MailIcon className="text-gray-500" size={18} />
                <p className="text-gray-700">{guest.email}</p>
              </div>
              {guest?.phone && (
                <div className="flex items-center gap-2 w-full sm:w-1/2">
                  <PhoneIcon className="text-gray-500" size={18} />
                  <p className="text-gray-700">{formatPhoneNumberWithLib(guest?.phone)}</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Child Guests Section - Conditionally Rendered */}
      {remainingChildGuests.length > 0 && (
        <div>
          {/* <div className="flex items-center gap-2 mb-3">
            <Users size={20} />
            <span className="font-medium">Children ({remainingChildGuests.length})</span>
          </div> */}
          {remainingChildGuests.map((guest, index) => (
            <div key={index} className="border-b border-gray-100 pb-3 mb-3">
              <div className="font-medium mb-2">
                Ticket {remainingAdultGuests.length + (totalGuests > 1 ? index + 2 : index + 1)}{' '}
                (Child)
              </div>
              <div className="flex items-center gap-2">
                <BabyIcon className="text-gray-500" size={18} />
                <p className="capitalize text-gray-700">
                  {guest.first_name} {guest.last_name}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
};

export default TicketDetailsCard;
