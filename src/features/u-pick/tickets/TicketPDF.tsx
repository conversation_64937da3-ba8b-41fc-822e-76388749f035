import { Document, Page, StyleSheet, Text, View, Image, Svg, Path } from '@react-pdf/renderer';
import QRCode from 'qrcode';
import React, { useState, useEffect } from 'react';

import { formatDate, formatTime } from '@/src/lib/utils';
import { Orders } from '@/src/types/Orders';
import { Tickets } from '@/src/types/Tickets';

interface TicketPDFProps {
  tickets: Tickets[];
  order: Orders;
}

const MulberryTicketPDF: React.FC<TicketPDFProps> = ({ tickets, order }) => {
  const [qrCodes, setQrCodes] = useState<{ [key: string]: string }>({});

  const generateQRCode = async (ticketId: string) => {
    try {
      const qrCodeDataUrl = await QRCode.toDataURL(ticketId, {
        width: 80,
        margin: 0,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      });
      return qrCodeDataUrl;
    } catch (err) {
      console.error('Error generating QR code:', err);
      return '';
    }
  };

  useEffect(() => {
    const loadQRCodes = async () => {
      const codes: { [key: string]: string } = {};
      for (const ticket of tickets) {
        if (ticket.ticket_id) {
          codes[ticket.ticket_id] = await generateQRCode(ticket.ticket_id);
        }
      }
      setQrCodes(codes);
    };

    loadQRCodes();
  }, [tickets]);

  if (!tickets.length) return null;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Image src="/Very-Mulberry-Logo.png" style={styles.logo} />
          </View>

          {/* Event Title */}
          <Text style={[styles.eventTitle, { textAlign: 'left', marginTop: 30, fontSize: 18 }]}>
            {order.event?.title}
          </Text>

          {/* Event Info Grid */}
          <View style={styles.eventInfoContainer}>
            <View style={styles.eventInfoGrid}>
              {/* Left Column */}
              <View style={styles.column}>
                {/* Date */}
                <View style={styles.infoRow}>
                  <View style={styles.iconContainer}>
                    <Svg viewBox="0 0 24 24" style={{ width: 24, height: 24 }}>
                      <Path
                        d="M19 4H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2zm0 16H5V8h14v12zM7 2h2v4H7V2zm8 0h2v4h-2V2z"
                        stroke="#4b5563"
                        strokeWidth={1}
                      />
                    </Svg>
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.label}>DATE</Text>
                    <Text style={styles.value}>
                      {order.schedule?.event_date && formatDate(order.schedule.event_date)}
                    </Text>
                  </View>
                </View>

                {/* Location */}
                <View style={styles.infoRow}>
                  <View style={styles.iconContainer}>
                    <Svg viewBox="0 0 24 24" style={{ width: 24, height: 24 }}>
                      <Path
                        d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z M12 7a3 3 0 1 0 0 6 3 3 0 0 0 0-6z"
                        stroke="#4b5563"
                        strokeWidth={1}
                      />
                    </Svg>
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.label}>LOCATION</Text>
                    <Text style={styles.value}>
                      {order.event?.event_location?.name}, {order.event?.event_location?.city},{' '}
                      {order.event?.event_location?.state}
                    </Text>
                  </View>
                </View>
              </View>

              {/* Right Column */}
              <View style={styles.column}>
                {/* Time */}
                <View style={styles.infoRow}>
                  <View style={styles.iconContainer}>
                    <Svg viewBox="0 0 24 24" style={{ width: 24, height: 24 }}>
                      <Path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"
                        stroke="#4b5563"
                        strokeWidth={1}
                      />
                    </Svg>
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.label}>TIMESLOT</Text>
                    <Text style={styles.value}>
                      {order.schedule?.start_time && formatTime(order.schedule.start_time)} -{' '}
                      {order.schedule?.end_time && formatTime(order.schedule.end_time)}
                    </Text>
                  </View>
                </View>

                {/* Guests */}
                <View style={styles.infoRow}>
                  <View style={styles.iconContainer}>
                    <Svg viewBox="0 0 24 24" style={{ width: 24, height: 24 }}>
                      <Path
                        d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2 M9 3a4 4 0 1 0 0 8 4 4 0 0 0 0-8z M23 21v-2a4 4 0 0 0-3-3.87 M16 3.13a4 4 0 0 1 0 7.75"
                        stroke="#4b5563"
                        strokeWidth={1}
                      />
                    </Svg>
                  </View>
                  <View style={styles.infoContent}>
                    <Text style={styles.label}>TOTAL GUESTS</Text>
                    <Text style={styles.value}>{tickets.length} Guests</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/* Guest Tickets */}
          <View style={styles.ticketsContainer}>
            {tickets.map(ticket => (
              <View key={ticket.id} style={styles.ticketCard}>
                <View style={styles.ticketContent}>
                  <View style={styles.ticketInfo}>
                    <View style={styles.ticketInfoRow}>
                      <View style={styles.iconContainer}>
                        <Svg viewBox="0 0 24 24" style={{ width: 24, height: 24 }}>
                          <Path
                            d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2 M12 3a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"
                            stroke="#4b5563"
                            strokeWidth={1}
                          />
                        </Svg>
                      </View>
                      <View>
                        <Text style={styles.label}>Guest Name</Text>
                        <Text style={styles.value}>
                          {ticket.user?.first_name} {ticket.user?.last_name}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.ticketInfoRow}>
                      <View style={styles.iconContainer}>
                        <Svg viewBox="0 0 24 24" style={{ width: 24, height: 24 }}>
                          <Path
                            d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z M13 5v2 M13 17v2 M13 11v2"
                            stroke="#4b5563"
                            strokeWidth={1}
                          />
                        </Svg>
                      </View>
                      <View>
                        <Text style={styles.label}>Ticket ID</Text>
                        <Text style={styles.value}>{ticket.ticket_id}</Text>
                      </View>
                    </View>
                  </View>
                  <View style={styles.qrCodeContainer}>
                    {ticket.ticket_id && qrCodes[ticket.ticket_id] && (
                      <Image src={qrCodes[ticket.ticket_id]} style={styles.qrCode} />
                    )}
                  </View>
                </View>
              </View>
            ))}
          </View>

          {/* Guidelines Section */}
          {order.event?.guidelines && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Important Guidelines</Text>
              <View style={styles.sectionContent}>
                {order.event.guidelines.map((item, index) => (
                  <Text key={index} style={styles.listItem}>
                    • {item}
                  </Text>
                ))}
              </View>
            </View>
          )}

          {/* What to Expect Section */}
          {order.event?.what_to_expect && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>What to Expect</Text>
              <View style={styles.sectionContent}>
                {order.event.what_to_expect.map((item, index) => (
                  <Text key={index} style={styles.listItem}>
                    • {item}
                  </Text>
                ))}
              </View>
            </View>
          )}

          {/* Footer */}
          <View style={styles.footer}>
            {/* Logo Section */}
            <View style={styles.footerLogoContainer}>
              <Image src="/Very-Mulberry-Logo.png" style={styles.footerLogo} />
            </View>

            {/* Contact Info Section */}
            <View style={styles.footerContactContainer}>
              {/* Left Column - Location */}
              <View style={styles.footerColumn}>
                <View style={styles.footerInfoRow}>
                  <Svg viewBox="0 0 24 24" style={styles.footerIcon}>
                    <Path
                      d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z M12 7a3 3 0 1 0 0 6 3 3 0 0 0 0-6z"
                      stroke="#892b76"
                      strokeWidth={1}
                    />
                  </Svg>
                  <Text style={styles.footerText}>501 Hoffman Lane, Brentwood, CA 94513</Text>
                </View>
              </View>

              {/* Vertical Dotted Border */}
              <View style={styles.footerDivider} />

              {/* Right Column - Website & Email */}
              <View style={styles.footerColumn}>
                <View style={styles.footerInfoRow}>
                  <Svg viewBox="0 0 24 24" style={styles.footerIcon}>
                    <Path
                      d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z M2 12h20 M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
                      stroke="#892b76"
                      strokeWidth={1}
                    />
                  </Svg>
                  <Text style={styles.footerText}>www.verymulberry.com</Text>
                </View>
                <View style={styles.footerInfoRow}>
                  <Svg viewBox="0 0 24 24" style={styles.footerIcon}>
                    <Path
                      d="M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6z M22 7l-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"
                      stroke="#892b76"
                      strokeWidth={1}
                    />
                  </Svg>
                  <Text style={styles.footerText}><EMAIL></Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
};

const styles = StyleSheet.create({
  page: {
    padding: 32,
    paddingBottom: 20, // Add extra padding at bottom
    backgroundColor: '#ffffff',
    fontFamily: 'Helvetica',
  },
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fdf2f8',
    padding: 32,
    borderBottom: 1,
    borderBottomColor: '#fbcfe8',
    alignItems: 'center',
  },
  logo: {
    width: 200,
  },
  eventTitle: {
    fontSize: 18, // Changed from 24 to 18 (approximately 1.5rem)
    fontWeight: 'bold',
    color: '#892b76',
    marginBottom: 24,
    textAlign: 'left',
  },
  eventInfoContainer: {
    backgroundColor: 'white',
    padding: 24,
    marginBottom: 32,
    borderRadius: 8,
    border: 1,
    borderColor: '#e5e7eb',
  },
  eventInfoGrid: {
    flexDirection: 'row',
    gap: 32,
  },
  column: {
    flex: 1,
    gap: 24,
  },
  infoRow: {
    flexDirection: 'row',
    gap: 16,
  },
  iconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    fontSize: 20,
  },
  infoContent: {
    flex: 1,
  },
  label: {
    fontSize: 10,
    color: '#6b7280',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    color: '#4b5563',
  },
  ticketsContainer: {
    gap: 24,
  },
  ticketCard: {
    borderRadius: 8,
    border: 1,
    borderColor: '#fbcfe8',
    padding: 24,
  },
  ticketContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ticketInfo: {
    flex: 1,
    gap: 16,
  },
  ticketInfoRow: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
  },
  qrCodeContainer: {
    width: 128,
    height: 128,
    padding: 12,
    borderLeftWidth: 2,
    borderLeftColor: '#fbcfe8',
    borderStyle: 'dotted',
  },
  qrCode: {
    width: '100%',
    height: '100%',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 8,
    border: 1,
    borderColor: '#e5e7eb',
    padding: 24,
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#892b76',
    marginBottom: 16,
  },
  sectionContent: {
    gap: 8,
  },
  listItem: {
    color: '#4b5563',
    fontSize: 14,
  },
  footer: {
    marginTop: 32,
    backgroundColor: '#fdf2f8',
    padding: 32,
    borderTop: 1,
    borderTopColor: '#fbcfe8',
  },
  footerText: {
    fontSize: 12,
    color: '#892b76',
    textAlign: 'left',
  },
  footerLogoContainer: {
    alignItems: 'center',
    marginBottom: 16,
    // Add fixed height to prevent shrinking
    height: 80,
  },
  footerLogo: {
    width: 200,
    height: 80,
    // Add object-fit equivalent to maintain aspect ratio
    objectFit: 'contain',
  },
  footerContactContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    width: '100%',
  },
  footerColumn: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'flex-start', // Align items to the start of the column
  },
  footerInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
    width: '100%', // Ensure the row takes full width
    justifyContent: 'flex-start', // Align content to the start
  },
  footerIcon: {
    width: 20,
    height: 20,
  },
  footerDivider: {
    width: 0,
    height: 50,
    borderLeft: 2,
    borderLeftStyle: 'dotted',
    borderLeftColor: '#fbcfe8',
    marginHorizontal: 32, // Add horizontal margin to create more space
  },
});

export default MulberryTicketPDF;
