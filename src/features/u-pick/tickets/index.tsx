'use client';

import { compareDesc } from 'date-fns';
import { useRouter, usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import ErrorAlert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs';
import withAuth from '@/src/features/auth/hoc/withAuth';
import { checkWaiver, fetchUserTickets } from '@/src/features/u-pick/slices/guestsSlice';
import TicketCard from '@/src/features/u-pick/tickets/TicketCard';
import { ROUTES, TICKET_STATUS } from '@/src/lib/constants';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';

const Ticket = (): JSX.Element => {
  const router = useRouter();
  const pathname = usePathname();
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);

  const {
    user,
    loading: authLoading,
    isAuthenticated,
  } = useAppSelector((state: RootState) => state.authentication);
  const { activeTickets, completedTickets, loading, error, isUserHasValidWaiver } = useAppSelector(
    (state: RootState) => state.guests
  );

  const dispatch = useAppDispatch();

  useEffect(() => {
    // Redirect if not authenticated
    if (!authLoading && !isAuthenticated) {
      router.push(ROUTES.LOGIN + '?redirect=' + ROUTES.RESERVATIONS);
      return;
    }

    // Fetch data in parallel if authenticated
    if (isAuthenticated && user) {
      const promises = [];

      if (user.id) {
        promises.push(
          dispatch(
            fetchUserTickets({
              userId: user.id,
            })
          )
        );
      }

      if (user.email) {
        promises.push(
          dispatch(
            checkWaiver({
              email: user.email,
              firstName: user.first_name,
              lastName: user.last_name,
            })
          )
        );
      }

      // Execute both requests in parallel and mark data as loaded when complete
      Promise.all(promises)
        .then(() => setInitialDataLoaded(true))
        .catch(err => {
          console.error('Error loading ticket data:', err);
          setInitialDataLoaded(true); // Still mark as loaded so UI can show error state
        });
    }
  }, [dispatch, user, isAuthenticated, authLoading, router]);

  /**
   * Start Custom Methods
   */

  const handleCheckIn = (ticketId: string) => {
    router.push(`/reservations/${ticketId}`);
  };

  // Helper function to safely get Date from various timestamp formats
  const getDateFromTimestamp = (timestamp: any): Date => {
    if (!timestamp) return new Date(0);

    // Handle Firestore Timestamp
    if (timestamp?.toDate) {
      return timestamp.toDate();
    }

    // Handle seconds timestamp
    if (timestamp?.seconds) {
      return new Date(timestamp.seconds * 1000);
    }

    // Handle string or number
    return new Date(timestamp);
  };

  // Sort tickets by created_at in descending order (newest first)
  const sortedActiveTickets = [...activeTickets].sort((a, b) =>
    compareDesc(getDateFromTimestamp(a.created_at), getDateFromTimestamp(b.created_at))
  );

  const sortedCompletedTickets = [...completedTickets].sort((a, b) =>
    compareDesc(getDateFromTimestamp(a.created_at), getDateFromTimestamp(b.created_at))
  );

  // Show loading spinner until initial data fetch completes
  if (loading || authLoading || !initialDataLoaded) {
    return <Spinner />;
  }

  if (error) {
    return <ErrorAlert error={error} />;
  }

  return (
    <div
      className={`${
        pathname === '/reservations'
          ? 'w-full lg:w-[60%] ml-0 mr-auto'
          : 'w-full md:max-w-2xl mx-auto p-4'
      }  space-y-8`}
    >
      <Tabs defaultValue="active" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger
            value="active"
            className="data-[state=active]:bg-mulberry data-[state=active]:text-white data-[state=inactive]:text-gray-800"
          >
            Active Tickets ({sortedActiveTickets.length})
          </TabsTrigger>
          <TabsTrigger
            value="completed"
            className="data-[state=active]:bg-mulberry data-[state=active]:text-white data-[state=inactive]:text-gray-800"
          >
            Completed ({sortedCompletedTickets.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="space-y-4">
            {sortedActiveTickets.length > 0 ? (
              sortedActiveTickets.map(ticket => (
                <TicketCard
                  key={Object.keys(ticket)[0]}
                  ticket={ticket}
                  isCompleted={false}
                  onCheckIn={handleCheckIn}
                  isUserHasValidWaiver={isUserHasValidWaiver}
                />
              ))
            ) : (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500 font-medium">No active tickets found.</p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="completed">
          <div className="space-y-4">
            {sortedCompletedTickets.length > 0 ? (
              sortedCompletedTickets.map(ticket => (
                <TicketCard
                  key={Object.keys(ticket)[0]}
                  ticket={ticket}
                  isCompleted={ticket.status === TICKET_STATUS.CHECKED_OUT}
                  onCheckIn={handleCheckIn}
                  isUserHasValidWaiver={isUserHasValidWaiver}
                />
              ))
            ) : (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500 font-medium">No completed tickets found.</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default withAuth(Ticket);
