import { XIcon } from 'lucide-react';
import React, { useState, useEffect, useCallback } from 'react';
import { useF<PERSON><PERSON><PERSON><PERSON>, Controller, UseFormReturn } from 'react-hook-form';

import { CustomPhoneInput } from '@/src/components/phone-input';
import { <PERSON><PERSON> } from '@/src/components/ui/button';
import { Card, CardHeader } from '@/src/components/ui/card';
import { FormItem, FormLabel, FormMessage } from '@/src/components/ui/form';
import { Input } from '@/src/components/ui/input';
import { GuestSchema } from '@/src/features/u-pick/guests/guestFormSchema';
import { AGE_GROUPS, QUESTION_TYPES, USER_ROLES } from '@/src/lib/constants';
import { cn } from '@/src/lib/utils';
import { useAppSelector, RootState } from '@/src/store';
import { EventQuestions } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';
import { AgeGroup, Users } from '@/src/types/Users';

/**
 * Default guest information template
 * Used when adding new guests to the form
 */
const DEFAULT_GUEST: Omit<GuestSchema, 'temp_id'> = {
  id: '',
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
  role: USER_ROLES.CUSTOMER,
  age_group: AGE_GROUPS.ADULT as AgeGroup,
} as const;

/**
 * Props interface for SelectGuestCard component
 * @interface SelectGuestCardProps
 */
interface SelectGuestCardProps {
  questions: EventQuestions[]; // Event-specific questions for guests
  form: UseFormReturn<{
    booker: Users;
    guests: GuestSchema[];
  }>; // Form control object from react-hook-form
  schedule: EventSchedules; // Event schedule details including pricing and capacity
  isAuthenticated?: boolean; // Add this line
}

/**
 * Props interface for the FormInput component
 * @interface FormInputProps
 */
interface FormInputProps {
  field: any;
  type: string;
  required: boolean;
  label: string;
  error?: string;
  disabled?: boolean;
  hasValue?: boolean;
  autoComplete?: string;
}

/**
 * Memoized form input component for guest details
 * @component
 */
const FormInput = React.memo(
  ({ field, type, required, label, error, disabled, hasValue, autoComplete }: FormInputProps) => (
    <FormItem>
      <FormLabel>{label}</FormLabel>
      {type === QUESTION_TYPES.PHONE ? (
        <CustomPhoneInput {...field} required={required} error={!!error} className="w-full" />
      ) : (
        <Input
          {...field}
          type={type}
          required={required}
          className={cn('w-full', disabled && hasValue && 'bg-gray-100')}
          disabled={disabled && hasValue}
          autoComplete={autoComplete}
        />
      )}
      {error && <FormMessage>{error}</FormMessage>}
    </FormItem>
  )
);

FormInput.displayName = 'FormInput';

export const SelectGuestCard: React.FC<SelectGuestCardProps> = ({
  questions,
  form,
  schedule,
  isAuthenticated,
}) => {
  // Move the useAppSelector hook to the component level
  const user = useAppSelector((state: RootState) => state.authentication.user);

  /**
   * Start Initials
   */
  // Local state for tracking guest counts
  const [guestCounts, setGuestCounts] = useState({
    adults: 1,
    children: 0,
    total: 1,
  });

  // Form array management for dynamic guest fields
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'guests',
  });
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  useEffect(() => {
    const adults = fields.filter(
      field => (field as GuestSchema).age_group === AGE_GROUPS.ADULT
    ).length;
    const children = fields.filter(
      field => (field as GuestSchema).age_group === AGE_GROUPS.CHILD // Changed from ADULT to CHILD
    ).length;
    setGuestCounts({
      adults,
      children,
      total: adults + children,
    });
  }, [fields]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */

  /**
   * Handles adding or removing guests of specific age groups
   *
   * @param {AgeGroup} type - Age group of the guest (adult/child)
   * @param {'add' | 'remove'} operation - Whether to add or remove a guest
   */

  const handleGuestChange = (type: AgeGroup, operation: 'add' | 'remove') => {
    const isAdult = type === AGE_GROUPS.ADULT;
    const currentCount = isAdult ? guestCounts.adults : guestCounts.children;
    const minCount = isAdult ? 1 : 0;

    if (operation === 'add' && guestCounts.total < (schedule.max_no_of_guest ?? 0)) {
      const newGuest = {
        ...DEFAULT_GUEST,
        //TODO-DATE
        temp_id: Date.now(),
        age_group: type,
        ...(type === AGE_GROUPS.CHILD && {
          email: null,
          phone: null,
          first_name: '',
          last_name: '',
        }),
      };
      // Use append with { shouldFocus: false } to prevent auto-scrolling
      append(newGuest, { shouldFocus: false });
    } else if (operation === 'remove' && currentCount > minCount) {
      const lastIndex = fields.map(f => (f as GuestSchema).age_group).lastIndexOf(type);
      if (lastIndex !== -1) {
        remove(lastIndex);
      }
    }
  };

  /**
   * End Custom Methods
   */
  const GuestCounter: React.FC<{
    type: AgeGroup;
    count: number;
    label: string;
    subtitle: string;
    description?: string;
  }> = ({ type, count, label, subtitle, description }) => {
    const handleMouseDown = (operation: 'add' | 'remove') => (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      handleGuestChange(type, operation);
    };
    return (
      <div className="flex justify-between items-center mb-3">
        <div>
          <h3 className="font-semibold text-md">
            {label} <span className="text-gray-500 text-sm">({subtitle})</span>
          </h3>
          {description && <p className="text-gray-500 text-xs">{description}</p>}
          <div className="md:hidden">
            {typeof schedule?.price_per_adult === 'number' && type === AGE_GROUPS.ADULT && (
              <p className="text-gray-500 text-xs">${schedule.price_per_adult} per adult</p>
            )}
            {typeof schedule?.price_per_child === 'number' && type === AGE_GROUPS.CHILD && (
              <p className="text-gray-500 text-xs">${schedule.price_per_child} per child</p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <div className="max-md:hidden">
            {typeof schedule?.price_per_adult === 'number' && type === AGE_GROUPS.ADULT && (
              <p className="text-gray-500 text-xs">${schedule.price_per_adult} per adult</p>
            )}
            {typeof schedule?.price_per_child === 'number' && type === AGE_GROUPS.CHILD && (
              <p className="text-gray-500 text-xs">${schedule.price_per_child} per child</p>
            )}
          </div>
          <span className="text-lg w-6 text-center">{count}</span>
          <Button
            type="button"
            variant="outline"
            onMouseDown={handleMouseDown('add')}
            onClick={e => e.preventDefault()}
            className="w-8 h-8 p-0"
            disabled={guestCounts.total >= (schedule.max_no_of_guest ?? 0)}
            tabIndex={-1}
          >
            +
          </Button>
        </div>
      </div>
    );
  };

  /**
   * Renders form fields for an individual guest
   *
   * @param {GuestSchema} guest - Guest data
   * @param {number} index - Guest index in the form array
   * @param {boolean} isOnlyAdult - Whether this is the only adult guest
   * @returns {JSX.Element} Guest form fields
   */
  const renderGuestFields = useCallback(
    (guest: GuestSchema, index: number, isOnlyAdult: boolean) => {
      const isFirstGuest = index === 0;
      const isAdult = guest.age_group === AGE_GROUPS.ADULT;

      // Get questions for the guest's age group
      let guestQuestions = questions.filter(q => q.show_for.includes(guest.age_group as AgeGroup));

      // For first adult (organizer), ensure phone question exists
      if (isFirstGuest && isAdult) {
        const hasPhoneQuestion = guestQuestions.some(q => q.question_type === QUESTION_TYPES.PHONE);
        if (!hasPhoneQuestion) {
          guestQuestions = [
            ...guestQuestions,
            {
              question_type: QUESTION_TYPES.PHONE,
              question_text: 'Phone Number',
              is_required: true,
              show_for: [AGE_GROUPS.ADULT as AgeGroup],
            },
          ];
        }
      }

      const getTicketLabel = () => {
        if (isFirstGuest) {
          return isOnlyAdult
            ? "Ticket 1 (Adult, 'You')"
            : "Ticket 1 (Adult, 'You', Event Organizer)";
        }
        return `Ticket ${index + 1} (${guest.age_group === AGE_GROUPS.ADULT ? AGE_GROUPS.ADULT : AGE_GROUPS.CHILD})`;
      };

      return (
        <div className="mt-6 space-y-3 " key={guest.temp_id}>
          <div className="flex justify-between items-center">
            <h4 className="text-md font-semibold text-gray-800 capitalize">{getTicketLabel()}</h4>
            {/* Only show remove button if not the first adult */}
            {!isFirstGuest && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700"
                onClick={() => remove(index)}
                tabIndex={-1}
              >
                <XIcon className="h-4 w-4" aria-label="Remove Guest" />
              </Button>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {guestQuestions.map((question, qIndex) => {
              const fieldName =
                question.question_type === QUESTION_TYPES.EMAIL
                  ? 'email'
                  : question.question_type === QUESTION_TYPES.PHONE
                    ? 'phone'
                    : question.question_text.toLowerCase().includes('last')
                      ? 'last_name'
                      : 'first_name';

              // Get the current field value and initial value
              const initialValue = isAuthenticated && isFirstGuest ? user?.[fieldName] : '';
              // Check if there was an initial value provided
              const hasInitialValue = Boolean(initialValue && initialValue.trim().length > 0);

              // Special handling for phone numbers
              const hasValue =
                fieldName === 'phone'
                  ? Boolean(initialValue && initialValue.replace(/[+\s-]/g, '').length > 0)
                  : hasInitialValue;

              // Only disable if:
              // 1. It's the first guest AND
              // 2. User is authenticated AND
              // 3. The field had an initial value from the logged-in user
              const shouldDisable = isFirstGuest && isAuthenticated && hasValue;

              return (
                <Controller
                  key={qIndex}
                  control={form.control}
                  name={`guests.${index}.${fieldName}`}
                  render={({ field, fieldState: { error } }) => (
                    <FormInput
                      field={field}
                      type={question.question_type}
                      required={question.is_required}
                      label={question.question_text}
                      error={error?.message}
                      disabled={shouldDisable}
                      hasValue={hasValue}
                      autoComplete={getAutoComplete(fieldName, index)}
                    />
                  )}
                />
              );
            })}
          </div>
        </div>
      );
    },
    [questions, form.control, remove, isAuthenticated, form.getValues, user]
  );

  return (
    <Card className="p-4 shadow-md">
      <GuestCounter
        type={AGE_GROUPS.ADULT as AgeGroup}
        count={guestCounts.adults}
        label="Adults"
        subtitle="Above 13+"
      />
      <GuestCounter
        type={AGE_GROUPS.CHILD as AgeGroup}
        count={guestCounts.children}
        label="Children"
        subtitle="Below 13"
        description="A parent/guardian must sign a waiver on behalf of the child."
      />
      <div className="flex justify-end">
        <p className="text-gray-600 text-sm font-bold">
          Total Price: $
          {guestCounts.adults * (schedule.price_per_adult ?? 0) +
            guestCounts.children * (schedule.price_per_child ?? 0)}
        </p>
      </div>
      {guestCounts.total >= (schedule.max_no_of_guest ?? 0) && (
        <p className="text-red-500 text-sm mt-4">
          Maximum number of guests ({schedule.max_no_of_guest ?? 0}) reached
        </p>
      )}
      <CardHeader className="px-0 pt-0 mb-[-30px]">
        <h2 className="font-semibold text-lg mt-4 mb-[-5px]">Guest Information</h2>
        <p className="text-gray-600 text-sm">
          Please fill in the required information for each guest.
        </p>
      </CardHeader>

      {fields.map((field, index) =>
        renderGuestFields(
          field as GuestSchema,
          index,
          (field as GuestSchema).age_group === AGE_GROUPS.ADULT && guestCounts.total <= 1
        )
      )}
    </Card>
  );
};

export default React.memo(SelectGuestCard);

/**
 * Determines the appropriate autocomplete attribute for form fields
 * @param fieldName - The name of the field (email, phone, first_name, last_name)
 * @param index - The index of the guest in the form
 * @returns The autocomplete attribute value
 */
const getAutoComplete = (fieldName: string, index: number): string => {
  // Only provide autocomplete suggestions for the first guest
  if (index === 0) {
    switch (fieldName) {
      case 'email':
        return 'email';
      case 'phone':
        return 'tel';
      case 'first_name':
        return 'given-name';
      case 'last_name':
        return 'family-name';
      default:
        return 'off';
    }
  }
  return 'off'; // Disable autocomplete for additional guests
};
