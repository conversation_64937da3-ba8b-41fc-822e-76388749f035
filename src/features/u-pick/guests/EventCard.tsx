'use client';

import { CalendarIcon, MapPinIcon, TicketsIcon } from 'lucide-react';
import React, { useState } from 'react';

import { Button } from '@/src/components/ui/button';
import { Calendar } from '@/src/components/ui/calendar';
import { Card, CardHeader, CardContent } from '@/src/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/src/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/src/components/ui/select';
import { getPacificTimeNow } from '@/src/lib/timezone';
import { cn, formatDate, formatTime } from '@/src/lib/utils';
import { Events } from '@/src/types/Events';
import { EventSchedules } from '@/src/types/EventSchedules';

/**
 * Props interface for the EventCard component
 * @interface EventCardProps
 * @property {Events} event - The event details including title, description, location, and date range
 * @property {EventSchedules} schedule - The specific schedule details including start/end times
 */
interface EventCardProps {
  event: Events;
  schedule: EventSchedules;
}

const EventCard = ({ event, schedule }: EventCardProps) => {
  /**
   * Start Initials
   */
  const [eventDate, setEventDate] = useState<Date>(
    new Date(schedule?.event_date || getPacificTimeNow())
  );

  // Initialize time state with schedule times or empty string as fallback
  const [selectedTime, setSelectedTime] = useState<string>(
    schedule?.start_time && schedule?.end_time ? `${schedule.start_time}-${schedule.end_time}` : ''
  );
  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  const handleLocationClick = () => {
    if (event?.event_location?.latitude && event?.event_location?.longitude) {
      // Google Maps URL with destination coordinates
      const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${event.event_location.latitude},${event.event_location.longitude}`;
      window.open(mapsUrl, '_blank');
    }
  };
  /**
   * End Custom Methods
   */

  if (!event || !schedule) return null;

  return (
    <Card className="p-3 shadow-md max-w-2xl mx-auto mb-3">
      {/* Date and Time Selection Section */}
      <div className="flex max-md:flex-col items-center mb-4 gap-2">
        {/* Date Picker */}
        <div className="flex items-center w-full">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                disabled
                className={cn(
                  'w-full justify-start text-left font-normal',
                  !eventDate && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {eventDate ? formatDate(eventDate) : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={eventDate}
                onSelect={date => date && setEventDate(date)}
                initialFocus
                disabled={date =>
                  // Disable dates outside event's date range
                  date < new Date(event.start_date || getPacificTimeNow()) ||
                  date > new Date(event.end_date || getPacificTimeNow())
                }
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Time Selector */}
        <div className="flex items-center w-full">
          <Select disabled value={selectedTime} onValueChange={setSelectedTime}>
            <SelectTrigger className="w-full border-gray-300 focus:ring-1 focus:ring-gray-400 p-1 text-sm">
              <SelectValue placeholder="Select Time">
                {selectedTime
                  ? `${formatTime(schedule.start_time)} - ${formatTime(schedule.end_time)}`
                  : 'Select Time'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={`${schedule.start_time}-${schedule.end_time}`}>
                {`${formatTime(schedule.start_time)} - ${formatTime(schedule.end_time)}`}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Event Details Section */}
      <CardHeader className="text-md flex font-semibold p-0 py-1 m-0">
        <span className="flex items-center gap-2">
          <TicketsIcon size={18} /> {event.title}
        </span>
      </CardHeader>

      <CardContent className="text-gray-600 text-sm p-0 py-1 m-0">{event.description}</CardContent>

      {/* Location Information */}
      <div
        className="flex items-center text-sm text-gray-800 mt-2 gap-2 hover:text-mulberry cursor-pointer group"
        onClick={handleLocationClick}
      >
        <MapPinIcon className="h-4 w-4 text-gray-500 group-hover:text-mulberry flex-shrink-0" />
        <span className="group-hover:text-mulberry">
          {event.event_location?.street}, {event.event_location?.city},{' '}
          {event.event_location?.state}
          {event.event_location?.zip_code ? `, ${event.event_location?.zip_code}` : ''}
        </span>
      </div>
    </Card>
  );
};

export default EventCard;
