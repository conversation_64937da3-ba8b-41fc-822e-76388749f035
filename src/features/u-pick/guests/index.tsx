'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';

import ErrorAlert from '@/src/components/error-alert';
import Spinner from '@/src/components/spinner';
import { Button } from '@/src/components/ui/button';
import { Form } from '@/src/components/ui/form';
import { updateUserProfile } from '@/src/features/auth/slices/authenticationSlice';
import { createGuestFormSchema, GuestSchema } from '@/src/features/u-pick/guests/guestFormSchema';
import SelectGuestCard from '@/src/features/u-pick/guests/SelectGuestCard';
import {
  fetchEventAndSchedule,
  getTicketStocks,
  saveGuestData,
} from '@/src/features/u-pick/slices/guestsSlice';
import {
  AGE_GROUPS,
  BUTTON_TEXTS,
  ROUTES,
  USER_ROLES,
  VALIDATION_ERROR_MESSAGES,
} from '@/src/lib/constants';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';
import { AgeGroup, UserRole, Users } from '@/src/types/Users';

import EventCard from './EventCard';

type FormValues = {
  booker: Users;
  guests: GuestSchema[];
};

const validateAndSetPhoneErrors = (data: FormValues, form: UseFormReturn<FormValues>): boolean => {
  let hasErrors = false;

  data.guests.forEach((guest, index) => {
    if (guest.age_group === AGE_GROUPS.ADULT) {
      let { phone } = guest;

      // Only validate phone for the first guest (organizer)
      if (index === 0) {
        // Add "+" prefix if not present
        if (phone && !phone.startsWith('+')) {
          phone = `+${phone}`;
          // Update the form value with the formatted phone number
          form.setValue(`guests.${index}.phone`, phone);
        }

        if (!phone || phone === '' || phone === '+') {
          hasErrors = true;
          form.setError(`guests.${index}.phone`, {
            type: 'manual',
            message: VALIDATION_ERROR_MESSAGES.PHONE_REQUIRED_FOR_ADULTS,
          });
        } else {
          try {
            const phoneNumber = parsePhoneNumberWithError(phone);
            if (!phoneNumber.isValid()) {
              hasErrors = true;
              form.setError(`guests.${index}.phone`, {
                type: 'manual',
                message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER,
              });
            }
          } catch (_error) {
            hasErrors = true;
            form.setError(`guests.${index}.phone`, {
              type: 'manual',
              message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_FORMAT,
            });
          }
        }
      } else {
        // For other adult guests, if phone is provided, validate the format
        if (phone) {
          if (!phone.startsWith('+')) {
            phone = `+${phone}`;
            form.setValue(`guests.${index}.phone`, phone);
          }
          try {
            const phoneNumber = parsePhoneNumberWithError(phone);
            if (!phoneNumber.isValid()) {
              hasErrors = true;
              form.setError(`guests.${index}.phone`, {
                type: 'manual',
                message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER,
              });
            }
          } catch (_error) {
            hasErrors = true;
            form.setError(`guests.${index}.phone`, {
              type: 'manual',
              message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_FORMAT,
            });
          }
        }
      }
    }
  });

  return hasErrors;
};

export default function Guests() {
  /**
   * Start Initials
   */
  const params = useParams();
  const eventId = params.eventId as string;
  const scheduleId = params.scheduleId as string;

  const router = useRouter();
  const dispatch = useAppDispatch();
  const { event, schedule, loading, error, availableTicketStock } = useAppSelector(
    (state: RootState) => state.guests
  );
  const { user, isAuthenticated } = useAppSelector((state: RootState) => state.authentication);

  const form = useForm<FormValues>({
    resolver: zodResolver(createGuestFormSchema(event?.event_questions || [])),
    defaultValues: {
      guests: [
        {
          //TODO-DATE
          temp_id: Date.now(),
          id: user?.id || '',
          role: USER_ROLES.CUSTOMER as AgeGroup,
          age_group: AGE_GROUPS.ADULT as UserRole,
          first_name: user?.first_name || '',
          last_name: user?.last_name || '',
          email: user?.email || '',
          phone: user?.phone || '', // Add this line to include phone
        },
      ],
    },
    mode: 'onChange',
  });
  /**
   * End Initials
   */

  useEffect(() => {
    if (event?.event_questions) {
      form.clearErrors();
      form.reset(form.getValues());
    }
  }, [event?.event_questions, form]);

  // Add new useEffect to update form when user auth state changes
  useEffect(() => {
    if (isAuthenticated && user) {
      const currentGuests = form.getValues('guests');
      if (currentGuests.length > 0) {
        form.setValue('guests.0', {
          ...currentGuests[0],
          id: user.id as string,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email || '',
          phone: user.phone || '', // Add this line to include phone
        });
      }
    }
  }, [isAuthenticated, user, form]);

  useEffect(() => {
    if (eventId && scheduleId) {
      dispatch(fetchEventAndSchedule({ eventId, scheduleId }));
    }
  }, [dispatch, eventId, scheduleId]);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = async (data: FormValues) => {
    const hasPhoneErrors = validateAndSetPhoneErrors(data, form);
    if (hasPhoneErrors) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Check ticket stock first
      const availableTickets = await dispatch(
        getTicketStocks({ event_id: eventId, schedule_id: scheduleId })
      ).unwrap();

      // Check if we have enough tickets for all guests
      const totalGuests = data.guests.length;
      if (availableTickets < totalGuests) {
        toast.error(
          `${availableTickets === 1 ? 'Sorry, only 1 ticket is' : `Sorry, only ${availableTickets} tickets are`} available in the selected slot. Try again with a different slot.`
        );
        setIsSubmitting(false);
        return;
      }

      // Helper function to format names
      const formatName = (name: string): string => {
        const trimmed = name.trim();
        trimmed.toLowerCase();
        return trimmed.charAt(0).toUpperCase() + trimmed.slice(1);
      };

      // Get the first adult as organizer
      const firstGuest: Users = {
        id: data.guests[0].id,
        first_name: formatName(data.guests[0].first_name || ''),
        last_name: formatName(data.guests[0].last_name || ''),
        email: data.guests[0].email || '',
        phone: data.guests[0].phone || '',
        role: USER_ROLES.CUSTOMER as UserRole,
        age_group: AGE_GROUPS.ADULT as AgeGroup,
        marketing_consent: false,
      };

      // Update authenticated user's phone number and names if they're missing or changed
      if (isAuthenticated && user) {
        const updates: {
          userId: string;
          phone?: string;
          firstName?: string;
          lastName?: string;
        } = { userId: user.id as string };

        // Check if phone has changed
        if (firstGuest.phone && firstGuest.phone !== user.phone) {
          updates.phone = firstGuest.phone;
        }

        // Check if first name is missing or empty
        if (firstGuest.first_name && (!user.first_name || user.first_name.trim() === '')) {
          updates.firstName = firstGuest.first_name;
        }

        // Check if last name is missing or empty
        if (firstGuest.last_name && (!user.last_name || user.last_name.trim() === '')) {
          updates.lastName = firstGuest.last_name;
        }

        // Only dispatch if there are updates to make
        if (Object.keys(updates).length > 1) {
          // > 1 because userId is always present
          try {
            await dispatch(updateUserProfile(updates)).unwrap();
          } catch (error) {
            console.error('Failed to update user profile:', error);
            // Continue with the booking process even if profile update fails
          }
        }
      }

      if (data.guests.length === 1) {
        await dispatch(
          saveGuestData({
            booker: firstGuest,
            guests: [firstGuest],
          })
        );
      } else {
        const remainingGuests: Users[] = data.guests.slice(1).map(guest => ({
          id: guest.id,
          first_name: formatName(guest.first_name || ''),
          last_name: formatName(guest.last_name || ''),
          email: guest.age_group === AGE_GROUPS.CHILD ? undefined : guest.email || undefined,
          phone: guest.age_group === AGE_GROUPS.CHILD ? undefined : guest.phone || undefined,
          role: USER_ROLES.CUSTOMER as UserRole,
          age_group: guest.age_group as AgeGroup,
          marketing_consent: false, // Add this required property
        }));

        await dispatch(
          saveGuestData({
            booker: firstGuest,
            guests: [firstGuest, ...remainingGuests],
          })
        );
      }

      router.push(ROUTES.TICKET_SUMMARY(eventId, scheduleId));
    } catch (error) {
      console.error('Form submission failed:', error);
      toast.error('Failed to process your request. Please try again.', {
        duration: 3000,
        id: 'submission-error',
      });
      setIsSubmitting(false); // Clear loading state on error
    }
  };

  // 1. First check loading state
  if (loading || isSubmitting) return <Spinner />;
  if (error) return <ErrorAlert error={error} title="Error" />;

  // 2. Check if required data exists
  if (!event || !schedule) {
    return null; // Return null during loading instead of error
  }

  // 3. Now render the full content when we have all required data
  return (
    <div className="max-w-2xl mx-auto pb-6">
      <EventCard event={event} schedule={schedule} />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <SelectGuestCard
            questions={event?.event_questions || []}
            form={form}
            schedule={schedule}
            isAuthenticated={isAuthenticated}
          />
          <div className="space-y-2">
            {availableTicketStock === 0 && (
              <div className="text-red-500 text-center font-medium">
                Sorry, this event is sold out!
              </div>
            )}
            <Button
              type="submit"
              className="w-full bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive disabled:opacity-50"
            >
              {BUTTON_TEXTS.CONTINUE}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
