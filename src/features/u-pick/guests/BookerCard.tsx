import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { Card } from '@/src/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/src/components/ui/form';
import { Input } from '@/src/components/ui/input';
import { GuestSchema } from '@/src/features/u-pick/guests/guestFormSchema';
import { FORM_LABELS } from '@/src/lib/constants';

/**
 * Interface for the form data structure used in BookerCard
 * @interface BookerFormData
 * @property {Object} booker - The main booker's personal information
 * @property {string} booker.firstname - <PERSON>'s first name
 * @property {string} booker.lastname - <PERSON>'s last name
 * @property {string} booker.email - <PERSON>'s email address
 * @property {string} booker.phone - <PERSON>'s phone number
 * @property {GuestSchema[]} guests - Array of additional guests' information
 *
 * @example
 * ```typescript
 * const formData: BookerFormData = {
 *   booker: {
 *     firstname: "<PERSON>",
 *     lastname: "<PERSON><PERSON>",
 *     email: "<EMAIL>",
 *     phone: "1234567890"
 *   },
 *   guests: [] // Array of GuestSchema objects
 * };
 * ```
 */
interface BookerFormData {
  booker: {
    firstname: string; // Required: Booker's first name
    lastname: string; // Required: Booker's last name
    email: string; // Required: Valid email address
    phone: string; // Required: Contact phone number
  };
  guests: GuestSchema[]; // Array of additional guests
}

/**
 * Props interface for the BookerCard component
 * @interface BookerCardProps
 * @property {UseFormReturn<BookerFormData>} form - React Hook Form instance containing form methods and state
 */
interface BookerCardProps {
  form: UseFormReturn<BookerFormData>;
}

const BookerCard: React.FC<BookerCardProps> = ({ form }) => {
  return (
    <Card className="p-4 shadow-md mx-auto mt-6">
      {/* Card title */}
      <h1 className="text-lg font-semibold">Organizer Details</h1>

      {/* Form fields grid layout */}
      <div className="grid grid-cols-2 gap-4 max-md:grid-cols-1">
        {/* First Name field */}
        <FormField
          control={form.control}
          name="booker.firstname"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{FORM_LABELS.FIRST_NAME}</FormLabel>
              <FormControl>
                <Input placeholder="John" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Last Name field */}
        <FormField
          control={form.control}
          name="booker.lastname"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{FORM_LABELS.LAST_NAME}</FormLabel>
              <FormControl>
                <Input placeholder="Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Email field */}
        <FormField
          control={form.control}
          name="booker.email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{FORM_LABELS.EMAIL}</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Phone Number field */}
        <FormField
          control={form.control}
          name="booker.phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{FORM_LABELS.PHONE}</FormLabel>
              <FormControl>
                <Input placeholder="Enter Your Phone Number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </Card>
  );
};

export default BookerCard;
