import { parsePhoneNumberWithError } from 'libphonenumber-js';
import { z } from 'zod';

import { AGE_GROUPS, USER_ROLES, VALIDATION_ERROR_MESSAGES } from '@/src/lib/constants';
import { EventQuestions } from '@/src/types/Events';
import { Users } from '@/src/types/Users';

/**
 * Creates a Zod schema for validating individual guest information
 *
 * @function createGuestSchema
 * @returns {z.ZodObject} A Zod schema object with validation rules for guest details
 *
 * @remarks
 * - Email validation is only enforced for adult guests
 * - Phone validation is only enforced for adult guests
 * - All guests require first name, last name, and age group
 *
 * @example
 * ```typescript
 * const guestSchema = createGuestSchema();
 * const validationResult = guestSchema.safeParse({
 *   temp_id: 1,
 *   id: "guest_123",
 *   role: "customer",
 *   age_group: "adult",
 *   first_name: "<PERSON>",
 *   last_name: "<PERSON><PERSON>",
 *   email: "<EMAIL>",
 *   phone: "1234567890"
 * });
 * ```
 */
export const createGuestSchema = () => {
  return z.object({
    // Temporary ID for form management
    temp_id: z.number(),

    // Unique identifier for the guest
    id: z.string(),

    // Guest role - currently limited to customer
    role: z.literal(USER_ROLES.CUSTOMER),

    // Age group classification
    age_group: z.enum([AGE_GROUPS.ADULT, AGE_GROUPS.CHILD, AGE_GROUPS.SENIOR] as const),

    // Basic personal information
    first_name: z.string().min(2, VALIDATION_ERROR_MESSAGES.FIRST_NAME_MIN),
    last_name: z.string().min(2, VALIDATION_ERROR_MESSAGES.LAST_NAME_MIN),

    // Email validation with special rules for adults
    email: z.union([z.string().email('Invalid email'), z.null()]).superRefine((val, ctx) => {
      const ageGroup = (ctx.path[0] as unknown as { age_group: Users['age_group'] }).age_group;
      // Only validate email for adults
      if (ageGroup === AGE_GROUPS.ADULT && !val) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: VALIDATION_ERROR_MESSAGES.EMAIL_REQUIRED_FOR_ADULTS,
        });
      }
    }),

    // Phone validation with special rules for adults
    phone: z.union([z.string(), z.null()]).superRefine((val, ctx) => {
      const ageGroup = (
        ctx.path[0] as unknown as {
          age_group: Users['age_group'];
        }
      ).age_group;
      const index = parseInt((ctx.path[1] as string) || '0', 10);

      // Only require phone for first adult (organizer)
      if (ageGroup === AGE_GROUPS.ADULT && index === 0) {
        // Check if value exists and is not empty
        if (!val || val.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: VALIDATION_ERROR_MESSAGES.PHONE_REQUIRED_FOR_ADULTS,
          });
          return;
        }

        // Remove all non-digit characters for length check
        const digitsOnly = val.replace(/\D/g, '');

        // Check minimum length (including country code)
        if (digitsOnly.length < 11) {
          // +1 plus 10 digits
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: VALIDATION_ERROR_MESSAGES.PHONE_MIN,
          });
          return;
        }

        try {
          const phoneNumber = parsePhoneNumberWithError(val);

          if (!phoneNumber.isValid()) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER,
            });
            return;
          }
        } catch (_error) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_FORMAT,
          });
        }
      } else if (ageGroup === AGE_GROUPS.ADULT && val) {
        // For other adults, validate format only if phone is provided
        try {
          const phoneNumber = parsePhoneNumberWithError(val);
          if (!phoneNumber.isValid()) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_NUMBER,
            });
          }
        } catch (_error) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: VALIDATION_ERROR_MESSAGES.INVALID_PHONE_FORMAT,
          });
        }
      }
    }),
  });
};

/**
 * Creates a Zod schema for validating the entire guest form
 *
 * @function createGuestFormSchema
 * @param {EventQuestions[]} _questions - Array of event-specific questions (reserved for future use)
 * @returns {z.ZodObject} A Zod schema object for the entire form
 *
 * @example
 * ```typescript
 * const formSchema = createGuestFormSchema([]);
 * const validationResult = formSchema.safeParse({
 *   guests: [ guest objects ]
 * });
 * ```
 */
export const createGuestFormSchema = (_questions: EventQuestions[]) => {
  return z.object({
    guests: z.array(createGuestSchema()),
  });
};

/**
 * Type definitions for schema inference
 *
 * @typedef {z.infer<ReturnType<typeof createGuestSchema>>} GuestSchema - Type for individual guest data
 * @typedef {z.infer<ReturnType<typeof createGuestFormSchema>>} GuestFormSchema - Type for the entire form data
 */
export type GuestSchema = z.infer<ReturnType<typeof createGuestSchema>>;
export type GuestFormSchema = z.infer<ReturnType<typeof createGuestFormSchema>>;
