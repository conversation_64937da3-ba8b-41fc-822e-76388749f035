import { Document, Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';
import { parse, format, isValid } from 'date-fns';
import QRCode from 'qrcode';
import React, { useEffect, useState } from 'react';

import { formatTime } from '@/src/lib/utils';
import { TicketTailorOrder } from '@/src/types/TicketTailor';

const styles = StyleSheet.create({
  page: {
    padding: 40,
    backgroundColor: 'white',
  },
  container: {
    overflow: 'hidden',
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  header: {
    padding: 20,
    textAlign: 'center',
    color: '#000',
  },
  headerText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
  },
  subHeaderText: {
    fontSize: 18,
    color: '#000',
    marginTop: 5,
  },
  content: {
    padding: '30px 40px',
    flex: 1,
    backgroundColor: '#ffffff',
  },
  infoGrid: {
    display: 'flex',
    flexDirection: 'column',
    gap: 15,
    marginBottom: 30,
  },
  infoRow: {
    display: 'flex',
    flexDirection: 'row',
    marginBottom: 8,
    paddingBottom: 8,
  },
  label: {
    width: '40%',
    fontWeight: 'bold',
    fontSize: 14,
    color: '#000000',
  },
  value: {
    width: '60%',
    fontSize: 14,
    color: '#000000',
  },
  qrCodeContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 0, // Changed from 25 to 0
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 8,
  },
  qrCode: {
    width: 200,
    height: 200,
  },
  footer: {
    padding: 15,
    textAlign: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#000000',
    marginBottom: 5,
  },
});

const ParkingPassPDF = ({ orders }: { orders: TicketTailorOrder[] }) => {
  const [qrCodes, setQrCodes] = useState<Record<string, string>>({});

  const generateQRCode = async (barcode: string) => {
    try {
      const qrCodeDataUrl = await QRCode.toDataURL(barcode, {
        width: 180,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      });
      return qrCodeDataUrl;
    } catch (err) {
      console.error('Error generating QR code:', err);
      return '';
    }
  };

  useEffect(() => {
    const loadQRCodes = async () => {
      const codes: Record<string, string> = {};
      for (const order of orders) {
        if (order.barcode) {
          codes[order.barcode] = await generateQRCode(order.barcode);
        }
      }
      setQrCodes(codes);
    };

    loadQRCodes();
  }, [orders]);

  const formatDate = (dateString: string) => {
    if (!dateString) return '';

    try {
      // Parse the date string using date-fns
      const parsedDate = parse(dateString, 'yyyy-MM-dd', new Date());

      // Check if the date is valid
      if (isValid(parsedDate)) {
        // Format the date using date-fns to "Month d, yyyy" format
        return format(parsedDate, 'MMMM d, yyyy');
      }

      // Fallback for other date formats
      const date = new Date(dateString);
      if (isValid(date)) {
        return format(date, 'MMMM d, yyyy');
      }

      return '';
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  return (
    <Document>
      {orders.map((order, index) => {
        const eventDetails = order.event_details;
        const eventName = eventDetails?.name || 'My Very Mulberry';
        const eventDate = eventDetails?.start_date?.date || order.event_date;
        const ticketType = order.ticket_type
          ? order.ticket_type.replace('Add-on: ', '')
          : 'Parking Pass';

        return (
          <Page key={index} size="A4" style={styles.page}>
            <View style={styles.container}>
              <View style={styles.header}>
                <Text style={styles.headerText}>{ticketType}</Text>
              </View>

              <View style={styles.content}>
                <View style={styles.infoGrid}>
                  <View style={styles.infoRow}>
                    <Text style={styles.label}>Ticket Code:</Text>
                    <Text style={styles.value}>{order.barcode || 'N/A'}</Text>
                  </View>

                  <View style={styles.infoRow}>
                    <Text style={styles.label}>Event Name:</Text>
                    <Text style={styles.value}>{eventName}</Text>
                  </View>

                  <View style={styles.infoRow}>
                    <Text style={styles.label}>Event Date:</Text>
                    <Text style={styles.value}>{formatDate(eventDate)}</Text>
                  </View>

                  <View style={styles.infoRow}>
                    <Text style={styles.label}>Event Time:</Text>
                    <Text style={styles.value}>
                      {eventDetails?.start_date?.time && eventDetails?.end_date?.time
                        ? `${formatTime(eventDetails.start_date.time)} - ${formatTime(eventDetails.end_date.time)}`
                        : 'N/A'}
                    </Text>
                  </View>
                </View>

                <View style={styles.qrCodeContainer}>
                  {order.barcode && qrCodes[order.barcode] && (
                    <Image src={qrCodes[order.barcode]} style={styles.qrCode} />
                  )}
                </View>
              </View>

              <View style={styles.footer}>
                <Text style={styles.footerText}>My Very Mulberry - U-Pick Experience</Text>
                <Text style={styles.footerText}>
                  501 Hoffman Lane, Brentwood, CA 94513 | <EMAIL>
                </Text>
              </View>
            </View>
          </Page>
        );
      })}
    </Document>
  );
};

export default ParkingPassPDF;
