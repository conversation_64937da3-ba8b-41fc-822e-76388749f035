import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { apiGet } from '@/src/lib/apiClient'; // Import apiClient
import { API_ENDPOINTS, ERROR_MESSAGES } from '@/src/lib/constants';
import { FarmerMarket } from '@/src/types/FarmerMarket';

interface FarmerMarketState {
  farmerMarkets: FarmerMarket[];
  loading: boolean;
  error: string | null;
}

const initialState: FarmerMarketState = {
  farmerMarkets: [],
  loading: false,
  error: null,
};

export const fetchFarmerMarkets = createAsyncThunk(
  'farmerMarket/fetchFarmerMarkets',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiGet(API_ENDPOINTS.FARMER_MARKETS); // Use apiGet
      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.error || ERROR_MESSAGES.FAILED_TO_FETCH_FARMER_MARKETS);
      }
      const data = await response.json();
      return data.data as FarmerMarket[];
    } catch (_error) {
      return rejectWithValue(ERROR_MESSAGES.FAILED_TO_FETCH_FARMER_MARKETS);
    }
  }
);

const farmerMarketSlice = createSlice({
  name: 'farmerMarket',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchFarmerMarkets.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFarmerMarkets.fulfilled, (state, action) => {
        state.loading = false;
        state.farmerMarkets = action.payload;
      })
      .addCase(fetchFarmerMarkets.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default farmerMarketSlice.reducer;
