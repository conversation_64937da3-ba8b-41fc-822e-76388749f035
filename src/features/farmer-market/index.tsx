import { format, isBefore, isAfter, startOfToday, parseISO, eachDayOfInterval } from 'date-fns';
import { Clock as ClockIcon, MapPin, MapPinned, XCircle } from 'lucide-react';
import React, { useEffect, useState, useRef, useCallback } from 'react';

import Spinner from '@/src/components/spinner';
import { Card, CardContent, CardHeader } from '@/src/components/ui/card';
import { ENDPOINTS } from '@/src/lib/constants';
import { convertToPacificTime, getPacificTimeNow } from '@/src/lib/timezone';
import { formatTime } from '@/src/lib/utils';
import { useAppSelector, RootState, useAppDispatch } from '@/src/store';
import type { FarmerMarket, Location } from '@/src/types/FarmerMarket';

import { fetchFarmerMarkets } from './slices/farmerMarketSlice';

export default function FarmerMarket() {
  const { farmerMarkets, loading } = useAppSelector((state: RootState) => state.farmerMarket);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [_monthsToShow, setMonthsToShow] = useState(1);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement | null>(null);

  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(fetchFarmerMarkets());
  }, [dispatch]);

  const handleLocationClick = (location: Location) => {
    const formattedAddress = `${location.street}, ${location.city},${location.state} ${location.zip_code}`;
    const encodedAddress = encodeURIComponent(formattedAddress); // Encode the address for URL
    const googleMapsUrl = `${ENDPOINTS.GOOGLE_MAPS_ADDRESS_URL}${encodedAddress}`;
    window.open(googleMapsUrl, '_blank');
  };

  const loadMore = useCallback(() => {
    if (!isLoadingMore) {
      setIsLoadingMore(true);
      setTimeout(() => {
        setMonthsToShow((prev: number) => prev + 1);
        setIsLoadingMore(false);
      }, 500);
    }
  }, [isLoadingMore]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && !isLoadingMore) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: '200px',
        threshold: 0.1,
      }
    );

    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loadMore, isLoadingMore]);

  useEffect(() => {
    const loadingElement = loadingRef.current;
    const observer = observerRef.current;

    if (loadingElement && observer) {
      observer.observe(loadingElement);
    }

    return () => {
      if (loadingElement && observer) {
        observer.unobserve(loadingElement);
      }
    };
  }, []);

  const getMonthlySchedule = () => {
    const schedule = [];
    const today = convertToPacificTime(startOfToday());

    // Find the latest end date among all markets
    const latestEndDate = farmerMarkets.reduce(
      (latest, market) => {
        const endDate = parseISO(market.season_end_date);
        return isAfter(endDate, latest) ? endDate : latest;
      },
      parseISO(farmerMarkets[0]?.season_end_date || '')
    );

    // Generate days from today until the latest end date
    const daysInRange = eachDayOfInterval({
      start: today,
      end: latestEndDate,
    });

    for (const currentDate of daysInRange) {
      const isToday = format(currentDate, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');

      const activeMarkets = farmerMarkets.filter(market => {
        const startDate = parseISO(market.season_start_date);
        const endDate = parseISO(market.season_end_date);
        const dayOfWeek = format(currentDate, 'EEE').toLowerCase();

        const isWithinSeason = !isBefore(currentDate, startDate) && !isAfter(currentDate, endDate);

        // Filter schedules based on day and if it's today, check if time hasn't passed
        const activeSchedules = market.schedules.filter(schedule => {
          if (schedule.day !== dayOfWeek || schedule.is_closed) return false;

          // For today, filter out passed time slots
          if (isToday) {
            const [hours, minutes] = schedule.start_time.split(':').map(Number);
            const scheduleTime = getPacificTimeNow();
            scheduleTime.setHours(hours, minutes, 0, 0);
            return !isBefore(scheduleTime, today);
          }

          return true;
        });

        return isWithinSeason && activeSchedules.length > 0;
      });

      if (activeMarkets.length > 0) {
        schedule.push({
          date: currentDate,
          markets: activeMarkets.map(market => {
            const isToday = format(currentDate, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');
            const now = getPacificTimeNow();

            return {
              ...market,
              schedules: market.schedules.filter(schedule => {
                if (schedule.day !== format(currentDate, 'EEE').toLowerCase() || schedule.is_closed)
                  return false;

                // For today, filter out passed time slots
                if (isToday) {
                  const [hours, minutes] = schedule.start_time.split(':').map(Number);
                  const scheduleTime = getPacificTimeNow();
                  scheduleTime.setHours(hours, minutes, 0, 0);
                  return !isBefore(scheduleTime, now);
                }

                return true;
              }),
            };
          }),
        });
      }
    }

    return schedule;
  };

  if (loading && farmerMarkets.length === 0) return <Spinner />;

  const monthlySchedule = getMonthlySchedule();

  // If no farmer markets are available at all
  if (monthlySchedule.length === 0 && !isLoadingMore && farmerMarkets.length > 0) {
    return (
      <div className="min-h-0 max-w-5xl mb-6 m-auto">
        <Card className="p-4 text-center">
          <CardContent>
            <XCircle className="mx-auto mb-2 h-8 w-8 text-mulberry" />
            <h3 className="text-lg font-semibold">No Farmer Markets Available</h3>
            <p className="text-gray-600">
              There are currently no upcoming farmer markets scheduled.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-0 max-w-5xl m-auto">
      {monthlySchedule.map(({ date, markets }) => (
        <div key={date.toISOString()}>
          {/* Date Header */}
          <div className="relative flex items-center my-6">
            <div className="flex-grow border-t border-gray-300" />
            <div className="mx-4 bg-white px-4 py-1 text-gray-800 font-semibold rounded-full shadow-md border border-gray-200">
              {format(date, 'EEEE, MMMM d, yyyy')}
            </div>
            <div className="flex-grow border-t border-gray-300" />
          </div>

          {/* Market List */}
          <div className="space-y-4">
            {markets.map(market => {
              const currentDaySchedules = market.schedules.filter(
                schedule => schedule.day === format(date, 'EEE').toLowerCase()
              );

              return (
                <Card key={market.id} className="mb-4 p-4">
                  <CardHeader className="p-0">
                    <div className="flex  justify-between items-center">
                      <h3 className="text-lg mb-2 capitalize font-semibold">{market.name}</h3>
                      <button
                        onClick={() => handleLocationClick(market.location as Location)}
                        className="px-3 py-1 hidden md:flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                      >
                        <MapPinned className="h-3 w-3" />
                        <span>Open Map</span>
                      </button>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    {currentDaySchedules.map((schedule, i) => (
                      <div key={i} className="">
                        <div className="flex flex-col gap-1">
                          {/* Time Section */}
                          <div className="flex items-center gap-2">
                            <ClockIcon className="h-4 w-4" />
                            <span className="font-medium text-sm whitespace-nowrap">
                              {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                            </span>
                          </div>

                          {/* Location and Navigation Section */}

                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-500 flex-shrink-0" />
                            <span className="text-gray-600 text-sm">
                              {market.location.street}, {market.location.city},{' '}
                              {market.location.state} {market.location.zip_code}
                            </span>
                          </div>
                          <button
                            onClick={() => handleLocationClick(market.location as Location)}
                            className="px-3 py-1 md:hidden mt-2 flex text-sm bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white rounded-full items-center justify-center gap-1"
                          >
                            <MapPinned className="h-3 w-3" />
                            <span>Open Map</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      ))}

      {/* Empty State */}
      {monthlySchedule.length === 0 && !isLoadingMore && farmerMarkets.length > 0 && (
        <div className="text-center p-8 bg-gray-50 rounded-lg">
          <p className="text-gray-500 font-medium">No farmer markets scheduled for this period.</p>
        </div>
      )}
    </div>
  );
}
