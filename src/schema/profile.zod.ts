import { z } from 'zod';

// Define validation schema for form data
export const updateProfileSchema = z.object({
  userId: z.string().min(1, 'User ID is required'), // User ID must be provided and not empty
  phone: z.string().optional().nullable(), // Phone number is optional
  firstName: z.string().min(2, 'First name must be at least 2 characters').optional().nullable(), // First name must be at least 2 chars if provided
  lastName: z.string().min(2, 'Last name must be at least 2 characters').optional().nullable(), // Last name must be at least 2 chars if provided
  profileImage: z.any().optional().nullable(), // Profile image is optional and can be any type (File object)
  marketingConsent: z.string().optional().nullable(), // Marketing consent is optional and stored as string
});

// Define customer validation schema
export const customerSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(2, 'First name must be at least 2 characters').trim(),
  lastName: z.string().min(2, 'Last name must be at least 2 characters').trim(),
  phone: z.string().min(10, 'Phone number must be at least 10 digits').optional(),
  addressLine1: z.string().min(5, 'Address is required').optional(),
  city: z.string().min(2, 'City is required').optional(),
  state: z.string().min(2, 'State is required').optional(),
  zipCode: z.string().min(5, 'Valid ZIP code is required').optional(),
});
