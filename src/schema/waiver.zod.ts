import { z } from 'zod';

// Define the waiver validation schema
const childSchema = z.object({
  first_name: z.string().min(2, 'First name must be at least 2 characters'),
  last_name: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email format').optional(),
});

// Export the schema for use in the application for waiver validation
export const waiverSchema = z.object({
  age_group: z.enum(['adult', 'child', 'senior']),
  first_name: z.string().min(2, 'First name must be at least 2 characters'),
  last_name: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  mailingList: z.boolean().optional().default(false),
  agreementAccepted: z.boolean().refine(val => val === true, {
    message: 'You must accept the waiver agreement',
  }),
  children: z.array(childSchema).optional(),
  waiver_version: z.string(),
  ip_address: z.string(),
  device_info: z.string(),
});
