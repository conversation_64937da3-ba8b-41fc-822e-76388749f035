import { z } from 'zod';

// Import the enum *value* object for runtime use in Zod schema
import { DiscountTargetTypeValue, SUBSCRIPTION_ORDER_TYPES } from '@/src/lib/constants';

import { isGiftHomeDelivery } from '../lib/utils';

// Define subscription request schema with Zod
export const subscriptionRequestSchema = z
  .object({
    variationId: z.string().min(1, 'Variation ID is required'),
    addressLine2: z.string().optional(),
    deliveryDate: z.string().optional(),
    zoneId: z.string().min(1, 'Zone ID is required'),
    email: z.string().email('Valid email is required'),
    phone: z.string().min(10, 'Valid phone number is required').optional(),
    addressLine1: z.string().min(5, 'Valid address is required'),
    city: z.string().min(2, 'City is required'),
    state: z.string().min(2, 'State is required'),
    zipCode: z.string().min(5, 'Valid ZIP code is required'),
    sourceId: z.string().min(1, 'Payment source ID is required'),
    customerId: z.string().min(1, 'Customer ID is required'),
    firstName: z.string().min(2, 'First name is required'),
    lastName: z.string().min(2, 'Last name is required'),
    deliveryNotes: z.string().optional(),
    orderFor: z.enum(Object.values(SUBSCRIPTION_ORDER_TYPES) as [string, ...string[]]),
    recipientFirstName: z.string().optional(),
    recipientLastName: z.string().optional(),
    recipientEmail: z.string().optional(),
    recipientPhone: z.string().optional(),
    recipientGiftMessage: z
      .string()
      .max(255, 'Gift message cannot exceed 255 characters')
      .optional(),
    recipientSquareCustomerId: z.string().optional(),
    userId: z.string().optional(),
    address: z.string().optional(),
    deliveryWindow: z.string().optional(),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    discountId: z.string().optional(),
    // Use z.enum with the runtime value object
    targetType: z
      .enum([DiscountTargetTypeValue.PRODUCT, DiscountTargetTypeValue.DELIVERY_FEE])
      .optional(),
    referralCode: z.string().optional(),
    referralPolicyId: z.string().optional(),
  })
  .refine(
    data => {
      // If orderFor is not SELF, validate recipient data
      if (isGiftHomeDelivery(data.orderFor)) {
        return (
          !!data.recipientFirstName &&
          !!data.recipientLastName &&
          !!data.recipientEmail &&
          !!data.recipientPhone &&
          !!data.recipientSquareCustomerId
        );
      }
      return true;
    },
    {
      message: 'Recipient information is required for gift orders',
      path: ['recipientFirstName'],
    }
  );

// Define validation schema for pause subscription request
export const pauseSubscriptionSchema = z.object({
  subscriptionId: z.string().min(1, 'Subscription ID is required'),
  firebase_user_id: z.string().min(1, 'User ID is required'),
});

// Define validation schema for update card request
export const updateCardSchema = z.object({
  subscriptionId: z.string().min(1, 'Subscription ID is required').trim(),
  sourceId: z.string().min(1, 'Source ID is required').trim(),
  firebase_user_id: z.string().min(1, 'User ID is required').trim(),
});
