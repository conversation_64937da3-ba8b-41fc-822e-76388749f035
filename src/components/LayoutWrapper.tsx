'use client';

import { ArrowUpRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

import Footer from '@/src/components/footer';
import Spinner from '@/src/components/spinner';
import { selectIsAuthenticated } from '@/src/features/auth/slices/authenticationSlice';
import { ROUTES } from '@/src/lib/constants';
import { RootState, useAppSelector } from '@/src/store';

import OFFERICON from '../../public/advertisement.png';
import INVOICEICON from '../../public/invoice.png';
import SUBSCRIPTIONICON from '../../public/premium.png';
import TICKETICON from '../../public/ticket.png';
import VMLOGO from '../../public/vmicon.png';

import { Button } from './ui/button';
import { Card } from './ui/card';

interface LayoutWrapperProps {
  children: React.ReactNode;
  sidebar: React.ReactNode;
  sidebarHeader: React.ReactNode;
}

export default function LayoutWrapper({ children, sidebar, sidebarHeader }: LayoutWrapperProps) {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector((state: RootState) => state.authentication.loading);
  const pathname = usePathname();
  const router = useRouter();

  // Show loading spinner while authentication state is being determined
  if (isLoading && pathname === '/') {
    return (
      <div className="flex flex-col min-h-screen">
        <div className="min-h-screen flex items-center justify-center">
          <Spinner />
        </div>
      </div>
    );
  }

  // Show simplified layout for unauthenticated users at root path
  if (!isAuthenticated && pathname === '/') {
    return (
      <div className="flex flex-col min-h-screen">
        <main className="flex-grow">
          {/* Header with Logo */}
          <div className="flex justify-center py-12 bg-white">
            <div className="w-32 h-32 relative">
              <Image
                src={VMLOGO}
                alt="Very Mulberry Logo"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
          {/* Main Content */}
          <div className="bg-mulberryLightest py-12 px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center max-md:text-2xl">
                One place for everything Very Mulberry.
              </h1>
              <p className="text-lg mb-8">
                Create a profile or Sign in to My Very Mulberry to get a personalized view of all
                Very Mulberry has to offer.
              </p>

              <div className="flex justify-center mb-12">
                <Button
                  onClick={() => router.push(ROUTES.LOGIN)}
                  className="bg-mulberry hover:bg-mulberryHover text-white"
                >
                  Sign In
                </Button>
              </div>

              {/* Feature Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="p-6 flex flex-col items-center">
                  <div className="h-16 w-16 text-mulberry mb-4">
                    <Image
                      src={TICKETICON}
                      alt="Tickets Icon"
                      width={64}
                      height={64}
                      className="object-contain"
                    />
                  </div>
                  <h3 className="font-medium text-center">
                    Book Your
                    <br />
                    U-Pick Tickets
                  </h3>
                </Card>

                <Card className="p-6 flex flex-col items-center">
                  <div className="h-16 w-16 text-mulberry mb-4">
                    <Image
                      src={SUBSCRIPTIONICON}
                      alt="Subscription Icon"
                      width={64}
                      height={64}
                      className="object-contain"
                    />
                  </div>
                  <h3 className="font-medium text-center">
                    Manage Your
                    <br />
                    Mulberry Subscription
                  </h3>
                </Card>

                <Card className="p-6 flex flex-col items-center">
                  <div className="h-16 w-16 text-mulberry mb-4">
                    <Image
                      src={INVOICEICON}
                      alt="Document Icon"
                      width={64}
                      height={64}
                      className="object-contain"
                    />
                  </div>
                  <h3 className="font-medium text-center">
                    Access Your
                    <br />
                    Tickets
                  </h3>
                </Card>

                <Card className="p-6 flex flex-col items-center">
                  <div className="h-16 w-16 text-mulberry mb-4">
                    <Image
                      src={OFFERICON}
                      alt="Megaphone Icon"
                      width={64}
                      height={64}
                      className="object-contain"
                    />
                  </div>
                  <h3 className="font-medium text-center">
                    Exclusive Offers
                    <br />& Farm News
                  </h3>
                </Card>
              </div>
            </div>
          </div>
          {/* FAQ */}
          <div className="bg-white flex flex-col justify-center py-[70px]">
            <h2 className="text-[46px] text-center max-md:text-3xl md:text-4xl text-mulberry font-bold max-md:mb-[20px] mb-[60px]">
              Frequently Asked Questions
            </h2>

            <div className="grid grid-cols-1 max-md:gap-0  md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              {/* Row 1, Col 1 */}
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-bold text-[16px] text-black mb-2">What is My Very Mulberry?</h3>
                <p className="text-[16px]text-black">
                  My Very Mulberry is your personalised account portal for everything related to
                  your experiences and purchases at Habitera Farms. It&apos;s designed to give you
                  easy, secure access to manage your U-Pick tickets, subscriptions, orders, and
                  preferences.
                </p>
              </div>

              {/* Row 1, Col 2 */}
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-bold text-[16px] text-black mb-2">
                  What information can I see in My Very Mulberry?
                </h3>
                <p className="text-[16px] text-black">
                  Your My Very Mulberry account lets you view your current and past U-Pick tickets,
                  upcoming events you&apos;ve booked, active subscriptions, previous orders, payment
                  details, and your account preferences all in one convenient location.
                </p>
              </div>

              {/* Row 2, Col 1 */}
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-bold text-[16px] text-black mb-2">
                  How is My Very Mulberry different from the main Very Mulberry website?
                </h3>
                <p className="text-[16px] text-black">
                  The Very Mulberry website is our public-facing site where you can learn about our
                  farm, know more about mulberries, and book events. My Very Mulberry, on the other
                  hand, is your personal dashboard—allowing you to manage your subscriptions, track
                  your orders, view your tickets, and easily access member-exclusive content and
                  benefits.
                </p>
              </div>

              {/* Row 2, Col 2 */}
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-bold text-[16px] text-black mb-2">
                  What are the benefits of using My Very Mulberry?
                </h3>
                <p className="text-[16px] text-black">By using My Very Mulberry, you gain:</p>
                <ul className="list-disc pl-5 text-[16px] text-black mt-2">
                  <li>
                    Convenience: Quickly access and manage your bookings, orders, and subscriptions
                  </li>
                  <li className="mt-1">
                    Enhanced Experience: Enjoy streamlined farm visits with digital waivers and
                    faster check-ins
                  </li>
                </ul>
              </div>

              {/* Row 3, Col 1 */}
              <div className="bg-white p-4 rounded-lg flex flex-col">
                <div className="flex-grow">
                  <h3 className="font-bold text-[16px] text-black mb-2">
                    How do I create a My Very Mulberry account?
                  </h3>
                  <p className="text-[16px] text-black mb-4">
                    You can create your My Very Mulberry account directly through the sign-in with
                    google on our login page, or you can enter your email with magic link.
                  </p>
                </div>
                <Link
                  href={`${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}/faqs}`}
                  className="text-mulberry hidden md:flex items-center gap-1 hover:underline font-bold text-[16px] self-start"
                >
                  See All FAQ
                  <ArrowUpRight />
                </Link>
              </div>

              {/* Row 3, Col 2 */}
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-bold text-[16px] text-black mb-2">
                  What can I do with My Very Mulberry?
                </h3>
                <p className="text-[16px] text-black">With My Very Mulberry, you can:</p>
                <ul className="list-disc pl-5 text-[16px] text-black mt-2">
                  <li>Book U-Pick ticket.</li>
                  <li>View and manage your event tickets and reservations.</li>
                  <li>Track your delivery subscriptions and order history.</li>
                  <li>Update your billing and contact information.</li>
                  <li>Access exclusive offers and member-only events.</li>
                  <li>Sign your waivers digitally for seamless farm visits.</li>
                </ul>
                <Link
                  href={`${process.env.NEXT_PUBLIC_WEBSITE_BASE_URL}/faqs}`}
                  className="text-mulberry  md:hidden flex mt-4 items-center gap-1 hover:underline font-bold text-[16px] self-start"
                >
                  See All FAQ
                  <ArrowUpRight />
                </Link>
              </div>
            </div>
          </div>
        </main>

        {/* Footer */}
        <Footer isAuthenticated={isAuthenticated} />
      </div>
    );
  }

  // Show full layout for authenticated users or other routes
  return (
    <div className={`${pathname === '/auth/login' ? 'px-0' : 'px-2 xs:px-4 sm:px-6 md:px-10'} `}>
      {sidebarHeader}
      <div className="flex flex-1 gap-4 md:gap-8">
        {sidebar}
        <main className={`${pathname === '/auth/login' ? 'mt-0' : 'mt-8'} flex-1`}>{children}</main>
      </div>
    </div>
  );
}
