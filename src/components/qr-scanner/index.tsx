'use client';

import { Scanner, IDetectedBarcode } from '@yudiel/react-qr-scanner';
import { Camera, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface QRScannerProps {
  onScanSuccess: (decodedText: string) => void;
  onClose: () => void;
  isProcessing: boolean;
}

const QRScanner = ({ onScanSuccess, onClose, isProcessing }: QRScannerProps) => {
  /**
   * Start Initials
   */
  const [isScanning, setIsScanning] = useState(true);
  /**
   * End Initials
   */

  /**
   * Start Custom Methods
   */
  const handleResult = (detectedCodes: IDetectedBarcode[]) => {
    if (isProcessing) return;
    if (detectedCodes.length > 0 && detectedCodes[0].rawValue) {
      setIsScanning(false);
      onScanSuccess(detectedCodes[0].rawValue);
    }
  };

  const handleError = (error: any) => {
    console.error(error);
    toast.error('Failed to access camera. Please check permissions and try again.');
  };
  /**
   * End Custom Methods
   */

  return (
    <div className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-auto overflow-hidden">
      {/* Header */}
      <div className="bg-mulberry text-white px-6 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Camera className="w-6 h-6" />
          <h3 className="text-lg font-semibold">QR Code Scanner</h3>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-white/20 rounded-full transition-colors duration-200"
          aria-label="Close scanner"
        >
          <X className="w-6 h-6" />
        </button>
      </div>

      {/* Scanner Container */}
      <div className="p-6">
        <div className="relative rounded-lg overflow-hidden bg-black/5">
          {isScanning && (
            <Scanner
              onScan={handleResult}
              onError={handleError}
              constraints={{
                facingMode: 'environment',
              }}
            />
          )}
        </div>

        {/* Scanning Tips */}
        <div className="mt-4 text-sm text-gray-600">
          <h4 className="font-medium mb-2">Scanning Tips:</h4>
          <ul className="list-disc list-inside space-y-1">
            <li>Hold the QR code steady in the frame</li>
            <li>Ensure adequate lighting</li>
            <li>Keep the camera lens clean</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default QRScanner;
