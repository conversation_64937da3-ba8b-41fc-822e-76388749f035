import { isValidPhoneNumber, parsePhoneNumberWithError } from 'libphonenumber-js';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

import { cn } from '@/src/lib/utils';

// Define a custom interface without extending InputHTMLAttributes
interface PhoneInputProps {
  onChange: (value: string) => void;
  value?: string;
  error?: boolean;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  name?: string;
  id?: string;
  placeholder?: string;
  country?: string;
  disableDropdown?: boolean; // Add this prop
}

// Create a component with forwardRef that safely handles the ref
export const CustomPhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  (
    {
      className,
      error,
      onChange,
      value,
      disabled,
      required,
      name,
      id,
      placeholder,
      country = 'us', // Default to 'us'
      disableDropdown = false,
      ...props
    },
    ref
  ) => {
    // Create a local ref to the input element
    const inputRef = useRef<HTMLInputElement | null>(null);

    // Forward the ref using useImperativeHandle
    useImperativeHandle(ref, () => inputRef.current as HTMLInputElement, []);

    // Remove the '+' prefix if present for the PhoneInput component
    const phoneValue = value?.startsWith('+') ? value.slice(1) : value;

    const handleChange = (val: string) => {
      const e164Value = `+${val}`;

      try {
        // Validate the phone number
        if (isValidPhoneNumber(e164Value)) {
          const phoneNumber = parsePhoneNumberWithError(e164Value);
          if (phoneNumber?.isValid()) {
            onChange(e164Value);
            return;
          }
        }
        // Still update the value even if invalid to allow for partial input
        onChange(e164Value);
      } catch (_error) {
        // Handle parsing errors by still updating the value
        onChange(e164Value);
      }
    };

    return (
      <PhoneInput
        country={country}
        countryCodeEditable={false}
        inputClass={cn(
          'flex h-9 w-full rounded-md border border-input px-3 py-1 text-sm shadow-sm transition-colors',
          'focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring',
          'disabled:cursor-not-allowed disabled:opacity-50',
          error && 'border-destructive',
          className
        )}
        enableSearch
        disableSearchIcon
        searchPlaceholder="Search country..."
        disableDropdown={disableDropdown}
        onlyCountries={['us']}
        inputProps={{
          ref: (element: HTMLInputElement) => {
            inputRef.current = element;
            // Handle the case where ref is a function
            if (typeof ref === 'function') {
              ref(element);
            }
          },
          disabled,
          required,
          name,
          id,
          placeholder,
          ...props,
        }}
        value={phoneValue || ''}
        onChange={handleChange}
        containerClass="w-full"
        dropdownClass="!bg-popover !border-border"
        buttonClass={cn(
          'border !border-input !bg-background hover:!bg-accent',
          error && '!border-destructive'
        )}
        searchClass="!bg-background !text-foreground"
        autoFormat
        preferredCountries={['us']}
      />
    );
  }
);

// Add display name for better debugging
CustomPhoneInput.displayName = 'CustomPhoneInput';
