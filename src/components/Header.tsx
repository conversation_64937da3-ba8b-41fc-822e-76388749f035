'use client';
import { <PERSON>u, LogOut, CircleUserRound } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import {
  logoutUser,
  selectIsAuthenticated,
  selectUser,
} from '@/src/features/auth/slices/authenticationSlice';
import { useAppDispatch, useAppSelector } from '@/src/store';

import { navigation, ROUTES } from '../lib/constants';
import { cn } from '../lib/utils';

import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Button } from './ui/button';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from './ui/sheet';

function isActivePath(href: string, pathname: string): boolean {
  try {
    if (process.env.NEXT_PUBLIC_WEBSITE_BASE_URL === href) {
      return false;
    }
    const hrefPath = new URL(href).pathname;

    // Normalize paths (remove trailing slashes unless it's just "/")
    const normalize = (path: string) => (path === '/' ? path : path.replace(/\/+$/, ''));

    const current = normalize(pathname);
    const target = normalize(hrefPath);

    if (target === '/') {
      // Only return true for exact home match
      return current === '/';
    }

    return current === target || current.startsWith(`${target}/`);
  } catch (_error) {
    console.error('Invalid href URL:', href);
    return false;
  }
}

function MainNav({ className, ...props }: React.HTMLAttributes<HTMLElement>) {
  const pathname = usePathname();
  const user = useAppSelector(selectUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  return (
    <nav className={cn('flex justify-end items-center ', className)} {...props}>
      {navigation.map(item => (
        <Link
          key={item.name}
          href={item.href}
          className={cn(
            'text-[16px] pb-[5px] font-bold whitespace-nowrap ml-[19.3px] font-georgia transition-colors hover:text-mulberry',
            isActivePath(item.href, pathname) ? 'text-mulberry' : 'text-black'
          )}
        >
          {item.name}
        </Link>
      ))}
      <Link
        className={cn(
          'text-[16px] font-semibold font-georgia transition-colors hover:text-mulberry flex items-center gap-2',
          pathname === '/auth/login' ? 'text-mulberry' : 'text-black'
        )}
        href={isAuthenticated ? ROUTES.PROFILE : ROUTES.LOGIN}
      >
        {isAuthenticated && user ? (
          <>
            <span className="ml-[18.2px] pb-[5px] -mr-[2px]">{user.first_name}</span>
            {user.profile_url ? (
              <Avatar className="h-8 w-8 mb-[2px]">
                <AvatarImage src={user.profile_url} alt={user.first_name} />
                <AvatarFallback className="bg-mulberry text-white text-xs font-semibold">
                  {`${user?.first_name?.[0]?.toUpperCase() || ''}${user?.last_name?.[0]?.toUpperCase() || ''}`}
                </AvatarFallback>
              </Avatar>
            ) : (
              <CircleUserRound className="h-6 w-6 text-mulberry" />
            )}
          </>
        ) : (
          <>
            <span className="whitespace-nowrap ml-[18.2px] pb-[5px] -mr-[2px]">Sign In</span>
            <Image
              width={24}
              height={24}
              src="/circle-user-round.svg"
              alt="User Icon"
              className="pb-[2px]"
            ></Image>
          </>
        )}
      </Link>
    </nav>
  );
}

function MobileNav({ className, onLogout }: { className?: string; onLogout: () => void }) {
  const pathname = usePathname();
  const [open, setOpen] = React.useState(false);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <Link href="/" onClick={() => setOpen(false)}>
        <Image
          className=" md:hidden"
          width={136}
          height={100}
          src="/Very-Mulberry-Logo.png"
          alt="Very Mulberry Logo"
        />
      </Link>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            'mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 lg:hidden',
            className
          )}
        >
          <Menu className="h-6 w-6" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="pr-0">
        <SheetHeader>
          <SheetTitle>
            <Link href="/" onClick={() => setOpen(false)}>
              <Image
                width={100}
                height={100}
                src="/Very-Mulberry-Logo.png"
                alt="Very Mulberry Logo"
              />
            </Link>
          </SheetTitle>
        </SheetHeader>
        <div className="flex flex-col mt-3 space-y-3 py-4">
          {navigation.map(item => (
            <Link
              key={item.name}
              href={item.href}
              onClick={() => setOpen(false)}
              className={cn(
                'text-[16px] font-semibold font-georgia transition-colors hover:text-mulberry',
                pathname === item.href ? 'text-mulberry' : 'text-black'
              )}
            >
              {item.name}
            </Link>
          ))}
          {isAuthenticated && (
            <button
              onClick={() => {
                onLogout();
                setOpen(false);
              }}
              className="flex items-center gap-2 text-[16px] font-semibold font-georgia text-red-600 hover:text-red-700"
            >
              <LogOut size={18} />
              Logout
            </button>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}

export default function Header() {
  const dispatch = useAppDispatch();

  const handleLogout = async () => {
    try {
      await dispatch(logoutUser());
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="sticky top-0  z-[50] md:z-[100] w-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* This is Main header desktop */}
      <div className=" flex h-[61.99px] max-md:h-[80px] max-md:w-[100%] max-md:p-[22px]  w-[80%] max-w-[1080px]  mx-auto items-center justify-between">
        <MobileNav className="md:hidden" onLogout={handleLogout} />
        <div className="hidden md:flex items-center">
          <Link href="/" className="flex items-center space-x-2">
            <span className="hidden font-bold  sm:inline-block">
              <Image
                className="ml-1"
                width={110}
                height={100}
                src="/Very-Mulberry-Logo.png"
                alt="Very Mulberry Logo"
              />
            </span>
          </Link>
        </div>
        <div className="hidden md:flex flex-1 justify-end">
          <MainNav />
        </div>
      </div>
    </header>
  );
}
