'use client';

import { Upload } from 'lucide-react';
import React, { useRef, useState, useEffect } from 'react';
import { toast } from 'sonner';

import Modal from '@/src/components/modal';
import { Avatar, AvatarFallback, AvatarImage } from '@/src/components/ui/avatar';
import { Button } from '@/src/components/ui/button';
import {
  updateUserProfile,
  setTempProfileImage,
} from '@/src/features/auth/slices/authenticationSlice';
import { useAppDispatch, useAppSelector, RootState } from '@/src/store';

interface ProfileImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImageSelect?: (file: File | null, previewUrl: string | null) => void;
  currentPreview?: string | null;
}

export default function ProfileImageModal({
  isOpen,
  onClose,
  onImageSelect,
  currentPreview,
}: ProfileImageModalProps) {
  /**
   * Start Initials
   */
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state: RootState) => state.authentication);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  /**
   * End Initials
   */

  /**
   * Start Lifecycle Methods
   */
  // Initialize preview with current preview when modal opens
  useEffect(() => {
    if (isOpen) {
      setPreviewUrl(currentPreview || null);
    }
  }, [isOpen, currentPreview]);
  /**
   * End Lifecycle Methods
   */

  /**
   * Start Custom Methods
   */
  // Handle file change and trigger profile update
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit');
        return;
      }

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = async () => {
        const newPreviewUrl = reader.result as string;
        setPreviewUrl(newPreviewUrl);

        // Update Redux state with the preview URL
        dispatch(setTempProfileImage(newPreviewUrl));

        // Call onImageSelect if provided
        if (onImageSelect) {
          onImageSelect(file, newPreviewUrl);
        }

        // Automatically update profile if user exists
        if (user?.id) {
          try {
            setIsSubmitting(true);
            await dispatch(
              updateUserProfile({
                userId: user.id,
                profileImage: file,
              })
            ).unwrap();
            toast.success('Profile picture updated successfully');
            onClose(); // Close modal after successful update
          } catch (_error) {
            toast.error('Failed to update profile picture');
          } finally {
            setIsSubmitting(false);
          }
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  /**
   * End Custom Methods
   */

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Edit Profile Picture"
      description="Upload a new profile picture or remove the current one"
    >
      <div className="flex flex-col items-center space-y-6 py-4">
        <Avatar className="h-32 w-32">
          <AvatarImage src={previewUrl || user?.profile_url} alt="User" />
          <AvatarFallback className="bg-mulberry text-white text-3xl font-semibold">
            {`${user?.first_name?.[0]?.toUpperCase() || ''}${user?.last_name?.[0]?.toUpperCase() || ''}`}
          </AvatarFallback>
        </Avatar>

        <p className="text-sm text-muted-foreground">Upload your image (max size 10MB)</p>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          className="hidden"
        />

        <div className="flex gap-4 w-full justify-center">
          <Button
            onClick={handleUploadClick}
            className="flex items-center gap-2 bg-mulberry hover:bg-mulberryHover"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Updating...
              </div>
            ) : (
              <>
                <Upload size={16} />
                Change
              </>
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
