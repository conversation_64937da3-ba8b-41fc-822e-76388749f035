'use client';
import { AlertCircleIcon } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/src/components/ui/alert';

interface ErrorAlertProps {
  error: string | null;
  title?: string;
}

export default function ErrorAlert({ error, title = 'Error' }: ErrorAlertProps) {
  return (
    <Alert variant="destructive">
      <AlertCircleIcon className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>{error}</AlertDescription>
    </Alert>
  );
}
