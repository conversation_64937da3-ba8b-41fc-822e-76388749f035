'use client';

import { useEffect } from 'react'; // Import useEffect
import { Provider } from 'react-redux';

import AuthStateListener from '@/src/features/auth/components/AuthStateListener';
import { initializeFirebaseAppCheck } from '@/src/lib/firebaseConfig'; // Import the initializer
import store from '@/src/store';

const Providers = ({ children }: { children: React.ReactNode }) => {
  // Initialize App Check on component mount (client-side only)
  useEffect(() => {
    // This effect runs after the initial render.
    // API calls triggered immediately on mount by child components might run before this completes.
    initializeFirebaseAppCheck();
  }, []); // Empty dependency array means this runs once after the initial mount

  return (
    <Provider store={store}>
      <AuthStateListener>{children}</AuthStateListener>
    </Provider>
  );
};

export default Providers;
