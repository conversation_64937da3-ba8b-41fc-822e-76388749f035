'use client';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

import { logoutUser, selectIsAuthenticated } from '../features/auth/slices/authenticationSlice';
import { SIDEBAR_ROUTES, sidebarItems } from '../lib/constants';
import { useAppDispatch, useAppSelector } from '../store';

import { Button } from './ui/button';

export default function SidebarHeader() {
  /**
   * Start Initials
   */
  const pathname = usePathname();
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [isNavOpen, setIsNavOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await dispatch(logoutUser());
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Replace the exact path matching with a more flexible approach
  const shouldShowSidebar = SIDEBAR_ROUTES.some(
    route => pathname === route || pathname.startsWith(`${route}/`)
  );

  if (!shouldShowSidebar || !isAuthenticated) {
    return null;
  }

  // If pathname is not in the sidebar items, default to home
  const isPathInSidebar = sidebarItems.some(item => item.path === pathname);
  const activePath = isPathInSidebar ? pathname : '/';
  /**
   * End Initials
   */

  return (
    <div className="w-full">
      <div className="flex justify-between text-white py-6 border-b-2">
        <h3
          className="font-semibold md:text-3xl text-xl text-mulberry flex items-center cursor-pointer md:cursor-default"
          onClick={() => setIsNavOpen(!isNavOpen)}
        >
          My Very Mulberry
          <span className="lg:hidden">
            {isNavOpen ? (
              <ChevronUp className="h-5 w-5 text-black" />
            ) : (
              <ChevronDown className="h-5 w-5 text-black" />
            )}
          </span>
        </h3>

        {isAuthenticated && (
          <div className="flex items-center">
            <Button
              onClick={handleLogout}
              className="bg-mulberry hover:bg-mulberryHover active:bg-mulberryActive text-white text-sm"
            >
              Sign Out
            </Button>
          </div>
        )}
      </div>

      {/* Mobile navigation - only visible on small screens when open */}
      {isNavOpen && (
        <div className="border-b-2 py-2 lg:hidden">
          <nav>
            <ul className="space-y-1">
              {sidebarItems.map(item => (
                <li key={item.path}>
                  <Link
                    href={item.path}
                    className={`block py-2 px-3 rounded-md transition-colors ${
                      activePath === item.path
                        ? 'text-mulberry font-medium'
                        : 'text-black hover:text-mulberry'
                    }`}
                    onClick={() => setIsNavOpen(false)}
                  >
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      )}
    </div>
  );
}
