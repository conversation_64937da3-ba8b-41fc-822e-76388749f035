'use client';
import { Edit2 } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/src/components/ui/avatar';

import { SIDEBAR_ROUTES, sidebarItems } from '../lib/constants';
import { cn } from '../lib/utils';
import { useAppSelector, RootState } from '../store';

import ProfileImageModal from './ProfileImageModal';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';

export default function Sidebar() {
  /**
   * Start Initials
   */
  const pathname = usePathname();
  const { user, tempProfileImage } = useAppSelector((state: RootState) => state.authentication);
  const [isProfileImageModalOpen, setIsProfileImageModalOpen] = useState(false);

  // Only show sidebar on specified routes
  const shouldShowSidebar = SIDEBAR_ROUTES.some(
    route => pathname === route || pathname.startsWith(`${route}/`)
  );

  if (!shouldShowSidebar || !user) {
    return null;
  }

  // If pathname is not in the sidebar items, default to home
  const isPathInSidebar = sidebarItems.some(item => item.path === pathname);
  const activePath = pathname.includes('/reservations')
    ? '/reservations'
    : isPathInSidebar
      ? pathname
      : '/';
  /**
   * End Initials
   */

  return (
    <div className="w-1/5 hidden lg:block">
      <div className="p-4">
        {/* Profile section */}
        <div className="flex flex-col items-start mb-6 pt-6 px-3">
          <div className="relative ml-3">
            <Avatar className="h-32 w-32 mb-4">
              <AvatarImage src={tempProfileImage || user?.profile_url} alt="User" />
              <AvatarFallback className="bg-mulberry text-white text-3xl font-semibold">
                {`${user?.first_name?.[0]?.toUpperCase() || ''}${user?.last_name?.[0]?.toUpperCase() || ''}`}
              </AvatarFallback>
            </Avatar>
            {pathname === '/profile' && (
              <button
                onClick={() => setIsProfileImageModalOpen(true)}
                className="absolute bottom-5 right-0 bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors"
                aria-label="Edit profile picture"
              >
                <Edit2 size={16} className="text-mulberry" />
              </button>
            )}
          </div>
          <div className="mt-1 ">
            {user ? (
              <>
                <h3 className="font-semibold text-3xl">{`${user.first_name || ''} ${user.last_name || ''}`}</h3>
                <p className="text-black text-md mt-3 truncate max-w-[150px] sm:max-w-[180px] md:max-w-[200px] lg:max-w-[220px] xl:max-w-[250px]">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span>{user.email || ''}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{user.email || ''}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </p>
              </>
            ) : (
              <>
                <div className="h-9 w-48 bg-gray-200 animate-pulse rounded-md mb-3"></div>
                <div className="h-5 w-36 bg-gray-200 animate-pulse rounded-md"></div>
              </>
            )}
          </div>
        </div>

        {/* Navigation - only visible on medium screens and above */}
        <nav>
          <ul className="space-y-1">
            {sidebarItems.map(item => (
              <li key={item.path}>
                <Link
                  href={item.path}
                  className={cn(
                    'flex items-center py-2 px-3 cursor-pointer rounded-md transition-colors',
                    activePath === item.path
                      ? 'text-mulberry font-medium'
                      : 'text-black hover:text-mulberry'
                  )}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Profile Image Modal */}
      <ProfileImageModal
        isOpen={isProfileImageModalOpen}
        onClose={() => setIsProfileImageModalOpen(false)}
      />
    </div>
  );
}
