import { NextRequest } from 'next/server';

// Import the explicitly initialized adminAppCheck instance
import { adminAppCheck } from '@/src/app/api/_firebaseAdmin';
import { ERROR_MESSAGES } from '@/src/lib/constants';

interface VerifyAppCheckResultError {
  error: string;
  status: 401 | 500;
}
export type VerifyAppCheckResult = VerifyAppCheckResultError | undefined;

/**
 * Verifies the Firebase App Check token from the request headers.
 * @param req - The NextRequest object
 * @param consumeToken - Whether to enable replay protection by consuming the token
 * @returns An object containing an error and status code if verification fails, otherwise undefined
 */
export async function verifyAppCheckToken(
  req: NextRequest,
  consumeToken: boolean = false
): Promise<VerifyAppCheckResult> {
  const appCheckToken = req.headers.get('X-Firebase-AppCheck');
  const pathname = req.nextUrl.pathname;

  if (!appCheckToken) {
    console.warn(`[AppCheck] Path: ${pathname} - Missing X-Firebase-AppCheck header`);
    return { error: ERROR_MESSAGES.APP_CHECK_TOKEN_MISSING, status: 401 };
  }

  try {
    // Use the imported adminAppCheck instance
    const appCheckClaims = await adminAppCheck.verifyToken(
      appCheckToken,
      consumeToken ? { consume: true } : undefined
    );

    // Check if token was already consumed (only relevant if consumeToken is true)
    if (consumeToken && appCheckClaims.alreadyConsumed) {
      console.warn(`[AppCheck] Path: ${pathname} - Token already consumed`);
      return { error: ERROR_MESSAGES.APP_CHECK_TOKEN_INVALID, status: 401 };
    }

    return undefined; // Success
  } catch (error: any) {
    console.warn(`[AppCheck] Path: ${pathname} - Token verification failed:`, error.message);

    // Handle specific Admin SDK initialization errors or App Check service being unavailable
    if (
      error.code === 'app/no-app' || // Default app not found
      error.code === 'app-check/app-check-not-initialized' || // App Check service not available for the app
      error.code === 'app-check/invalid-credential' // Usually means service account issues
    ) {
      console.error(
        `[AppCheck] Critical Firebase Admin SDK or AppCheck service initialization error for ${pathname}: ${error.code} - ${error.message}`
      );
      return { error: ERROR_MESSAGES.INTERNAL_SERVER_ERROR, status: 500 };
    }

    // Handle other App Check specific errors (like invalid token format, expired, etc.)
    if (error.code && error.code.startsWith('app-check/')) {
      console.warn(
        `[AppCheck] App Check token validation error for ${pathname}: ${error.code} - ${error.message}`
      );
      return { error: ERROR_MESSAGES.APP_CHECK_TOKEN_INVALID, status: 401 };
    }

    // Fallback for other unexpected errors
    console.error(`[AppCheck] Unexpected error during token verification for ${pathname}:`, error);
    return { error: ERROR_MESSAGES.INTERNAL_SERVER_ERROR, status: 500 }; // Changed to 500 for unexpected issues
  }
}
