import { DecodedIdToken } from 'firebase-admin/auth';
import { NextRequest } from 'next/server'; // Keep NextRequest for accessing headers/url

import { adminAuth } from '@/src/app/api/_firebaseAdmin'; // Ensure this path is correct
import { ERROR_MESSAGES } from '@/src/lib/constants'; // Import generic error message

// Define return types for clarity
interface VerifyAuthResultSuccess {
  decodedToken: DecodedIdToken;
}
interface VerifyAuthResultError {
  error: string;
  status: 401 | 500; // Use specific statuses
}
export type VerifyAuthResult = VerifyAuthResultSuccess | VerifyAuthResultError;

/**
 * Verifies the Firebase Auth ID token from the request headers.
 * @param req - The NextRequest object.
 * @returns An object containing either the decodedToken or an error with a status code.
 */
export async function verifyAuthToken(req: NextRequest): Promise<VerifyAuthResult> {
  const authHeader = req.headers.get('authorization');
  const pathname = req.nextUrl.pathname; // Get pathname for logging

  // console.info(`[verifyAuthToken] Verifying token for: ${pathname}`); // Keep info log if needed

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.warn(
      `[verifyAuthToken] Failed for ${pathname}: Missing or invalid Authorization header.`
    );
    // Keep this error specific but safe for 401
    return { error: 'Unauthorized: Missing or invalid Authorization header', status: 401 };
  }

  const token = authHeader.split('Bearer ')[1];
  // console.info(`[verifyAuthToken] Found Bearer token for ${pathname}. Attempting verification...`);

  try {
    const decodedToken = await adminAuth.verifyIdToken(token);
    // console.info(
    //   `[verifyAuthToken] Token verified successfully for ${pathname}. UID: ${decodedToken.uid}`
    // );
    // Return only the decodedToken on success, let the caller handle the response
    return { decodedToken };
  } catch (error: any) {
    // Log detailed error server-side
    console.warn(`[verifyAuthToken Error] Error verifying token for ${pathname}:`, error.message);

    // Handle specific Firebase Admin initialization errors -> Return 500 generic error
    if (error.code === 'app/no-app') {
      console.warn(`[verifyAuthToken Error] Firebase Admin SDK not initialized for ${pathname}.`);
      return { error: ERROR_MESSAGES.INTERNAL_SERVER_ERROR, status: 500 };
    }

    // For specific token validation errors (expired, invalid format, etc.) -> Return 401 specific but safe error
    if (
      error.code === 'auth/id-token-expired' ||
      error.code === 'auth/argument-error' ||
      error.code === 'auth/id-token-revoked' // Add other relevant auth error codes if needed
    ) {
      console.warn(`[verifyAuthToken] Token invalid (Code: ${error.code}) for ${pathname}.`);
      // Return a consistent "Invalid token" message for these cases
      return { error: 'Unauthorized: Invalid token', status: 401 };
    }

    // For any other unexpected error during verification -> Return 401 generic error
    console.warn(`[verifyAuthToken] Generic token verification failed for ${pathname}.`);
    return { error: 'Unauthorized: Could not verify token', status: 401 };
  }
}
