import { configureStore } from '@reduxjs/toolkit'; // Fix: Import getDefaultMiddleware
import { useDispatch, useSelector } from 'react-redux';

import authenticationSlice from '@/src/features/auth/slices/authenticationSlice';
import scheduleSlice from '@/src/features/schedule/slices/scheduleSlice';
// Add the anonymousFlowSlice import
import anonymousFlowSlice from '@/src/features/self-checkout/anonymous/slices/anonymousFlowSlice';
import addressValidationReducer from '@/src/features/subscription/slices/addressValidationSlice';
import discountValidationReducer from '@/src/features/subscription/slices/discountValidationSlice';
import userSubscriptionReducer from '@/src/features/subscription/slices/userSubscriptionSlice';
import checkoutReducer from '@/src/features/u-pick/slices/checkoutSlice';
import guestsSlice from '@/src/features/u-pick/slices/guestsSlice';
import userWaiversReducer from '@/src/features/u-pick/slices/userWaiversSlice';
import waiverContentReducer from '@/src/features/u-pick/slices/waiverContentSlice';
import waiversSlice from '@/src/features/u-pick/slices/waiversSlice';

import farmerMarketSlice from './features/farmer-market/slices/farmerMarketSlice';
import giftGiverReducer from './features/gift/slices/giftGiverSlice';
import giftRecipientReducer from './features/gift/slices/giftRecipientSlice';
import referralReducer from './features/referral/slices/referralSlice';
import deliveryZoneReducer from './features/subscription/slices/deliveryZoneSlice';
import subscriptionCustomerReducer from './features/subscription/slices/subscriptionCustomerSlice';
import subscriptionDetailsReducer from './features/subscription/slices/subscriptionDetailsSlice';
import subscriptionPaymentReducer from './features/subscription/slices/subscriptionPaymentSlice';
import subscriptionProductReducer from './features/subscription/slices/subscriptionProductSlice';
import ticketTailorReducer from './features/ticket-tailor/slice';

// Configure the Redux store
const store = configureStore({
  reducer: {
    authentication: authenticationSlice,
    schedule: scheduleSlice,
    addressValidation: addressValidationReducer,
    subscriptionDetails: subscriptionDetailsReducer,
    subscriptionProduct: subscriptionProductReducer,
    guests: guestsSlice,
    waiverContent: waiverContentReducer,
    discountValidation: discountValidationReducer,
    userWaivers: userWaiversReducer,
    waivers: waiversSlice,
    farmerMarket: farmerMarketSlice,
    deliveryZone: deliveryZoneReducer,
    subscriptionPayment: subscriptionPaymentReducer,
    subscriptionCustomer: subscriptionCustomerReducer,
    userSubscription: userSubscriptionReducer,
    ticketTailor: ticketTailorReducer,
    checkout: checkoutReducer,
    anonymousFlow: anonymousFlowSlice,
    giftGiver: giftGiverReducer,
    giftRecipient: giftRecipientReducer,
    referralSlice: referralReducer,
  },
  // Keep the existing serializableCheck middleware configuration
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore specific paths known to contain non-serializable data like Dates/Timestamps
        ignoredActionPaths: [
          'payload.latestPayload', // Ignore the whole payload from dexie records in actions
          'payload.createdAt',
          'payload.updatedAt',
          'payload.lastUpdated',
          'payload.*.latestPayload', // For arrays of attempts
          'payload.*.createdAt',
          'payload.*.updatedAt',
          'payload.*.lastUpdated',
          'meta.arg', // Often contains complex objects in thunks
          'guests.event.start_date',
          'guests.event.end_date',
          'guests.event.created_at',
          'guests.event.updated_at',
          'guests.schedule.event_date',
          'guests.schedule.created_at',
          'guests.schedule.updated_at',
          'guests.order.created_at',
          'guests.order.updated_at',
          'guests.order.order_date',
          'guests.tickets.*.created_at',
          'guests.tickets.*.updated_at',
          'guests.tickets.*.checked_in_at',
          'guests.activeTickets.*.*.created_at', // Active tickets might be nested
          'guests.activeTickets.*.*.updated_at',
          'guests.activeTickets.*.*.checked_in_at',
          'guests.activeTickets.*.*.event_date', // Adding event_date
          'guests.activeTickets.*.*.start_date', // Adding start_date if it's a Date
          'guests.activeTickets.*.*.end_date', // Adding end_date if it's a Date
          'userWaivers.userWaivers.*.signed_at',
          'userWaivers.userWaivers.*.valid_until',
          'userWaivers.childWaivers.*.signed_at',
          'userWaivers.childWaivers.*.valid_until',
          'waivers.order.created_at', // From waiversSlice if applicable
          'waivers.order.updated_at',
          'waivers.order.order_date',
          'waivers.tickets.*.created_at',
          'waivers.tickets.*.updated_at',
          'waivers.tickets.*.checked_in_at',
        ],
        // Ignore specific paths in the state tree
        ignoredPaths: [
          'guests.event.start_date',
          'guests.event.end_date',
          'guests.event.created_at',
          'guests.event.updated_at',
          'guests.schedule.event_date',
          'guests.schedule.created_at',
          'guests.schedule.updated_at',
          'guests.order.created_at',
          'guests.order.updated_at',
          'guests.order.order_date',
          'guests.tickets', // Ignore the whole tickets array for simplicity
          'guests.activeTickets', // Ignore the whole activeTickets structure
          'guests.completedTickets', // Ignore the whole completedTickets array
          'userWaivers.userWaivers', // Ignore waiver arrays
          'userWaivers.childWaivers',
          'waivers.order', // Ignore order object in waiversSlice
          'waivers.tickets', // Ignore tickets array in waiversSlice
          'anonymousFlow.claimListAttempts', // Ignore list attempts which come from Dexie
          'anonymousFlow.currentAttempt', // Ignore current attempt which comes from Dexie
        ],
      },
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector = useSelector;

// Export the store as default
export default store;
