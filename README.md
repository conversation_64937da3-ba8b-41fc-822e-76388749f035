# My Very Mulberry

A Next.js-based platform for U-Pick Experience management.

## Features

- **Event Management**

  - Create and manage events with detailed information
  - Schedule multiple sessions for each event
  - Set pricing and capacity limits

- **Ticket System**

  - QR code-based ticket generation
  - Check-in/check-out functionality
  - Digital waiver integration

- **User Management**

  - User authentication with Firebase
  - Profile management
  - Role-based access control

- **Payment Processing**
  - Secure payment handling with Square
  - Transaction history
  - Refund management

## Tech Stack

- **Framework**: Next.js 15.1.0
- **Language**: TypeScript
- **Authentication**: Firebase Authentication
- **Database**: Firebase Firestore
- **Storage**: Firebase Storage
- **Payment Processing**: Square
- **Email Service**: SendGrid
- **Monitoring**: Sentry
- **State Management**: Redux Toolkit
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Code Quality**:
  - ESLint for code linting
  - Prettier for code formatting
  - Husky for git hooks
  - lint-staged for staged files linting

## Prerequisites

- Node.js 18.x or higher
- npm or yarn
- Firebase project
- Square account
- SendGrid account
- Sentry account

## Getting Started

1. **Clone the repository**

   ```bash
   git clone [repository-url]
   cd my-very-mulberry
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**

   1. Copy environment file:
      ```bash
      cp .env.example .env.local
      ```
   2. Set up Firebase App Hosting secrets:

      ```bash
      # Firebase Configuration
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_FIREBASE_API_KEY
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_FIREBASE_PROJECT_ID
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_FIREBASE_APP_ID

      # Firebase Admin
      firebase apphosting:secrets:grantaccess FIREBASE_PRIVATE_KEY
      firebase apphosting:secrets:grantaccess FIREBASE_CLIENT_EMAIL
      firebase apphosting:secrets:grantaccess FIREBASE_PRIVATE_BUCKET

      # Square Configuration
      firebase apphosting:secrets:grantaccess NEXT_SQUARE_ACCESS_TOKEN
      firebase apphosting:secrets:grantaccess NEXT_SQUARE_LOCATION_ID
      firebase apphosting:secrets:grantaccess NEXT_SQUARE_ENVIRONMENT
      firebase apphosting:secrets:grantaccess NEXT_ADULT_CATALOG_ID
      firebase apphosting:secrets:grantaccess NEXT_CHILD_CATALOG_ID
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_CHECKOUT_STORE_URL

      # SendGrid Configuration
      firebase apphosting:secrets:grantaccess SENDGRID_API_KEY
      firebase apphosting:secrets:grantaccess SENDGRID_FROM_EMAIL
      firebase apphosting:secrets:grantaccess SENDGRID_MAGIC_LINK_TEMPLATE_ID

      # Application Configuration
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_BASE_URL

      # Monitoring
      firebase apphosting:secrets:grantaccess SENTRY_AUTH_TOKEN

      # Square Subscription Configuration
      firebase apphosting:secrets:grantaccess NEXT_SQUARE_SUBSCRIPTION_PRODUCT_ID
      firebase apphosting:secrets:grantaccess NEXT_SQUARE_SUBSCRIPTION_PRODUCT_VARIATION_ID
      firebase apphosting:secrets:grantaccess NEXT_SQUARE_SUBSCRIPTION_PLAN_VARIATION_ID
      firebase apphosting:secrets:grantaccess NEXT_SQUARE_SUBSCRIPTION_DELIVERY_FEE_VARIATION_ID
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_SQUARE_LOCATION_ID
      firebase apphosting:secrets:grantaccess NEXT_PUBLIC_SQUARE_APP_ID

      # Ticket Tailor Configuration
      firebase apphosting:secrets:grantaccess TICKET_TAILOR_API_KEY
      ```

4. **Run the development server**

   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

## Project Structure

```
src/
├── app/              # Next.js app directory
├── components/       # Reusable components
├── features/         # Feature-based modules
├── lib/              # Utility functions and configurations
├── store/            # Redux store setup
└── types/            # TypeScript type definitions
```

## Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

## Deployment

The application is deployed using Firebase App Hosting with Cloud Run backend. Configuration can be found in `apphosting.yaml`.

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Run tests and ensure code quality
4. Submit a pull request

## License

This project is private and confidential.
