{"name": "very-mulberry", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev-nt": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.4", "@react-pdf/renderer": "^4.2.1", "@reduxjs/toolkit": "^2.5.1", "@sendgrid/mail": "^7.7.0", "@sentry/nextjs": "^9.12.0", "@sentry/replay": "^7.116.0", "@yudiel/react-qr-scanner": "^2.2.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.0.0", "date-fns-tz": "^3.2.0", "dexie": "^4.0.11", "dompurify": "^3.2.5", "embla-carousel-react": "^8.6.0", "firebase": "^11.2.0", "firebase-admin": "^13.1.0", "input-otp": "^1.4.2", "jsdom": "^26.0.0", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.10.55", "lucide-react": "^0.474.0", "next": "15.1.0", "next-themes": "^0.4.4", "qrcode": "^1.5.4", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-google-autocomplete": "^2.7.5", "react-hook-form": "^7.54.2", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-signature-canvas": "^1.0.7", "react-square-web-payments-sdk": "^3.2.4-beta.1", "sonner": "^1.7.4", "square": "^38.2.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jsdom": "^21.1.7", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-signature-canvas": "^1.0.7", "@typescript-eslint/eslint-plugin": "^8.23.0", "eslint": "^9", "eslint-config-next": "15.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.1.0", "husky": "^8.0.0", "lint-staged": "^15.4.3", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}